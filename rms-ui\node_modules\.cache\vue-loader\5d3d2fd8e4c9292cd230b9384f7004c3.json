{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=style&index=0&id=ab3cbd62&scoped=true&lang=css", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752933907771}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5ib3gtY2FyZCB7CiAgaGVpZ2h0OiA2MDBweDsKICBvdmVyZmxvdy15OiBhdXRvOwp9CgoudGV4dC1jZW50ZXIgewogIHRleHQtYWxpZ246IGNlbnRlcjsKfQoKLmNsZWFyZml4OmJlZm9yZSwKLmNsZWFyZml4OmFmdGVyIHsKICBkaXNwbGF5OiB0YWJsZTsKICBjb250ZW50OiAiIjsKfQoKLmNsZWFyZml4OmFmdGVyIHsKICBjbGVhcjogYm90aDsKfQoKLmVsLWRlc2NyaXB0aW9ucyB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmVsLXRhYmxlIHsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgpoNCB7CiAgbWFyZ2luOiAyMHB4IDAgMTBweCAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+iBA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/rms/prescription/review", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item label=\"科室\" prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"关键字\" prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"医生姓名或患者姓名\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 工具栏 -->\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"自动刷新\"\n          inactive-text=\"手动刷新\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <!-- 主要内容区域 -->\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 处方列表 -->\n      <el-col :span=\"9\">\n        <el-card class=\"list-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>待审核处方列表</span>\n            <span class=\"header-info\">（共 {{ total }} 条）</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"prescriptionList\"\n            @selection-change=\"handleSelectionChange\"\n            @row-click=\"handleRowClick\"\n            highlight-current-row\n            height=\"calc(100vh - 320px)\"\n            ref=\"prescriptionTable\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"100\" show-overflow-tooltip />\n            <el-table-column label=\"医生\" align=\"center\" prop=\"doctName\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"患者\" align=\"center\" prop=\"name\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"严重程度\" align=\"center\" width=\"90\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                  {{ getSeverityText(scope.row.level) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n            class=\"pagination-container\"\n          />\n        </el-card>\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"15\">\n        <el-card class=\"detail-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n            <span class=\"header-info\">（{{ currentPrescription.code }}）</span>\n          </div>\n\n          <div class=\"detail-content\" style=\"height: calc(100vh - 320px); overflow-y: auto;\">\n            <!-- 基本信息 - 紧凑布局 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">处方号：</span>{{ currentPrescription.presId }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"16\"><span class=\"label\">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 诊断信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">诊断信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 医生和患者信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">医生和患者信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">科室：</span>{{ currentPrescription.deptName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">医生：</span>{{ currentPrescription.doctName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">患者：</span>{{ currentPrescription.name }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"6\"><span class=\"label\">性别：</span>{{ currentPrescription.sex }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">年龄：</span>{{ calculateAge(currentPrescription.birth) }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">身高：</span>{{ currentPrescription.height || '未知' }}cm</el-col>\n                <el-col :span=\"6\"><span class=\"label\">体重：</span>{{ currentPrescription.weight || '未知' }}kg</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 药品明细 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">药品明细</h4>\n              <el-table :data=\"medicationList\" size=\"mini\" border max-height=\"200\">\n                <el-table-column label=\"药名\" prop=\"medName\" min-width=\"120\" show-overflow-tooltip />\n                <el-table-column label=\"组号\" prop=\"group\" width=\"50\" />\n                <el-table-column label=\"规格\" prop=\"spec\" width=\"80\" show-overflow-tooltip />\n                <el-table-column label=\"给药途径\" prop=\"administer\" width=\"70\" />\n                <el-table-column label=\"单次量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.dose }}{{ scope.row.doseUnit }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"频次\" prop=\"freq\" width=\"50\" />\n                <el-table-column label=\"天数\" prop=\"days\" width=\"50\" />\n                <el-table-column label=\"开药数量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.ordQty }}{{ scope.row.ordUom }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"金额\" prop=\"money\" width=\"60\" />\n                <el-table-column label=\"用药说明\" prop=\"yysm\" min-width=\"100\" show-overflow-tooltip />\n              </el-table>\n            </div>\n\n            <!-- 处方分析结果 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方分析结果</h4>\n              <el-tabs v-model=\"activeTab\" type=\"card\" class=\"compact-tabs\">\n                <el-tab-pane name=\"important\">\n                  <span slot=\"label\">\n                    重要问题 <el-badge :value=\"getAnalysisResultsByLevel('重要').length\" v-if=\"getAnalysisResultsByLevel('重要').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"general\">\n                  <span slot=\"label\">\n                    一般问题 <el-badge :value=\"getAnalysisResultsByLevel('一般').length\" v-if=\"getAnalysisResultsByLevel('一般').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"other\">\n                  <span slot=\"label\">\n                    其它问题 <el-badge :value=\"getAnalysisResultsByLevel('其它').length\" v-if=\"getAnalysisResultsByLevel('其它').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n              </el-tabs>\n            </div>\n\n            <!-- 历史审核记录 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">历史审核记录</h4>\n              <el-table :data=\"reviewHistory\" size=\"mini\" border max-height=\"120\">\n                <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"80\" />\n                <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"130\">\n                  <template slot-scope=\"scope\">\n                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"审核意见\" prop=\"text\" min-width=\"150\" show-overflow-tooltip />\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n        <el-card v-else class=\"detail-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 固定在底部的审核操作区域 -->\n    <div class=\"review-actions-fixed\">\n      <el-card class=\"actions-card\">\n        <el-row :gutter=\"15\" align=\"middle\" type=\"flex\">\n          <el-col :span=\"8\">\n            <el-select\n              v-model=\"selectedProblems\"\n              multiple\n              placeholder=\"选择问题类型\"\n              style=\"width: 100%\"\n              size=\"small\"\n              collapse-tags>\n              <el-option\n                v-for=\"problem in problemTypes\"\n                :key=\"problem.cfwtbh\"\n                :label=\"problem.cfwtname\"\n                :value=\"problem.cfwtname\">\n              </el-option>\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"danger\"\n              icon=\"el-icon-close\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleReject\"\n              v-hasPermi=\"['rms:prescription:review:reject']\"\n              style=\"width: 100%\">\n              审核打回\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-check\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleApprove\"\n              v-hasPermi=\"['rms:prescription:review:approve']\"\n              style=\"width: 100%\">\n              审核通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"8\" class=\"text-right\">\n            <span class=\"selection-info\">\n              已选择 <strong>{{ ids.length }}</strong> 条处方\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"clearSelection\"\n                v-if=\"ids.length > 0\">\n                清空选择\n              </el-button>\n            </span>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 选中的处方对象数组（用于状态持久化）\n      selectedPrescriptions: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n    this.loadSelectedState()\n  },\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n    }\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      this.loading = true\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n\n        // 恢复选择状态\n        this.$nextTick(() => {\n          this.restoreSelectedState()\n        })\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.code)\n      this.selectedPrescriptions = selection\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n\n      // 保存选择状态到本地存储\n      this.saveSelectedState()\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.box-card {\n  height: 600px;\n  overflow-y: auto;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-descriptions {\n  margin-bottom: 20px;\n}\n\n.el-table {\n  margin-bottom: 10px;\n}\n\nh4 {\n  margin: 20px 0 10px 0;\n  color: #303133;\n  font-weight: 500;\n}\n</style>"]}]}