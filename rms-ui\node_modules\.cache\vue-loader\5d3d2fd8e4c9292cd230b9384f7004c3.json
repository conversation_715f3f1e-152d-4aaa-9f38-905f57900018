{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=style&index=0&id=ab3cbd62&scoped=true&lang=css", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752931063897}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYm94LWNhcmQgewogIGhlaWdodDogNjAwcHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKfQoKLnRleHQtY2VudGVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi5jbGVhcmZpeDpiZWZvcmUsCi5jbGVhcmZpeDphZnRlciB7CiAgZGlzcGxheTogdGFibGU7CiAgY29udGVudDogIiI7Cn0KCi5jbGVhcmZpeDphZnRlciB7CiAgY2xlYXI6IGJvdGg7Cn0KCi5lbC1kZXNjcmlwdGlvbnMgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5lbC10YWJsZSB7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQoKaDQgewogIG1hcmdpbjogMjBweCAwIDEwcHggMDsKICBjb2xvcjogIzMwMzEzMzsKICBmb250LXdlaWdodDogNTAwOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4dA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/rms/prescription/review", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item label=\"科室\" prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"关键字\" prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"医生姓名或患者姓名\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 操作按钮 -->\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-select v-model=\"selectedProblems\" multiple placeholder=\"选择问题类型\" style=\"width: 300px\" size=\"mini\">\n          <el-option\n            v-for=\"problem in problemTypes\"\n            :key=\"problem.cfwtbh\"\n            :label=\"problem.cfwtname\"\n            :value=\"problem.cfwtname\">\n          </el-option>\n        </el-select>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-close\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleReject\"\n          v-hasPermi=\"['rms:prescription:review:reject']\"\n        >审核打回</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-check\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleApprove\"\n          v-hasPermi=\"['rms:prescription:review:approve']\"\n        >审核通过</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"自动刷新\"\n          inactive-text=\"手动刷新\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <!-- 处方列表 -->\n    <el-row :gutter=\"20\">\n      <el-col :span=\"12\">\n        <el-table\n          v-loading=\"loading\"\n          :data=\"prescriptionList\"\n          @selection-change=\"handleSelectionChange\"\n          @row-click=\"handleRowClick\"\n          highlight-current-row\n          height=\"600\">\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n          <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"120\" />\n          <el-table-column label=\"医生姓名\" align=\"center\" prop=\"doctName\" width=\"100\" />\n          <el-table-column label=\"患者姓名\" align=\"center\" prop=\"name\" width=\"100\" />\n          <el-table-column label=\"严重程度\" align=\"center\" width=\"100\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                {{ getSeverityText(scope.row.level) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <span>{{ parseTime(scope.row.presTime, '{y}-{m}-{d}') }}</span>\n            </template>\n          </el-table-column>\n        </el-table>\n\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n          </div>\n\n          <!-- 基本信息 -->\n          <el-descriptions title=\"处方信息\" :column=\"2\" size=\"small\" border>\n            <el-descriptions-item label=\"处方号\">{{ currentPrescription.presId }}</el-descriptions-item>\n            <el-descriptions-item label=\"金额\">{{ currentPrescription.money || '未知' }}</el-descriptions-item>\n            <el-descriptions-item label=\"就诊号\">{{ currentPrescription.treatCode }}</el-descriptions-item>\n            <el-descriptions-item label=\"就诊日期\">{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d}') }}</el-descriptions-item>\n            <el-descriptions-item label=\"处方说明\" :span=\"2\">{{ currentPrescription.presSm || '无' }}</el-descriptions-item>\n            <el-descriptions-item label=\"服用方法\" :span=\"2\">{{ currentPrescription.requir || '无' }}</el-descriptions-item>\n          </el-descriptions>\n\n          <el-descriptions title=\"诊断信息\" :column=\"2\" size=\"small\" border style=\"margin-top: 20px\">\n            <el-descriptions-item label=\"诊断信息\" :span=\"2\">{{ currentPrescription.diaInfo || '无' }}</el-descriptions-item>\n            <el-descriptions-item label=\"中医主病\">{{ currentPrescription.zyzb || '无' }}</el-descriptions-item>\n            <el-descriptions-item label=\"中医主症\">{{ currentPrescription.zyzz || '无' }}</el-descriptions-item>\n            <el-descriptions-item label=\"用药理由\" :span=\"2\">{{ currentPrescription.reason || '无' }}</el-descriptions-item>\n          </el-descriptions>\n\n          <el-descriptions title=\"医生信息\" :column=\"2\" size=\"small\" border style=\"margin-top: 20px\">\n            <el-descriptions-item label=\"科室\">{{ currentPrescription.deptName }}</el-descriptions-item>\n            <el-descriptions-item label=\"医生姓名\">{{ currentPrescription.doctName }}</el-descriptions-item>\n          </el-descriptions>\n\n          <el-descriptions title=\"患者信息\" :column=\"2\" size=\"small\" border style=\"margin-top: 20px\">\n            <el-descriptions-item label=\"患者姓名\">{{ currentPrescription.name }}</el-descriptions-item>\n            <el-descriptions-item label=\"出生日期\">{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-descriptions-item>\n            <el-descriptions-item label=\"性别\">{{ currentPrescription.sex }}</el-descriptions-item>\n            <el-descriptions-item label=\"年龄\">{{ calculateAge(currentPrescription.birth) }}</el-descriptions-item>\n            <el-descriptions-item label=\"身高\">{{ currentPrescription.height || '未知' }}cm</el-descriptions-item>\n            <el-descriptions-item label=\"体重\">{{ currentPrescription.weight || '未知' }}kg</el-descriptions-item>\n            <el-descriptions-item label=\"孕周\">{{ currentPrescription.pregnant || '无' }}</el-descriptions-item>\n            <el-descriptions-item label=\"过敏信息\" :span=\"2\">{{ currentPrescription.allInfo || '无' }}</el-descriptions-item>\n          </el-descriptions>\n\n          <!-- 药品明细 -->\n          <div style=\"margin-top: 20px\">\n            <h4>药品明细</h4>\n            <el-table :data=\"medicationList\" size=\"small\" border>\n              <el-table-column label=\"药名\" prop=\"medName\" />\n              <el-table-column label=\"组号\" prop=\"group\" width=\"60\" />\n              <el-table-column label=\"规格\" prop=\"spec\" width=\"100\" />\n              <el-table-column label=\"给药途径\" prop=\"administer\" width=\"80\" />\n              <el-table-column label=\"单次量\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.dose }}{{ scope.row.doseUnit }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"频次\" prop=\"freq\" width=\"60\" />\n              <el-table-column label=\"天数\" prop=\"days\" width=\"60\" />\n              <el-table-column label=\"开药数量\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.ordQty }}{{ scope.row.ordUom }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"金额\" prop=\"money\" width=\"80\" />\n              <el-table-column label=\"用药说明\" prop=\"yysm\" />\n            </el-table>\n          </div>\n\n          <!-- 处方分析结果 -->\n          <div style=\"margin-top: 20px\">\n            <h4>处方分析结果</h4>\n            <el-tabs v-model=\"activeTab\">\n              <el-tab-pane label=\"重要问题\" name=\"important\">\n                <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"small\">\n                  <el-table-column label=\"药物A\" prop=\"ywa\" width=\"120\" />\n                  <el-table-column label=\"药物B\" prop=\"ywb\" width=\"120\" />\n                  <el-table-column label=\"问题名称\" prop=\"wtname\" />\n                  <el-table-column label=\"标题\" prop=\"title\" />\n                  <el-table-column label=\"详情\" prop=\"detail\" show-overflow-tooltip />\n                </el-table>\n              </el-tab-pane>\n              <el-tab-pane label=\"一般问题\" name=\"general\">\n                <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"small\">\n                  <el-table-column label=\"药物A\" prop=\"ywa\" width=\"120\" />\n                  <el-table-column label=\"药物B\" prop=\"ywb\" width=\"120\" />\n                  <el-table-column label=\"问题名称\" prop=\"wtname\" />\n                  <el-table-column label=\"标题\" prop=\"title\" />\n                  <el-table-column label=\"详情\" prop=\"detail\" show-overflow-tooltip />\n                </el-table>\n              </el-tab-pane>\n              <el-tab-pane label=\"其它问题\" name=\"other\">\n                <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"small\">\n                  <el-table-column label=\"药物A\" prop=\"ywa\" width=\"120\" />\n                  <el-table-column label=\"药物B\" prop=\"ywb\" width=\"120\" />\n                  <el-table-column label=\"问题名称\" prop=\"wtname\" />\n                  <el-table-column label=\"标题\" prop=\"title\" />\n                  <el-table-column label=\"详情\" prop=\"detail\" show-overflow-tooltip />\n                </el-table>\n              </el-tab-pane>\n            </el-tabs>\n          </div>\n\n          <!-- 历史审核记录 -->\n          <div style=\"margin-top: 20px\">\n            <h4>历史审核记录</h4>\n            <el-table :data=\"reviewHistory\" size=\"small\" border>\n              <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"100\" />\n              <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"150\">\n                <template slot-scope=\"scope\">\n                  {{ parseTime(scope.row.createTime) }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"审核意见\" prop=\"text\" />\n            </el-table>\n          </div>\n        </el-card>\n        <el-card v-else class=\"box-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n  },\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n    }\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      this.loading = true\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.code)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.box-card {\n  height: 600px;\n  overflow-y: auto;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-descriptions {\n  margin-bottom: 20px;\n}\n\n.el-table {\n  margin-bottom: 10px;\n}\n\nh4 {\n  margin: 20px 0 10px 0;\n  color: #303133;\n  font-weight: 500;\n}\n</style>"]}]}