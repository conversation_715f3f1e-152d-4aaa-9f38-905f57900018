{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=style&index=0&id=ab3cbd62&scoped=true&lang=css", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752989385426}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLyog5Li76KaB5YaF5a655Yy65Z+fICovCi5hcHAtY29udGFpbmVyIHsKICAvKnBhZGRpbmctYm90dG9tOiA4MHB4OyAhKiDkuLrlm7rlrprnmoTlrqHmoLjljLrln5/nlZnlh7rnqbrpl7QgKiEqLwp9CgoubWFpbi1jb250ZW50IHsKICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAyMzBweCk7IC8qIOmhtumDqOWvvOiIqig1MHB4KSArIOmdouWMheWxkSg0MHB4KSArIOaQnOe0ouWMuuWfnyg4MHB4KSArIOW6lemDqOWuoeaguOWMuuWfnyg4MHB4KSAtIOS4gOS6m+i+uei3ne+8jOenu+mZpOW3peWFt+agj+mrmOW6piAqLwp9CgovKiDlpITmlrnliJfooajljaHniYcgKi8KLmxpc3QtY2FyZCB7CiAgaGVpZ2h0OiAxMDAlOwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKfQoKLmxpc3QtY2FyZCAuZWwtY2FyZF9fYm9keSB7CiAgcGFkZGluZzogMTBweDsKICBmbGV4OiAxOwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgoucGFnaW5hdGlvbi1jb250YWluZXIgewogIG1hcmdpbi10b3A6IDEwcHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGZsZXgtc2hyaW5rOiAwOwp9CgovKiDlpITmlrnor6bmg4XljaHniYcgKi8KLmRldGFpbC1jYXJkIHsKICBoZWlnaHQ6IDEwMCU7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwp9CgouZGV0YWlsLWNhcmQgLmVsLWNhcmRfX2JvZHkgewogIHBhZGRpbmc6IDEwcHg7CiAgZmxleDogMTsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgouZGV0YWlsLWNvbnRlbnQgewogIHBhZGRpbmctcmlnaHQ6IDVweDsKfQoKLyog57uf5LiA5L+h5oGv5Yy65Z+f5qC35byPICovCi51bmlmaWVkLWluZm8tc2VjdGlvbiB7CiAgYm9yZGVyOiAxcHggc29saWQgI2ViZWVmNTsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgcGFkZGluZzogMTJweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOwogIG1hcmdpbi1ib3R0b206IDEycHg7Cn0KCi5pbmZvLWdyb3VwIHsKICBtYXJnaW4tYm90dG9tOiAxMnB4Owp9CgouaW5mby1ncm91cDpsYXN0LWNoaWxkIHsKICBtYXJnaW4tYm90dG9tOiAwOwp9CgouZ3JvdXAtdGl0bGUgewogIG1hcmdpbjogMCAwIDhweCAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgZm9udC1zaXplOiAxM3B4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwogIHBhZGRpbmctYm90dG9tOiA0cHg7Cn0KCi5jb21wYWN0LXJvdyB7CiAgbWFyZ2luLWJvdHRvbTogNnB4OwogIGZvbnQtc2l6ZTogMTJweDsKICBsaW5lLWhlaWdodDogMS4zOwp9CgouY29tcGFjdC1yb3c6bGFzdC1jaGlsZCB7CiAgbWFyZ2luLWJvdHRvbTogMDsKfQoKLmxhYmVsIHsKICBmb250LXdlaWdodDogNTAwOwogIGNvbG9yOiAjNjA2MjY2OwogIG1hcmdpbi1yaWdodDogNHB4Owp9CgovKiDkv53nlZnljp/mnInkv6Hmga/ljLrln5/moLflvI/ku6Xlhbzlrrnlhbbku5bpg6jliIYgKi8KLmluZm8tc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMTJweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBwYWRkaW5nOiA4cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTsKfQoKLnNlY3Rpb24tdGl0bGUgewogIG1hcmdpbjogMCAwIDhweCAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgZm9udC1zaXplOiAxM3B4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwogIHBhZGRpbmctYm90dG9tOiA0cHg7Cn0KCi5pbmZvLXJvdyB7CiAgbWFyZ2luLWJvdHRvbTogNnB4OwogIGZvbnQtc2l6ZTogMTJweDsKICBsaW5lLWhlaWdodDogMS4zOwp9CgovKiDntKflh5HnmoTmoIfnrb7pobUgKi8KLmNvbXBhY3QtdGFicyAuZWwtdGFic19faGVhZGVyIHsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgouY29tcGFjdC10YWJzIC5lbC10YWItcGFuZSB7CiAgcGFkZGluZzogMDsKfQoKLyog5Zu65a6a5Zyo5bqV6YOo55qE5a6h5qC45pON5L2c5Yy65Z+fICovCi5yZXZpZXctYWN0aW9ucy1maXhlZCB7CiAgcG9zaXRpb246IGZpeGVkOwogIGJvdHRvbTogMDsKICBsZWZ0OiAyMDBweDsgLyog5L6n6L655qCP5a695bqmICovCiAgcmlnaHQ6IDA7CiAgei1pbmRleDogMTAwMDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gdG9wLCByZ2JhKDI1NSwyNTUsMjU1LDAuOTUpIDAlLCByZ2JhKDI1NSwyNTUsMjU1LDAuOSkgMTAwJSk7CiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDVweCk7CiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm94LXNoYWRvdzogMCAtMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgbWF4LXdpZHRoOiBjYWxjKDEwMHZ3IC0gMjAwcHgpOyAvKiDpmZDliLbmnIDlpKflrr3luqbvvIzlh4/ljrvkvqfovrnmoI/lrr3luqYgKi8KfQoKLyog5b2T5L6n6L655qCP5pS26LW35pe255qE6YCC6YWNICovCi5oaWRlU2lkZWJhciAucmV2aWV3LWFjdGlvbnMtZml4ZWQgewogIGxlZnQ6IDU0cHg7IC8qIOaUtui1t+WQjueahOS+p+i+ueagj+WuveW6piAqLwogIG1heC13aWR0aDogY2FsYygxMDB2dyAtIDU0cHgpOwp9CgovKiDnp7vliqjnq6/pgILphY0gKi8KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7CiAgLnJldmlldy1hY3Rpb25zLWZpeGVkIHsKICAgIGxlZnQ6IDA7CiAgICByaWdodDogMDsKICAgIG1heC13aWR0aDogMTAwdnc7CiAgfQp9CgouYWN0aW9ucy1jYXJkIHsKICBtYXJnaW46IDA7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDA7CiAgYm94LXNoYWRvdzogbm9uZTsKfQoKLmFjdGlvbnMtY2FyZCAuZWwtY2FyZF9fYm9keSB7CiAgcGFkZGluZzogMTVweCAyMHB4Owp9Cgouc2VsZWN0aW9uLWluZm8gewogIGNvbG9yOiAjNjA2MjY2OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLnNlbGVjdGlvbi1pbmZvIHN0cm9uZyB7CiAgY29sb3I6ICM0MDllZmY7Cn0KCi8qIOWktOmDqOS/oeaBr+agt+W8jyAqLwouaGVhZGVyLWluZm8gewogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtc2l6ZTogMTJweDsKICBtYXJnaW4tbGVmdDogMTBweDsKfQoKLyog5ZON5bqU5byP6K6+6K6hICovCkBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHsKICAubWFpbi1jb250ZW50IC5lbC1jb2w6Zmlyc3QtY2hpbGQgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICB9CgogIC5pbmZvLXJvdyAuZWwtY29sIHsKICAgIG1hcmdpbi1ib3R0b206IDVweDsKICB9CgogIC8qIOiwg+aVtOWwj+Wxj+W5leS4i+eahOmrmOW6puiuoeeulyAqLwogIC5tYWluLWNvbnRlbnQgewogICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMjgwcHgpOwogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7CiAgLm1haW4tY29udGVudCB7CiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAzMjBweCk7CiAgfQoKICAucmV2aWV3LWFjdGlvbnMtZml4ZWQgLmVsLXJvdyAuZWwtY29sIHsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgfQoKICAuYWN0aW9ucy1jYXJkIC5lbC1jYXJkX19ib2R5IHsKICAgIHBhZGRpbmc6IDEwcHggMTVweDsKICB9Cn0KCi8qIOi2heWkp+Wxj+W5leS8mOWMliAqLwpAbWVkaWEgKG1pbi13aWR0aDogMTkyMHB4KSB7CiAgLm1haW4tY29udGVudCB7CiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAyMjBweCk7CiAgfQp9CgovKiDpgJrnlKjmoLflvI8gKi8KLnRleHQtY2VudGVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi50ZXh0LXJpZ2h0IHsKICB0ZXh0LWFsaWduOiByaWdodDsKfQoKLmNsZWFyZml4OmJlZm9yZSwKLmNsZWFyZml4OmFmdGVyIHsKICBkaXNwbGF5OiB0YWJsZTsKICBjb250ZW50OiAiIjsKfQoKLmNsZWFyZml4OmFmdGVyIHsKICBjbGVhcjogYm90aDsKfQoKLmVsLXRhYmxlIHsKICBtYXJnaW4tYm90dG9tOiA1cHg7Cn0KCi8qIOihqOagvOS8mOWMliAqLwouZWwtdGFibGUgLmNlbGwgewogIHBhZGRpbmc6IDAgNXB4Owp9CgouZWwtdGFibGUtLW1pbmkgdGQgewogIHBhZGRpbmc6IDRweCAwOwp9CgovKiDlvr3nq6DmoLflvI8gKi8KLmVsLWJhZGdlIHsKICBtYXJnaW4tbGVmdDogNXB4Owp9CgovKiDmu5rliqjmnaHmoLflvI8gKi8KOjotd2Via2l0LXNjcm9sbGJhciB7CiAgd2lkdGg6IDZweDsKICBoZWlnaHQ6IDZweDsKfQoKOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7CiAgYmFja2dyb3VuZDogI2YxZjFmMTsKICBib3JkZXItcmFkaXVzOiAzcHg7Cn0KCjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgewogIGJhY2tncm91bmQ6ICNjMWMxYzE7CiAgYm9yZGVyLXJhZGl1czogM3B4Owp9Cgo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjYThhOGE4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi+BA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/rms/prescription/review", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"搜索关键字\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n      <!-- 自动刷新开关移动到筛选条件区域右侧 -->\n      <el-form-item style=\"float: right; margin-right: 0;\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"开启审方\"\n          inactive-text=\"停止审方\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-form-item>\n    </el-form>\n\n    <!-- 主要内容区域 -->\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 处方列表 -->\n      <el-col :span=\"9\" style=\"height: 100%\">\n        <el-card class=\"list-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>待审核处方列表</span>\n            <span class=\"header-info\">（共 {{ total }} 条）</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"prescriptionList\"\n            @selection-change=\"handleSelectionChange\"\n            @row-click=\"handleRowClick\"\n            highlight-current-row\n            :height=\"tableHeight\"\n            ref=\"prescriptionTable\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"100\" show-overflow-tooltip />\n            <el-table-column label=\"医生\" align=\"center\" prop=\"doctName\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"患者\" align=\"center\" prop=\"name\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"严重程度\" align=\"center\" width=\"90\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                  {{ getSeverityText(scope.row.level) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n            class=\"pagination-container\"\n          />\n        </el-card>\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"15\" style=\"height: 100%\">\n        <el-card class=\"detail-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n            <span class=\"header-info\">（{{ currentPrescription.code }}）</span>\n          </div>\n\n          <div class=\"detail-content\" :style=\"{ height: detailHeight, overflowY: 'auto' }\">\n            <!-- 合并的基本信息卡片 -->\n            <div class=\"unified-info-section\">\n              <!-- 处方基本信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">处方信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">处方号：</span>{{ currentPrescription.presId }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>\n                </el-row>\n              </div>\n\n              <!-- 医生和患者信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">医生和患者信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">科室：</span>{{ currentPrescription.deptName }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">医生：</span>{{ currentPrescription.doctName }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">患者：</span>{{ currentPrescription.name }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">性别/年龄：</span>{{ currentPrescription.sex }} / {{ calculateAge(currentPrescription.birth) }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">身高/体重：</span>{{ currentPrescription.height || '未知' }}cm / {{ currentPrescription.weight || '未知' }}kg</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"24\"><span class=\"label\">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>\n                </el-row>\n              </div>\n\n              <!-- 诊断信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">诊断信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>\n                </el-row>\n              </div>\n            </div>\n\n            <!-- 药品明细 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">药品明细</h4>\n              <el-table :data=\"medicationList\" size=\"mini\" border max-height=\"200\">\n                <el-table-column label=\"药名\" align=\"center\" prop=\"medName\" min-width=\"120\" show-overflow-tooltip />\n                <el-table-column label=\"组号\" align=\"center\" prop=\"group\" width=\"50\" />\n                <el-table-column label=\"规格\" align=\"center\" prop=\"spec\" width=\"80\" show-overflow-tooltip />\n                <el-table-column label=\"给药途径\" align=\"center\" prop=\"administer\" width=\"80\" />\n                <el-table-column label=\"单次量\" align=\"center\" width=\"100\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.dose) || 0).toFixed(2) }}{{ scope.row.doseUnit }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"频次\" align=\"center\" prop=\"freq\" width=\"50\" />\n                <el-table-column label=\"天数\" align=\"center\" prop=\"days\" width=\"50\" />\n                <el-table-column label=\"开药数量\" align=\"center\" width=\"80\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.ordQty) || 0).toFixed(2) }}{{ scope.row.ordUom }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"金额\" align=\"center\" prop=\"money\" width=\"80\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.money) || 0).toFixed(2) }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"用药说明\" align=\"center\" prop=\"yysm\" min-width=\"100\" show-overflow-tooltip />\n              </el-table>\n            </div>\n\n            <!-- 处方分析结果 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方分析结果</h4>\n              <el-tabs v-model=\"activeTab\" type=\"card\" class=\"compact-tabs\">\n                <el-tab-pane name=\"important\">\n                  <span slot=\"label\">\n                    重要问题 <el-badge :value=\"getAnalysisResultsByLevel('重要').length\" v-if=\"getAnalysisResultsByLevel('重要').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"general\">\n                  <span slot=\"label\">\n                    一般问题 <el-badge :value=\"getAnalysisResultsByLevel('一般').length\" v-if=\"getAnalysisResultsByLevel('一般').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"other\">\n                  <span slot=\"label\">\n                    其它问题 <el-badge :value=\"getAnalysisResultsByLevel('其它').length\" v-if=\"getAnalysisResultsByLevel('其它').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n              </el-tabs>\n            </div>\n\n            <!-- 历史审核记录 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">历史审核记录</h4>\n              <el-table :data=\"reviewHistory\" size=\"mini\" border max-height=\"120\">\n                <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"80\" />\n                <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"130\">\n                  <template slot-scope=\"scope\">\n                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"审核意见\" prop=\"text\" min-width=\"150\" show-overflow-tooltip />\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n        <el-card v-else class=\"detail-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 固定在底部的审核操作区域 -->\n    <div class=\"review-actions-fixed\">\n      <el-card class=\"actions-card\">\n        <el-row :gutter=\"15\" align=\"middle\" type=\"flex\">\n          <el-col :span=\"8\">\n            <el-select\n              v-model=\"selectedProblems\"\n              multiple\n              placeholder=\"选择问题类型\"\n              style=\"width: 100%\"\n              size=\"small\"\n              collapse-tags>\n              <el-option\n                v-for=\"problem in problemTypes\"\n                :key=\"problem.cfwtbh\"\n                :label=\"problem.cfwtbh + ' ' +problem.cfwtname\"\n                :value=\"problem.cfwtname\">\n              </el-option>\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"danger\"\n              icon=\"el-icon-close\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleReject\"\n              v-hasPermi=\"['rms:prescription:review:reject']\"\n              style=\"width: 100%\">\n              审核打回\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-check\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleApprove\"\n              v-hasPermi=\"['rms:prescription:review:approve']\"\n              style=\"width: 100%\">\n              审核通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"8\" class=\"text-right\">\n            <span class=\"selection-info\">\n              已选择 <strong>{{ ids.length }}</strong> 条处方\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"clearSelection\"\n                v-if=\"ids.length > 0\">\n                清空选择\n              </el-button>\n            </span>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 选中的处方对象数组（用于状态持久化）\n      selectedPrescriptions: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 是否由自动刷新触发的标记\n      isAutoRefreshTriggered: false,\n      // 页面不可见前是否开启了自动刷新\n      wasAutoRefreshActive: false,\n      // 是否正在恢复选择状态（用于阻止意外的选择变化事件）\n      isRestoringSelection: false,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  computed: {\n    /** 计算表格高度 */\n    tableHeight() {\n      // 100vh - 顶部导航(50px) - 面包屑(40px) - 搜索区域(80px) - 卡片头部(60px) - 分页区域(50px) - 底部审核区域(80px) - 边距(40px)\n      return 'calc(100vh - 350px)'\n    },\n    /** 计算详情区域高度 */\n    detailHeight() {\n      // 与表格高度保持一致，减去卡片头部\n      return 'calc(100vh - 340px)'\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n    this.loadSelectedState()\n  },\n  mounted() {\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize)\n    // 监听侧边栏状态变化\n    this.checkSidebarStatus()\n    // 监听页面可见性变化\n    document.addEventListener('visibilitychange', this.handleVisibilityChange)\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      console.log('🔄 [数据加载] 开始加载处方列表', {\n        是否自动刷新: this.autoRefresh,\n        当前页码: this.queryParams.pageNum,\n        页面大小: this.queryParams.pageSize,\n        加载时间: new Date().toLocaleTimeString(),\n        当前选择状态: this.ids\n      })\n\n      // 在数据加载前保存当前的选择状态\n      const savedIds = [...this.ids]\n      console.log('💾 [状态备份] 备份当前选择状态', { 备份的ids: savedIds })\n\n      this.loading = true\n      // 设置恢复状态标记，防止表格重新渲染时清空选择\n      this.isRestoringSelection = true\n\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        console.log('📊 [数据加载] 处方列表加载完成', {\n          数据条数: response.rows.length,\n          总数: response.total,\n          处方编码列表: response.rows.map(item => item.code)\n        })\n\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n\n        // 恢复备份的选择状态\n        this.ids = savedIds\n\n        console.log('🔄 [状态恢复] 准备恢复选择状态', {\n          备份的ids: savedIds,\n          当前ids: this.ids,\n          ids数量: this.ids.length,\n          表格ref存在: !!this.$refs.prescriptionTable,\n          是否自动刷新触发: this.isAutoRefreshTriggered\n        })\n\n        // 根据是否自动刷新使用不同的恢复策略\n        if (this.isAutoRefreshTriggered) {\n          console.log('🔄 [自动刷新] 使用自动刷新专用恢复机制')\n          this.handleAutoRefreshRestore()\n          this.isAutoRefreshTriggered = false // 重置标记\n        } else {\n          console.log('👆 [手动操作] 使用常规恢复机制')\n          // 恢复选择状态 - 确保DOM完全更新后再恢复\n          this.$nextTick(() => {\n            console.log('⏰ [延迟恢复] $nextTick执行，准备延迟恢复状态')\n            // 延迟一小段时间确保表格完全渲染\n            setTimeout(() => {\n              console.log('🎯 [开始恢复] 开始执行状态恢复')\n              this.restoreSelectedState()\n            }, 200) // 增加延迟时间到200ms\n          })\n        }\n      }).catch(error => {\n        this.loading = false\n        this.isRestoringSelection = false // 出错时也要重置标记\n        console.error('❌ [加载失败] 获取处方列表失败:', error)\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      console.log('🔄 [选择变化] 触发选择变化事件', {\n        选中数量: selection.length,\n        选中处方: selection.map(item => ({ code: item.code, name: item.name })),\n        触发时间: new Date().toLocaleTimeString(),\n        正在恢复状态: this.isRestoringSelection\n      })\n\n      // 如果正在恢复选择状态，忽略这次选择变化事件\n      if (this.isRestoringSelection) {\n        console.log('🚫 [忽略事件] 正在恢复选择状态，忽略此次选择变化事件')\n        return\n      }\n\n      this.ids = selection.map(item => item.code)\n      this.selectedPrescriptions = selection\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n      console.log('multiple', this.multiple)\n      console.log('selection.length', selection.length)\n\n      console.log('💾 [状态更新] 更新组件状态', {\n        ids: this.ids,\n        single: this.single,\n        multiple: this.multiple\n      })\n\n      // 保存选择状态到本地存储\n      this.saveSelectedState()\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      console.log('🔄 [自动刷新] 切换自动刷新状态', {\n        开启: value,\n        刷新间隔: this.refreshInterval,\n        当前选择数量: this.ids.length\n      })\n\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          console.log('⏰ [自动刷新] 自动刷新定时器触发', {\n            触发时间: new Date().toLocaleTimeString(),\n            当前选择状态: this.ids\n          })\n\n          // 标记这是自动刷新触发的数据加载\n          this.isAutoRefreshTriggered = true\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    },\n\n    /** 保存选择状态到本地存储 */\n    saveSelectedState() {\n      const selectedCodes = this.ids\n      console.log('💾 [保存状态] 保存选择状态到localStorage', {\n        选中的处方编码: selectedCodes,\n        数量: selectedCodes.length,\n        保存时间: new Date().toLocaleTimeString()\n      })\n\n      localStorage.setItem('prescription_review_selected', JSON.stringify(selectedCodes))\n\n      // 验证保存是否成功\n      const saved = localStorage.getItem('prescription_review_selected')\n      console.log('✅ [保存验证] localStorage保存结果', {\n        保存的数据: saved,\n        解析后: JSON.parse(saved || '[]')\n      })\n    },\n\n    /** 从本地存储加载选择状态 */\n    loadSelectedState() {\n      console.log('📖 [加载状态] 开始从localStorage加载选择状态')\n\n      try {\n        const savedSelection = localStorage.getItem('prescription_review_selected')\n        console.log('📖 [加载状态] localStorage中的数据', {\n          原始数据: savedSelection,\n          数据类型: typeof savedSelection\n        })\n\n        if (savedSelection) {\n          const parsedIds = JSON.parse(savedSelection)\n          this.ids = parsedIds\n          this.multiple = this.ids.length === 0\n          this.single = this.ids.length !== 1\n\n          console.log('✅ [加载成功] 状态加载完成', {\n            加载的ids: this.ids,\n            数量: this.ids.length,\n            multiple: this.multiple,\n            single: this.single\n          })\n        } else {\n          console.log('ℹ️ [加载状态] localStorage中没有保存的选择状态')\n          this.ids = []\n        }\n      } catch (error) {\n        console.error('❌ [加载失败] 加载选择状态失败:', error)\n        this.ids = []\n      }\n    },\n\n    /** 恢复表格选择状态 */\n    restoreSelectedState() {\n      console.log('🎯 [状态恢复] 开始恢复表格选择状态', {\n        需要恢复的ids: this.ids,\n        ids数量: this.ids.length,\n        表格ref存在: !!this.$refs.prescriptionTable,\n        处方列表长度: this.prescriptionList.length,\n        处方列表编码: this.prescriptionList.map(item => item.code)\n      })\n\n      // 检查前置条件\n      if (this.ids.length === 0) {\n        console.log('ℹ️ [状态恢复] 没有需要恢复的选择状态')\n        return\n      }\n\n      if (!this.$refs.prescriptionTable) {\n        console.error('❌ [状态恢复] 表格ref不存在')\n        return\n      }\n\n      if (this.prescriptionList.length === 0) {\n        console.log('ℹ️ [状态恢复] 处方列表为空，无法恢复状态')\n        return\n      }\n\n      try {\n        // 清除当前选择\n        console.log('🧹 [清除选择] 清除表格当前选择状态')\n        this.$refs.prescriptionTable.clearSelection()\n\n        // 恢复选择状态\n        let restoredCount = 0\n        let matchedRows = []\n        let unmatchedIds = []\n\n        this.prescriptionList.forEach(row => {\n          if (this.ids.includes(row.code)) {\n            console.log(`✅ [匹配成功] 找到匹配的处方: ${row.code} - ${row.name}`)\n            this.$refs.prescriptionTable.toggleRowSelection(row, true)\n            restoredCount++\n            matchedRows.push({ code: row.code, name: row.name })\n          }\n        })\n\n        // 检查未匹配的ID\n        this.ids.forEach(id => {\n          if (!this.prescriptionList.find(row => row.code === id)) {\n            unmatchedIds.push(id)\n          }\n        })\n\n        // 更新选择状态统计\n        this.multiple = this.ids.length === 0\n        this.single = this.ids.length !== 1\n\n        console.log('📊 [恢复结果] 状态恢复完成', {\n          恢复成功数量: restoredCount,\n          匹配的处方: matchedRows,\n          未匹配的ID: unmatchedIds,\n          最终状态: {\n            multiple: this.multiple,\n            single: this.single,\n            ids: this.ids\n          }\n        })\n\n        // 验证恢复结果\n        this.$nextTick(() => {\n          const selectedRows = this.$refs.prescriptionTable.selection || []\n          console.log('🔍 [恢复验证] 验证表格选择状态', {\n            表格选中行数: selectedRows.length,\n            表格选中编码: selectedRows.map(row => row.code),\n            期望选中数量: this.ids.length,\n            状态一致: selectedRows.length === restoredCount\n          })\n\n          if (selectedRows.length !== restoredCount) {\n            console.warn('⚠️ [状态不一致] 表格选择状态与预期不符，尝试重新恢复')\n            // 如果状态不一致，再次尝试恢复\n            setTimeout(() => {\n              this.restoreSelectedStateRetry()\n            }, 100)\n          } else {\n            // 恢复成功，重置恢复状态标记\n            console.log('✅ [恢复完成] 状态恢复成功，重置恢复标记')\n            this.isRestoringSelection = false\n          }\n        })\n\n      } catch (error) {\n        console.error('❌ [恢复失败] 恢复选择状态失败:', error)\n      }\n    },\n\n    /** 清空选择 */\n    clearSelection() {\n      // 设置恢复标记，防止clearSelection触发选择变化事件\n      this.isRestoringSelection = true\n\n      this.ids = []\n      this.selectedPrescriptions = []\n      this.multiple = true\n      this.single = true\n\n      // 清除表格选择\n      if (this.$refs.prescriptionTable) {\n        this.$refs.prescriptionTable.clearSelection()\n      }\n\n      // 清除本地存储\n      localStorage.removeItem('prescription_review_selected')\n\n      console.log('🧹 [清空选择] 选择状态已清空', {\n        清空时间: new Date().toLocaleTimeString()\n      })\n\n      // 重置恢复标记\n      setTimeout(() => {\n        this.isRestoringSelection = false\n      }, 100)\n    },\n\n    /** 重试恢复选择状态 */\n    restoreSelectedStateRetry() {\n      console.log('🔄 [重试恢复] 开始重试恢复选择状态')\n\n      if (!this.$refs.prescriptionTable) {\n        console.error('❌ [重试失败] 表格ref仍然不存在')\n        this.isRestoringSelection = false\n        return\n      }\n\n      // 强制清除选择\n      this.$refs.prescriptionTable.clearSelection()\n\n      // 重新恢复\n      let retryCount = 0\n      this.prescriptionList.forEach(row => {\n        if (this.ids.includes(row.code)) {\n          this.$refs.prescriptionTable.toggleRowSelection(row, true)\n          retryCount++\n          console.log(`🔄 [重试恢复] 重新选择处方: ${row.code}`)\n        }\n      })\n\n      console.log(`✅ [重试完成] 重试恢复了 ${retryCount} 个处方的选择状态`)\n\n      // 重试完成后重置标记\n      this.isRestoringSelection = false\n    },\n\n    /** 自动刷新时的特殊处理 */\n    handleAutoRefreshRestore() {\n      console.log('🔄 [自动刷新] 自动刷新触发，特殊处理选择状态恢复')\n\n      // 在自动刷新时，给更多时间让表格完全渲染\n      this.$nextTick(() => {\n        setTimeout(() => {\n          console.log('🎯 [自动刷新恢复] 开始自动刷新后的状态恢复')\n          this.restoreSelectedState()\n\n          // 额外验证\n          setTimeout(() => {\n            const selectedRows = this.$refs.prescriptionTable?.selection || []\n            if (selectedRows.length === 0 && this.ids.length > 0) {\n              console.warn('⚠️ [自动刷新] 第一次恢复失败，进行第二次尝试')\n              this.restoreSelectedStateRetry()\n            } else {\n              // 自动刷新恢复成功，重置标记\n              console.log('✅ [自动刷新] 自动刷新恢复成功，重置恢复标记')\n              this.isRestoringSelection = false\n            }\n          }, 300)\n        }, 300) // 自动刷新时使用更长的延迟\n      })\n    },\n\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 窗口大小变化时，强制重新计算表格高度\n      this.$nextTick(() => {\n        if (this.$refs.prescriptionTable) {\n          this.$refs.prescriptionTable.doLayout()\n        }\n      })\n    },\n\n    /** 检查侧边栏状态 */\n    checkSidebarStatus() {\n      // 检查body是否有hideSidebar类\n      const body = document.body\n      if (body.classList.contains('hideSidebar')) {\n        // 侧边栏已收起\n        this.updateFixedAreaPosition(true)\n      } else {\n        // 侧边栏展开\n        this.updateFixedAreaPosition(false)\n      }\n\n      // 监听侧边栏状态变化\n      const observer = new MutationObserver((mutations) => {\n        mutations.forEach((mutation) => {\n          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {\n            const isHidden = body.classList.contains('hideSidebar')\n            this.updateFixedAreaPosition(isHidden)\n          }\n        })\n      })\n\n      observer.observe(body, {\n        attributes: true,\n        attributeFilter: ['class']\n      })\n\n      // 保存observer引用以便清理\n      this.sidebarObserver = observer\n    },\n\n    /** 更新固定区域位置 */\n    updateFixedAreaPosition(isHidden) {\n      const fixedArea = document.querySelector('.review-actions-fixed')\n      if (fixedArea) {\n        if (isHidden) {\n          fixedArea.style.left = '54px'\n          fixedArea.style.maxWidth = 'calc(100vw - 54px)'\n        } else {\n          fixedArea.style.left = '200px'\n          fixedArea.style.maxWidth = 'calc(100vw - 200px)'\n        }\n      }\n    },\n\n    /** 处理页面可见性变化 */\n    handleVisibilityChange() {\n      if (document.hidden) {\n        console.log('👁️ [页面状态] 页面变为不可见，暂停自动刷新')\n        // 页面不可见时暂停自动刷新\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n          this.wasAutoRefreshActive = this.autoRefresh\n        }\n      } else {\n        console.log('👁️ [页面状态] 页面变为可见，恢复自动刷新')\n        // 页面可见时恢复自动刷新\n        if (this.wasAutoRefreshActive && this.autoRefresh) {\n          this.toggleAutoRefresh(true)\n        }\n\n        // 页面重新可见时，如果有选择状态需要恢复，立即恢复\n        if (this.ids.length > 0) {\n          console.log('🔄 [页面可见] 页面重新可见，检查选择状态')\n          setTimeout(() => {\n            this.restoreSelectedState()\n          }, 100)\n        }\n      }\n    }\n  },\n  beforeDestroy() {\n    console.log('🧹 [组件销毁] 开始清理组件资源')\n\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n      console.log('🧹 [组件销毁] 清理自动刷新定时器')\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('resize', this.handleResize)\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange)\n    console.log('🧹 [组件销毁] 清理事件监听器')\n\n    // 清理侧边栏观察器\n    if (this.sidebarObserver) {\n      this.sidebarObserver.disconnect()\n      console.log('🧹 [组件销毁] 清理侧边栏观察器')\n    }\n\n    console.log('✅ [组件销毁] 组件资源清理完成')\n  }\n}\n</script>\n\n<style scoped>\n/* 主要内容区域 */\n.app-container {\n  /*padding-bottom: 80px; !* 为固定的审核区域留出空间 *!*/\n}\n\n.main-content {\n  height: calc(100vh - 230px); /* 顶部导航(50px) + 面包屑(40px) + 搜索区域(80px) + 底部审核区域(80px) - 一些边距，移除工具栏高度 */\n}\n\n/* 处方列表卡片 */\n.list-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.list-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.pagination-container {\n  margin-top: 10px;\n  text-align: center;\n  flex-shrink: 0;\n}\n\n/* 处方详情卡片 */\n.detail-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  overflow: hidden;\n}\n\n.detail-content {\n  padding-right: 5px;\n}\n\n/* 统一信息区域样式 */\n.unified-info-section {\n  border: 1px solid #ebeef5;\n  border-radius: 6px;\n  padding: 12px;\n  background-color: #fafafa;\n  margin-bottom: 12px;\n}\n\n.info-group {\n  margin-bottom: 12px;\n}\n\n.info-group:last-child {\n  margin-bottom: 0;\n}\n\n.group-title {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 13px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 4px;\n}\n\n.compact-row {\n  margin-bottom: 6px;\n  font-size: 12px;\n  line-height: 1.3;\n}\n\n.compact-row:last-child {\n  margin-bottom: 0;\n}\n\n.label {\n  font-weight: 500;\n  color: #606266;\n  margin-right: 4px;\n}\n\n/* 保留原有信息区域样式以兼容其他部分 */\n.info-section {\n  margin-bottom: 12px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 8px;\n  background-color: #fafafa;\n}\n\n.section-title {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 13px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 4px;\n}\n\n.info-row {\n  margin-bottom: 6px;\n  font-size: 12px;\n  line-height: 1.3;\n}\n\n/* 紧凑的标签页 */\n.compact-tabs .el-tabs__header {\n  margin-bottom: 10px;\n}\n\n.compact-tabs .el-tab-pane {\n  padding: 0;\n}\n\n/* 固定在底部的审核操作区域 */\n.review-actions-fixed {\n  position: fixed;\n  bottom: 0;\n  left: 200px; /* 侧边栏宽度 */\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(to top, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);\n  backdrop-filter: blur(5px);\n  border-top: 1px solid #e4e7ed;\n  box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.1);\n  max-width: calc(100vw - 200px); /* 限制最大宽度，减去侧边栏宽度 */\n}\n\n/* 当侧边栏收起时的适配 */\n.hideSidebar .review-actions-fixed {\n  left: 54px; /* 收起后的侧边栏宽度 */\n  max-width: calc(100vw - 54px);\n}\n\n/* 移动端适配 */\n@media (max-width: 768px) {\n  .review-actions-fixed {\n    left: 0;\n    right: 0;\n    max-width: 100vw;\n  }\n}\n\n.actions-card {\n  margin: 0;\n  border: none;\n  border-radius: 0;\n  box-shadow: none;\n}\n\n.actions-card .el-card__body {\n  padding: 15px 20px;\n}\n\n.selection-info {\n  color: #606266;\n  font-size: 14px;\n}\n\n.selection-info strong {\n  color: #409eff;\n}\n\n/* 头部信息样式 */\n.header-info {\n  color: #909399;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .main-content .el-col:first-child {\n    margin-bottom: 20px;\n  }\n\n  .info-row .el-col {\n    margin-bottom: 5px;\n  }\n\n  /* 调整小屏幕下的高度计算 */\n  .main-content {\n    height: calc(100vh - 280px);\n  }\n}\n\n@media (max-width: 768px) {\n  .main-content {\n    height: calc(100vh - 320px);\n  }\n\n  .review-actions-fixed .el-row .el-col {\n    margin-bottom: 10px;\n  }\n\n  .actions-card .el-card__body {\n    padding: 10px 15px;\n  }\n}\n\n/* 超大屏幕优化 */\n@media (min-width: 1920px) {\n  .main-content {\n    height: calc(100vh - 220px);\n  }\n}\n\n/* 通用样式 */\n.text-center {\n  text-align: center;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-table {\n  margin-bottom: 5px;\n}\n\n/* 表格优化 */\n.el-table .cell {\n  padding: 0 5px;\n}\n\n.el-table--mini td {\n  padding: 4px 0;\n}\n\n/* 徽章样式 */\n.el-badge {\n  margin-left: 5px;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"]}]}