{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752980811737}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\babel.config.js", "mtime": 1748394285000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_prescriptionreview", "require", "name", "data", "loading", "ids", "selectedPrescriptions", "single", "multiple", "showSearch", "total", "prescriptionList", "currentPrescription", "medicationList", "analysisResults", "reviewHistory", "departmentList", "problemTypes", "selectedProblems", "autoRefresh", "refreshTimer", "refreshInterval", "activeTab", "queryParams", "pageNum", "pageSize", "deptCode", "prescriptionType", "keyword", "computed", "tableHeight", "detailHeight", "created", "getList", "getDepartmentList", "getProblemTypeList", "getRefreshConfig", "loadSelectedState", "mounted", "window", "addEventListener", "handleResize", "checkSidebarStatus", "methods", "_this", "params", "_objectSpread2", "default", "doctName", "getPendingPrescriptions", "then", "response", "rows", "$nextTick", "setTimeout", "restoreSelectedState", "catch", "error", "console", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "code", "length", "saveSelectedState", "handleRowClick", "row", "getPrescriptionDetailData", "_this2", "getPrescriptionDetail", "prescription", "medications", "_this3", "getDepartments", "_this4", "getProblemTypes", "_this5", "getRefreshTime", "toggleAutoRefresh", "value", "_this6", "setInterval", "clearInterval", "handleApprove", "_this7", "$modal", "msgError", "confirm", "approvePrescriptions", "clearSelection", "msgSuccess", "handleReject", "_this8", "codes", "problemNames", "rejectPrescriptions", "getAnalysisResultsByLevel", "level", "filter", "wtlvl", "getSeverityType", "getSeverityText", "calculateAge", "birthDate", "birth", "Date", "now", "age", "getFullYear", "selectedCodes", "localStorage", "setItem", "JSON", "stringify", "savedSelection", "getItem", "parse", "warn", "_this9", "$refs", "prescriptionTable", "restoredCount", "for<PERSON>ach", "includes", "toggleRowSelection", "log", "concat", "removeItem", "_this0", "doLayout", "_this1", "body", "document", "classList", "contains", "updateFixedAreaPosition", "observer", "MutationObserver", "mutations", "mutation", "type", "attributeName", "isHidden", "observe", "attributes", "attributeFilter", "sidebarObserver", "fixedArea", "querySelector", "style", "left", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "disconnect"], "sources": ["src/views/rms/prescription/review/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"搜索关键字\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 工具栏 -->\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"自动刷新\"\n          inactive-text=\"手动刷新\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <!-- 主要内容区域 -->\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 处方列表 -->\n      <el-col :span=\"8\">\n        <el-card class=\"list-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>待审核处方列表</span>\n            <span class=\"header-info\">（共 {{ total }} 条）</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"prescriptionList\"\n            @selection-change=\"handleSelectionChange\"\n            @row-click=\"handleRowClick\"\n            highlight-current-row\n            :height=\"tableHeight\"\n            ref=\"prescriptionTable\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"100\" show-overflow-tooltip />\n            <el-table-column label=\"医生\" align=\"center\" prop=\"doctName\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"患者\" align=\"center\" prop=\"name\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"严重程度\" align=\"center\" width=\"90\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                  {{ getSeverityText(scope.row.level) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n            class=\"pagination-container\"\n          />\n        </el-card>\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"16\">\n        <el-card class=\"detail-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n            <span class=\"header-info\">（{{ currentPrescription.code }}）</span>\n          </div>\n\n          <div class=\"detail-content\" :style=\"{ height: detailHeight, overflowY: 'auto' }\">\n            <!-- 基本信息 - 紧凑布局 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">处方号：</span>{{ currentPrescription.presId }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"16\"><span class=\"label\">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 诊断信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">诊断信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 医生和患者信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">医生和患者信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">科室：</span>{{ currentPrescription.deptName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">医生：</span>{{ currentPrescription.doctName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">患者：</span>{{ currentPrescription.name }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"6\"><span class=\"label\">性别：</span>{{ currentPrescription.sex }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">年龄：</span>{{ calculateAge(currentPrescription.birth) }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">身高：</span>{{ currentPrescription.height || '未知' }}cm</el-col>\n                <el-col :span=\"6\"><span class=\"label\">体重：</span>{{ currentPrescription.weight || '未知' }}kg</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 药品明细 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">药品明细</h4>\n              <el-table :data=\"medicationList\" size=\"mini\" border max-height=\"200\">\n                <el-table-column label=\"药名\" prop=\"medName\" min-width=\"120\" show-overflow-tooltip />\n                <el-table-column label=\"组号\" prop=\"group\" width=\"50\" />\n                <el-table-column label=\"规格\" prop=\"spec\" width=\"80\" show-overflow-tooltip />\n                <el-table-column label=\"给药途径\" prop=\"administer\" width=\"70\" />\n                <el-table-column label=\"单次量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.dose }}{{ scope.row.doseUnit }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"频次\" prop=\"freq\" width=\"50\" />\n                <el-table-column label=\"天数\" prop=\"days\" width=\"50\" />\n                <el-table-column label=\"开药数量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.ordQty }}{{ scope.row.ordUom }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"金额\" prop=\"money\" width=\"60\" />\n                <el-table-column label=\"用药说明\" prop=\"yysm\" min-width=\"100\" show-overflow-tooltip />\n              </el-table>\n            </div>\n\n            <!-- 处方分析结果 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方分析结果</h4>\n              <el-tabs v-model=\"activeTab\" type=\"card\" class=\"compact-tabs\">\n                <el-tab-pane name=\"important\">\n                  <span slot=\"label\">\n                    重要问题 <el-badge :value=\"getAnalysisResultsByLevel('重要').length\" v-if=\"getAnalysisResultsByLevel('重要').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"general\">\n                  <span slot=\"label\">\n                    一般问题 <el-badge :value=\"getAnalysisResultsByLevel('一般').length\" v-if=\"getAnalysisResultsByLevel('一般').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"other\">\n                  <span slot=\"label\">\n                    其它问题 <el-badge :value=\"getAnalysisResultsByLevel('其它').length\" v-if=\"getAnalysisResultsByLevel('其它').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n              </el-tabs>\n            </div>\n\n            <!-- 历史审核记录 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">历史审核记录</h4>\n              <el-table :data=\"reviewHistory\" size=\"mini\" border max-height=\"120\">\n                <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"80\" />\n                <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"130\">\n                  <template slot-scope=\"scope\">\n                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"审核意见\" prop=\"text\" min-width=\"150\" show-overflow-tooltip />\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n        <el-card v-else class=\"detail-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 固定在底部的审核操作区域 -->\n    <div class=\"review-actions-fixed\">\n      <el-card class=\"actions-card\">\n        <el-row :gutter=\"15\" align=\"middle\" type=\"flex\">\n          <el-col :span=\"8\">\n            <el-select\n              v-model=\"selectedProblems\"\n              multiple\n              placeholder=\"选择问题类型\"\n              style=\"width: 100%\"\n              size=\"small\"\n              collapse-tags>\n              <el-option\n                v-for=\"problem in problemTypes\"\n                :key=\"problem.cfwtbh\"\n                :label=\"problem.cfwtbh + problem.cfwtname\"\n                :value=\"problem.cfwtname\">\n              </el-option>\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"danger\"\n              icon=\"el-icon-close\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleReject\"\n              v-hasPermi=\"['rms:prescription:review:reject']\"\n              style=\"width: 100%\">\n              审核打回\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-check\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleApprove\"\n              v-hasPermi=\"['rms:prescription:review:approve']\"\n              style=\"width: 100%\">\n              审核通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"8\" class=\"text-right\">\n            <span class=\"selection-info\">\n              已选择 <strong>{{ ids.length }}</strong> 条处方\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"clearSelection\"\n                v-if=\"ids.length > 0\">\n                清空选择\n              </el-button>\n            </span>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 选中的处方对象数组（用于状态持久化）\n      selectedPrescriptions: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  computed: {\n    /** 计算表格高度 */\n    tableHeight() {\n      // 100vh - 顶部导航(50px) - 面包屑(40px) - 搜索区域(120px) - 卡片头部(60px) - 分页区域(50px) - 底部审核区域(80px) - 边距(40px)\n      return 'calc(100vh - 440px)'\n    },\n    /** 计算详情区域高度 */\n    detailHeight() {\n      // 与表格高度保持一致\n      return 'calc(100vh - 380px)'\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n    this.loadSelectedState()\n  },\n  mounted() {\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize)\n    // 监听侧边栏状态变化\n    this.checkSidebarStatus()\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      this.loading = true\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n\n        // 恢复选择状态 - 确保DOM完全更新后再恢复\n        this.$nextTick(() => {\n          // 延迟一小段时间确保表格完全渲染\n          setTimeout(() => {\n            this.restoreSelectedState()\n          }, 100)\n        })\n      }).catch(error => {\n        this.loading = false\n        console.error('获取处方列表失败:', error)\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.code)\n      this.selectedPrescriptions = selection\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n\n      // 保存选择状态到本地存储\n      this.saveSelectedState()\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    },\n\n    /** 保存选择状态到本地存储 */\n    saveSelectedState() {\n      const selectedCodes = this.ids\n      localStorage.setItem('prescription_review_selected', JSON.stringify(selectedCodes))\n    },\n\n    /** 从本地存储加载选择状态 */\n    loadSelectedState() {\n      try {\n        const savedSelection = localStorage.getItem('prescription_review_selected')\n        if (savedSelection) {\n          this.ids = JSON.parse(savedSelection)\n          this.multiple = this.ids.length === 0\n          this.single = this.ids.length !== 1\n        }\n      } catch (error) {\n        console.warn('加载选择状态失败:', error)\n        this.ids = []\n      }\n    },\n\n    /** 恢复表格选择状态 */\n    restoreSelectedState() {\n      if (this.ids.length > 0 && this.$refs.prescriptionTable && this.prescriptionList.length > 0) {\n        try {\n          // 清除当前选择\n          this.$refs.prescriptionTable.clearSelection()\n\n          // 恢复选择状态\n          let restoredCount = 0\n          this.prescriptionList.forEach(row => {\n            if (this.ids.includes(row.code)) {\n              this.$refs.prescriptionTable.toggleRowSelection(row, true)\n              restoredCount++\n            }\n          })\n\n          // 更新选择状态统计\n          this.multiple = this.ids.length === 0\n          this.single = this.ids.length !== 1\n\n          // 调试信息\n          if (restoredCount > 0) {\n            console.log(`恢复了 ${restoredCount} 个处方的选择状态`)\n          }\n        } catch (error) {\n          console.error('恢复选择状态失败:', error)\n        }\n      }\n    },\n\n    /** 清空选择 */\n    clearSelection() {\n      this.ids = []\n      this.selectedPrescriptions = []\n      this.multiple = true\n      this.single = true\n\n      // 清除表格选择\n      if (this.$refs.prescriptionTable) {\n        this.$refs.prescriptionTable.clearSelection()\n      }\n\n      // 清除本地存储\n      localStorage.removeItem('prescription_review_selected')\n    },\n\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 窗口大小变化时，强制重新计算表格高度\n      this.$nextTick(() => {\n        if (this.$refs.prescriptionTable) {\n          this.$refs.prescriptionTable.doLayout()\n        }\n      })\n    },\n\n    /** 检查侧边栏状态 */\n    checkSidebarStatus() {\n      // 检查body是否有hideSidebar类\n      const body = document.body\n      if (body.classList.contains('hideSidebar')) {\n        // 侧边栏已收起\n        this.updateFixedAreaPosition(true)\n      } else {\n        // 侧边栏展开\n        this.updateFixedAreaPosition(false)\n      }\n\n      // 监听侧边栏状态变化\n      const observer = new MutationObserver((mutations) => {\n        mutations.forEach((mutation) => {\n          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {\n            const isHidden = body.classList.contains('hideSidebar')\n            this.updateFixedAreaPosition(isHidden)\n          }\n        })\n      })\n\n      observer.observe(body, {\n        attributes: true,\n        attributeFilter: ['class']\n      })\n\n      // 保存observer引用以便清理\n      this.sidebarObserver = observer\n    },\n\n    /** 更新固定区域位置 */\n    updateFixedAreaPosition(isHidden) {\n      const fixedArea = document.querySelector('.review-actions-fixed')\n      if (fixedArea) {\n        if (isHidden) {\n          fixedArea.style.left = '54px'\n          fixedArea.style.maxWidth = 'calc(100vw - 54px)'\n        } else {\n          fixedArea.style.left = '200px'\n          fixedArea.style.maxWidth = 'calc(100vw - 200px)'\n        }\n      }\n    }\n  },\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n    }\n    // 移除事件监听器\n    window.removeEventListener('resize', this.handleResize)\n\n    // 清理侧边栏观察器\n    if (this.sidebarObserver) {\n      this.sidebarObserver.disconnect()\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 主要内容区域 */\n.app-container {\n  /*padding-bottom: 80px; !* 为固定的审核区域留出空间 *!*/\n}\n\n.main-content {\n  height: calc(100vh - 240px); /* 顶部导航(50px) + 面包屑(40px) + 搜索区域(80px) + 工具栏(40px) + 底部审核区域(80px) - 一些边距 */\n}\n\n/* 处方列表卡片 */\n.list-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.list-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.pagination-container {\n  margin-top: 10px;\n  text-align: center;\n  flex-shrink: 0;\n}\n\n/* 处方详情卡片 */\n.detail-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  overflow: hidden;\n}\n\n.detail-content {\n  padding-right: 5px;\n}\n\n/* 信息区域样式 */\n.info-section {\n  margin-bottom: 15px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 10px;\n  background-color: #fafafa;\n}\n\n.section-title {\n  margin: 0 0 10px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 14px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 5px;\n}\n\n.info-row {\n  margin-bottom: 8px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.label {\n  font-weight: 500;\n  color: #606266;\n  margin-right: 5px;\n}\n\n/* 紧凑的标签页 */\n.compact-tabs .el-tabs__header {\n  margin-bottom: 10px;\n}\n\n.compact-tabs .el-tab-pane {\n  padding: 0;\n}\n\n/* 固定在底部的审核操作区域 */\n.review-actions-fixed {\n  position: fixed;\n  bottom: 0;\n  left: 200px; /* 侧边栏宽度 */\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(to top, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);\n  backdrop-filter: blur(5px);\n  border-top: 1px solid #e4e7ed;\n  box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.1);\n  max-width: calc(100vw - 200px); /* 限制最大宽度，减去侧边栏宽度 */\n}\n\n/* 当侧边栏收起时的适配 */\n.hideSidebar .review-actions-fixed {\n  left: 54px; /* 收起后的侧边栏宽度 */\n  max-width: calc(100vw - 54px);\n}\n\n/* 移动端适配 */\n@media (max-width: 768px) {\n  .review-actions-fixed {\n    left: 0;\n    right: 0;\n    max-width: 100vw;\n  }\n}\n\n.actions-card {\n  margin: 0;\n  border: none;\n  border-radius: 0;\n  box-shadow: none;\n}\n\n.actions-card .el-card__body {\n  padding: 15px 20px;\n}\n\n.selection-info {\n  color: #606266;\n  font-size: 14px;\n}\n\n.selection-info strong {\n  color: #409eff;\n}\n\n/* 头部信息样式 */\n.header-info {\n  color: #909399;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .main-content .el-col:first-child {\n    margin-bottom: 20px;\n  }\n\n  .info-row .el-col {\n    margin-bottom: 5px;\n  }\n\n  /* 调整小屏幕下的高度计算 */\n  .main-content {\n    height: calc(100vh - 280px);\n  }\n}\n\n@media (max-width: 768px) {\n  .main-content {\n    height: calc(100vh - 320px);\n  }\n\n  .review-actions-fixed .el-row .el-col {\n    margin-bottom: 10px;\n  }\n\n  .actions-card .el-card__body {\n    padding: 10px 15px;\n  }\n}\n\n/* 超大屏幕优化 */\n@media (min-width: 1920px) {\n  .main-content {\n    height: calc(100vh - 220px);\n  }\n}\n\n/* 通用样式 */\n.text-center {\n  text-align: center;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-table {\n  margin-bottom: 5px;\n}\n\n/* 表格优化 */\n.el-table .cell {\n  padding: 0 5px;\n}\n\n.el-table--mini td {\n  padding: 4px 0;\n}\n\n/* 徽章样式 */\n.el-badge {\n  margin-left: 5px;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAyTA,IAAAA,mBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,qBAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,gBAAA;MACA;MACAC,mBAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,YAAA;MACA;MACAC,gBAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,eAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,OAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA;MACA;IACA;IACA,eACAC,YAAA,WAAAA,aAAA;MACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;IACA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACA,aACAV,OAAA,WAAAA,QAAA;MAAA,IAAAW,KAAA;MACA,KAAAxC,OAAA;MACA;MACA,IAAAyC,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAxB,WAAA;;MAEA;MACA,IAAAsB,MAAA,CAAAjB,OAAA;QACAiB,MAAA,CAAAG,QAAA,GAAAH,MAAA,CAAAjB,OAAA;QACAiB,MAAA,CAAA3C,IAAA,GAAA2C,MAAA,CAAAjB,OAAA;MACA;MAEA,IAAAqB,2CAAA,EAAAJ,MAAA,EAAAK,IAAA,WAAAC,QAAA;QACAP,KAAA,CAAAjC,gBAAA,GAAAwC,QAAA,CAAAC,IAAA;QACAR,KAAA,CAAAlC,KAAA,GAAAyC,QAAA,CAAAzC,KAAA;QACAkC,KAAA,CAAAxC,OAAA;;QAEA;QACAwC,KAAA,CAAAS,SAAA;UACA;UACAC,UAAA;YACAV,KAAA,CAAAW,oBAAA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAb,KAAA,CAAAxC,OAAA;QACAsD,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA,aACAE,WAAA,WAAAA,YAAA;MACA,KAAApC,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IAEA,aACA2B,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IAEA,cACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1D,GAAA,GAAA0D,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA;MAAA;MACA,KAAA5D,qBAAA,GAAAyD,SAAA;MACA,KAAAxD,MAAA,GAAAwD,SAAA,CAAAI,MAAA;MACA,KAAA3D,QAAA,IAAAuD,SAAA,CAAAI,MAAA;;MAEA;MACA,KAAAC,iBAAA;IACA;IAEA,YACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAA1D,mBAAA,GAAA0D,GAAA;MACA,KAAAC,yBAAA,CAAAD,GAAA,CAAAJ,IAAA;IACA;IAEA,aACAK,yBAAA,WAAAA,0BAAAL,IAAA;MAAA,IAAAM,MAAA;MACA,IAAAC,yCAAA,EAAAP,IAAA,EAAAhB,IAAA,WAAAC,QAAA;QACAqB,MAAA,CAAA5D,mBAAA,GAAAuC,QAAA,CAAAhD,IAAA,CAAAuE,YAAA;QACAF,MAAA,CAAA3D,cAAA,GAAAsC,QAAA,CAAAhD,IAAA,CAAAwE,WAAA;QACAH,MAAA,CAAA1D,eAAA,GAAAqC,QAAA,CAAAhD,IAAA,CAAAW,eAAA;QACA0D,MAAA,CAAAzD,aAAA,GAAAoC,QAAA,CAAAhD,IAAA,CAAAY,aAAA;MACA;IACA;IAEA,aACAmB,iBAAA,WAAAA,kBAAA;MAAA,IAAA0C,MAAA;MACA,IAAAC,kCAAA,IAAA3B,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAA5D,cAAA,GAAAmC,QAAA,CAAAhD,IAAA;MACA;IACA;IAEA,eACAgC,kBAAA,WAAAA,mBAAA;MAAA,IAAA2C,MAAA;MACA,IAAAC,mCAAA,IAAA7B,IAAA,WAAAC,QAAA;QACA2B,MAAA,CAAA7D,YAAA,GAAAkC,QAAA,CAAAhD,IAAA;MACA;IACA;IAEA,aACAiC,gBAAA,WAAAA,iBAAA;MAAA,IAAA4C,MAAA;MACA,IAAAC,kCAAA,IAAA/B,IAAA,WAAAC,QAAA;QACA6B,MAAA,CAAA3D,eAAA,GAAA8B,QAAA,CAAAhD,IAAA;MACA;IACA;IAEA,aACA+E,iBAAA,WAAAA,kBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA;QACA,KAAA/D,YAAA,GAAAiE,WAAA;UACAD,MAAA,CAAAnD,OAAA;QACA,QAAAZ,eAAA;MACA;QACA,SAAAD,YAAA;UACAkE,aAAA,MAAAlE,YAAA;UACA,KAAAA,YAAA;QACA;MACA;IACA;IAEA,WACAmE,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAAnF,GAAA,CAAA8D,MAAA;QACA,KAAAsB,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAD,MAAA,CAAAE,OAAA,mBAAAzC,IAAA;QACA,WAAA0C,wCAAA,EAAAJ,MAAA,CAAAnF,GAAA;MACA,GAAA6C,IAAA;QACAsC,MAAA,CAAAvD,OAAA;QACAuD,MAAA,CAAAK,cAAA;QACAL,MAAA,CAAAC,MAAA,CAAAK,UAAA;MACA,GAAAtC,KAAA;IACA;IAEA,WACAuC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAA3F,GAAA,CAAA8D,MAAA;QACA,KAAAsB,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,SAAAxE,gBAAA,CAAAiD,MAAA;QACA,KAAAsB,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAD,MAAA,CAAAE,OAAA,iBAAAzC,IAAA;QACA,IAAA/C,IAAA;UACA8F,KAAA,EAAAD,MAAA,CAAA3F,GAAA;UACA6F,YAAA,EAAAF,MAAA,CAAA9E;QACA;QACA,WAAAiF,uCAAA,EAAAhG,IAAA;MACA,GAAA+C,IAAA;QACA8C,MAAA,CAAA/D,OAAA;QACA+D,MAAA,CAAA9E,gBAAA;QACA8E,MAAA,CAAAH,cAAA;QACAG,MAAA,CAAAP,MAAA,CAAAK,UAAA;MACA,GAAAtC,KAAA;IACA;IAEA,mBACA4C,yBAAA,WAAAA,0BAAAC,KAAA;MACA,YAAAvF,eAAA,CAAAwF,MAAA,WAAArC,IAAA;QAAA,OAAAA,IAAA,CAAAsC,KAAA,KAAAF,KAAA;MAAA;IACA;IAEA,eACAG,eAAA,WAAAA,gBAAAH,KAAA;MACA,QAAAA,KAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA,eACAI,eAAA,WAAAA,gBAAAJ,KAAA;MACA,OAAAA,KAAA;IACA;IAEA,WACAK,YAAA,WAAAA,aAAAC,SAAA;MACA,KAAAA,SAAA;MACA,IAAAC,KAAA,OAAAC,IAAA,CAAAF,SAAA;MACA,IAAAG,GAAA,OAAAD,IAAA;MACA,IAAAE,GAAA,GAAAD,GAAA,CAAAE,WAAA,KAAAJ,KAAA,CAAAI,WAAA;MACA,OAAAD,GAAA;IACA;IAEA,kBACA3C,iBAAA,WAAAA,kBAAA;MACA,IAAA6C,aAAA,QAAA5G,GAAA;MACA6G,YAAA,CAAAC,OAAA,iCAAAC,IAAA,CAAAC,SAAA,CAAAJ,aAAA;IACA;IAEA,kBACA5E,iBAAA,WAAAA,kBAAA;MACA;QACA,IAAAiF,cAAA,GAAAJ,YAAA,CAAAK,OAAA;QACA,IAAAD,cAAA;UACA,KAAAjH,GAAA,GAAA+G,IAAA,CAAAI,KAAA,CAAAF,cAAA;UACA,KAAA9G,QAAA,QAAAH,GAAA,CAAA8D,MAAA;UACA,KAAA5D,MAAA,QAAAF,GAAA,CAAA8D,MAAA;QACA;MACA,SAAAV,KAAA;QACAC,OAAA,CAAA+D,IAAA,cAAAhE,KAAA;QACA,KAAApD,GAAA;MACA;IACA;IAEA,eACAkD,oBAAA,WAAAA,qBAAA;MAAA,IAAAmE,MAAA;MACA,SAAArH,GAAA,CAAA8D,MAAA,aAAAwD,KAAA,CAAAC,iBAAA,SAAAjH,gBAAA,CAAAwD,MAAA;QACA;UACA;UACA,KAAAwD,KAAA,CAAAC,iBAAA,CAAA/B,cAAA;;UAEA;UACA,IAAAgC,aAAA;UACA,KAAAlH,gBAAA,CAAAmH,OAAA,WAAAxD,GAAA;YACA,IAAAoD,MAAA,CAAArH,GAAA,CAAA0H,QAAA,CAAAzD,GAAA,CAAAJ,IAAA;cACAwD,MAAA,CAAAC,KAAA,CAAAC,iBAAA,CAAAI,kBAAA,CAAA1D,GAAA;cACAuD,aAAA;YACA;UACA;;UAEA;UACA,KAAArH,QAAA,QAAAH,GAAA,CAAA8D,MAAA;UACA,KAAA5D,MAAA,QAAAF,GAAA,CAAA8D,MAAA;;UAEA;UACA,IAAA0D,aAAA;YACAnE,OAAA,CAAAuE,GAAA,uBAAAC,MAAA,CAAAL,aAAA;UACA;QACA,SAAApE,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA;MACA;IACA;IAEA,WACAoC,cAAA,WAAAA,eAAA;MACA,KAAAxF,GAAA;MACA,KAAAC,qBAAA;MACA,KAAAE,QAAA;MACA,KAAAD,MAAA;;MAEA;MACA,SAAAoH,KAAA,CAAAC,iBAAA;QACA,KAAAD,KAAA,CAAAC,iBAAA,CAAA/B,cAAA;MACA;;MAEA;MACAqB,YAAA,CAAAiB,UAAA;IACA;IAEA,eACA1F,YAAA,WAAAA,aAAA;MAAA,IAAA2F,MAAA;MACA;MACA,KAAA/E,SAAA;QACA,IAAA+E,MAAA,CAAAT,KAAA,CAAAC,iBAAA;UACAQ,MAAA,CAAAT,KAAA,CAAAC,iBAAA,CAAAS,QAAA;QACA;MACA;IACA;IAEA,cACA3F,kBAAA,WAAAA,mBAAA;MAAA,IAAA4F,MAAA;MACA;MACA,IAAAC,IAAA,GAAAC,QAAA,CAAAD,IAAA;MACA,IAAAA,IAAA,CAAAE,SAAA,CAAAC,QAAA;QACA;QACA,KAAAC,uBAAA;MACA;QACA;QACA,KAAAA,uBAAA;MACA;;MAEA;MACA,IAAAC,QAAA,OAAAC,gBAAA,WAAAC,SAAA;QACAA,SAAA,CAAAhB,OAAA,WAAAiB,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,qBAAAD,QAAA,CAAAE,aAAA;YACA,IAAAC,QAAA,GAAAX,IAAA,CAAAE,SAAA,CAAAC,QAAA;YACAJ,MAAA,CAAAK,uBAAA,CAAAO,QAAA;UACA;QACA;MACA;MAEAN,QAAA,CAAAO,OAAA,CAAAZ,IAAA;QACAa,UAAA;QACAC,eAAA;MACA;;MAEA;MACA,KAAAC,eAAA,GAAAV,QAAA;IACA;IAEA,eACAD,uBAAA,WAAAA,wBAAAO,QAAA;MACA,IAAAK,SAAA,GAAAf,QAAA,CAAAgB,aAAA;MACA,IAAAD,SAAA;QACA,IAAAL,QAAA;UACAK,SAAA,CAAAE,KAAA,CAAAC,IAAA;UACAH,SAAA,CAAAE,KAAA,CAAAE,QAAA;QACA;UACAJ,SAAA,CAAAE,KAAA,CAAAC,IAAA;UACAH,SAAA,CAAAE,KAAA,CAAAE,QAAA;QACA;MACA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAxI,YAAA;MACAkE,aAAA,MAAAlE,YAAA;IACA;IACA;IACAmB,MAAA,CAAAsH,mBAAA,gBAAApH,YAAA;;IAEA;IACA,SAAA6G,eAAA;MACA,KAAAA,eAAA,CAAAQ,UAAA;IACA;EACA;AACA", "ignoreList": []}]}