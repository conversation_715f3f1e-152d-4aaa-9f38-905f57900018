{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752989385426}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\babel.config.js", "mtime": 1748394285000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_prescriptionreview", "require", "name", "data", "loading", "ids", "selectedPrescriptions", "single", "multiple", "showSearch", "total", "prescriptionList", "currentPrescription", "medicationList", "analysisResults", "reviewHistory", "departmentList", "problemTypes", "selectedProblems", "autoRefresh", "refreshTimer", "refreshInterval", "isAutoRefreshTriggered", "wasAutoRefreshActive", "isRestoringSelection", "activeTab", "queryParams", "pageNum", "pageSize", "deptCode", "prescriptionType", "keyword", "computed", "tableHeight", "detailHeight", "created", "getList", "getDepartmentList", "getProblemTypeList", "getRefreshConfig", "loadSelectedState", "mounted", "window", "addEventListener", "handleResize", "checkSidebarStatus", "document", "handleVisibilityChange", "methods", "_this", "console", "log", "是否自动刷新", "当前页码", "页面大小", "加载时间", "Date", "toLocaleTimeString", "当前选择状态", "savedIds", "_toConsumableArray2", "default", "备份的ids", "params", "_objectSpread2", "doctName", "getPendingPrescriptions", "then", "response", "数据条数", "rows", "length", "总数", "处方编码列表", "map", "item", "code", "当前ids", "ids数量", "表格ref存在", "$refs", "prescriptionTable", "是否自动刷新触发", "handleAutoRefreshRestore", "$nextTick", "setTimeout", "restoreSelectedState", "catch", "error", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "选中数量", "选中处方", "触发时间", "正在恢复状态", "saveSelectedState", "handleRowClick", "row", "getPrescriptionDetailData", "_this2", "getPrescriptionDetail", "prescription", "medications", "_this3", "getDepartments", "_this4", "getProblemTypes", "_this5", "getRefreshTime", "toggleAutoRefresh", "value", "_this6", "开启", "刷新间隔", "当前选择数量", "setInterval", "clearInterval", "handleApprove", "_this7", "$modal", "msgError", "confirm", "approvePrescriptions", "clearSelection", "msgSuccess", "handleReject", "_this8", "codes", "problemNames", "rejectPrescriptions", "getAnalysisResultsByLevel", "level", "filter", "wtlvl", "getSeverityType", "getSeverityText", "calculateAge", "birthDate", "birth", "now", "age", "getFullYear", "selectedCodes", "选中的处方编码", "数量", "保存时间", "localStorage", "setItem", "JSON", "stringify", "saved", "getItem", "保存的数据", "解析后", "parse", "savedSelection", "原始数据", "数据类型", "_typeof2", "parsedIds", "加载的ids", "_this9", "需要恢复的ids", "处方列表长度", "处方列表编码", "restoredCount", "matchedRows", "unmatchedIds", "for<PERSON>ach", "includes", "concat", "toggleRowSelection", "push", "id", "find", "恢复成功数量", "匹配的处方", "未匹配的ID", "最终状态", "selectedRows", "表格选中行数", "表格选中编码", "期望选中数量", "状态一致", "warn", "restoreSelectedStateRetry", "_this0", "removeItem", "清空时间", "_this1", "retryCount", "_this10", "_this10$$refs$prescri", "_this11", "doLayout", "_this12", "body", "classList", "contains", "updateFixedAreaPosition", "observer", "MutationObserver", "mutations", "mutation", "type", "attributeName", "isHidden", "observe", "attributes", "attributeFilter", "sidebarObserver", "fixedArea", "querySelector", "style", "left", "max<PERSON><PERSON><PERSON>", "_this13", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "disconnect"], "sources": ["src/views/rms/prescription/review/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"搜索关键字\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n      <!-- 自动刷新开关移动到筛选条件区域右侧 -->\n      <el-form-item style=\"float: right; margin-right: 0;\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"开启审方\"\n          inactive-text=\"停止审方\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-form-item>\n    </el-form>\n\n    <!-- 主要内容区域 -->\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 处方列表 -->\n      <el-col :span=\"9\" style=\"height: 100%\">\n        <el-card class=\"list-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>待审核处方列表</span>\n            <span class=\"header-info\">（共 {{ total }} 条）</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"prescriptionList\"\n            @selection-change=\"handleSelectionChange\"\n            @row-click=\"handleRowClick\"\n            highlight-current-row\n            :height=\"tableHeight\"\n            ref=\"prescriptionTable\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"100\" show-overflow-tooltip />\n            <el-table-column label=\"医生\" align=\"center\" prop=\"doctName\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"患者\" align=\"center\" prop=\"name\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"严重程度\" align=\"center\" width=\"90\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                  {{ getSeverityText(scope.row.level) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n            class=\"pagination-container\"\n          />\n        </el-card>\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"15\" style=\"height: 100%\">\n        <el-card class=\"detail-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n            <span class=\"header-info\">（{{ currentPrescription.code }}）</span>\n          </div>\n\n          <div class=\"detail-content\" :style=\"{ height: detailHeight, overflowY: 'auto' }\">\n            <!-- 合并的基本信息卡片 -->\n            <div class=\"unified-info-section\">\n              <!-- 处方基本信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">处方信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">处方号：</span>{{ currentPrescription.presId }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>\n                </el-row>\n              </div>\n\n              <!-- 医生和患者信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">医生和患者信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">科室：</span>{{ currentPrescription.deptName }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">医生：</span>{{ currentPrescription.doctName }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">患者：</span>{{ currentPrescription.name }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">性别/年龄：</span>{{ currentPrescription.sex }} / {{ calculateAge(currentPrescription.birth) }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">身高/体重：</span>{{ currentPrescription.height || '未知' }}cm / {{ currentPrescription.weight || '未知' }}kg</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"24\"><span class=\"label\">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>\n                </el-row>\n              </div>\n\n              <!-- 诊断信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">诊断信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>\n                </el-row>\n              </div>\n            </div>\n\n            <!-- 药品明细 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">药品明细</h4>\n              <el-table :data=\"medicationList\" size=\"mini\" border max-height=\"200\">\n                <el-table-column label=\"药名\" align=\"center\" prop=\"medName\" min-width=\"120\" show-overflow-tooltip />\n                <el-table-column label=\"组号\" align=\"center\" prop=\"group\" width=\"50\" />\n                <el-table-column label=\"规格\" align=\"center\" prop=\"spec\" width=\"80\" show-overflow-tooltip />\n                <el-table-column label=\"给药途径\" align=\"center\" prop=\"administer\" width=\"80\" />\n                <el-table-column label=\"单次量\" align=\"center\" width=\"100\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.dose) || 0).toFixed(2) }}{{ scope.row.doseUnit }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"频次\" align=\"center\" prop=\"freq\" width=\"50\" />\n                <el-table-column label=\"天数\" align=\"center\" prop=\"days\" width=\"50\" />\n                <el-table-column label=\"开药数量\" align=\"center\" width=\"80\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.ordQty) || 0).toFixed(2) }}{{ scope.row.ordUom }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"金额\" align=\"center\" prop=\"money\" width=\"80\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.money) || 0).toFixed(2) }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"用药说明\" align=\"center\" prop=\"yysm\" min-width=\"100\" show-overflow-tooltip />\n              </el-table>\n            </div>\n\n            <!-- 处方分析结果 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方分析结果</h4>\n              <el-tabs v-model=\"activeTab\" type=\"card\" class=\"compact-tabs\">\n                <el-tab-pane name=\"important\">\n                  <span slot=\"label\">\n                    重要问题 <el-badge :value=\"getAnalysisResultsByLevel('重要').length\" v-if=\"getAnalysisResultsByLevel('重要').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"general\">\n                  <span slot=\"label\">\n                    一般问题 <el-badge :value=\"getAnalysisResultsByLevel('一般').length\" v-if=\"getAnalysisResultsByLevel('一般').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"other\">\n                  <span slot=\"label\">\n                    其它问题 <el-badge :value=\"getAnalysisResultsByLevel('其它').length\" v-if=\"getAnalysisResultsByLevel('其它').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n              </el-tabs>\n            </div>\n\n            <!-- 历史审核记录 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">历史审核记录</h4>\n              <el-table :data=\"reviewHistory\" size=\"mini\" border max-height=\"120\">\n                <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"80\" />\n                <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"130\">\n                  <template slot-scope=\"scope\">\n                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"审核意见\" prop=\"text\" min-width=\"150\" show-overflow-tooltip />\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n        <el-card v-else class=\"detail-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 固定在底部的审核操作区域 -->\n    <div class=\"review-actions-fixed\">\n      <el-card class=\"actions-card\">\n        <el-row :gutter=\"15\" align=\"middle\" type=\"flex\">\n          <el-col :span=\"8\">\n            <el-select\n              v-model=\"selectedProblems\"\n              multiple\n              placeholder=\"选择问题类型\"\n              style=\"width: 100%\"\n              size=\"small\"\n              collapse-tags>\n              <el-option\n                v-for=\"problem in problemTypes\"\n                :key=\"problem.cfwtbh\"\n                :label=\"problem.cfwtbh + ' ' +problem.cfwtname\"\n                :value=\"problem.cfwtname\">\n              </el-option>\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"danger\"\n              icon=\"el-icon-close\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleReject\"\n              v-hasPermi=\"['rms:prescription:review:reject']\"\n              style=\"width: 100%\">\n              审核打回\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-check\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleApprove\"\n              v-hasPermi=\"['rms:prescription:review:approve']\"\n              style=\"width: 100%\">\n              审核通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"8\" class=\"text-right\">\n            <span class=\"selection-info\">\n              已选择 <strong>{{ ids.length }}</strong> 条处方\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"clearSelection\"\n                v-if=\"ids.length > 0\">\n                清空选择\n              </el-button>\n            </span>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 选中的处方对象数组（用于状态持久化）\n      selectedPrescriptions: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 是否由自动刷新触发的标记\n      isAutoRefreshTriggered: false,\n      // 页面不可见前是否开启了自动刷新\n      wasAutoRefreshActive: false,\n      // 是否正在恢复选择状态（用于阻止意外的选择变化事件）\n      isRestoringSelection: false,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  computed: {\n    /** 计算表格高度 */\n    tableHeight() {\n      // 100vh - 顶部导航(50px) - 面包屑(40px) - 搜索区域(80px) - 卡片头部(60px) - 分页区域(50px) - 底部审核区域(80px) - 边距(40px)\n      return 'calc(100vh - 350px)'\n    },\n    /** 计算详情区域高度 */\n    detailHeight() {\n      // 与表格高度保持一致，减去卡片头部\n      return 'calc(100vh - 340px)'\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n    this.loadSelectedState()\n  },\n  mounted() {\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize)\n    // 监听侧边栏状态变化\n    this.checkSidebarStatus()\n    // 监听页面可见性变化\n    document.addEventListener('visibilitychange', this.handleVisibilityChange)\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      console.log('🔄 [数据加载] 开始加载处方列表', {\n        是否自动刷新: this.autoRefresh,\n        当前页码: this.queryParams.pageNum,\n        页面大小: this.queryParams.pageSize,\n        加载时间: new Date().toLocaleTimeString(),\n        当前选择状态: this.ids\n      })\n\n      // 在数据加载前保存当前的选择状态\n      const savedIds = [...this.ids]\n      console.log('💾 [状态备份] 备份当前选择状态', { 备份的ids: savedIds })\n\n      this.loading = true\n      // 设置恢复状态标记，防止表格重新渲染时清空选择\n      this.isRestoringSelection = true\n\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        console.log('📊 [数据加载] 处方列表加载完成', {\n          数据条数: response.rows.length,\n          总数: response.total,\n          处方编码列表: response.rows.map(item => item.code)\n        })\n\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n\n        // 恢复备份的选择状态\n        this.ids = savedIds\n\n        console.log('🔄 [状态恢复] 准备恢复选择状态', {\n          备份的ids: savedIds,\n          当前ids: this.ids,\n          ids数量: this.ids.length,\n          表格ref存在: !!this.$refs.prescriptionTable,\n          是否自动刷新触发: this.isAutoRefreshTriggered\n        })\n\n        // 根据是否自动刷新使用不同的恢复策略\n        if (this.isAutoRefreshTriggered) {\n          console.log('🔄 [自动刷新] 使用自动刷新专用恢复机制')\n          this.handleAutoRefreshRestore()\n          this.isAutoRefreshTriggered = false // 重置标记\n        } else {\n          console.log('👆 [手动操作] 使用常规恢复机制')\n          // 恢复选择状态 - 确保DOM完全更新后再恢复\n          this.$nextTick(() => {\n            console.log('⏰ [延迟恢复] $nextTick执行，准备延迟恢复状态')\n            // 延迟一小段时间确保表格完全渲染\n            setTimeout(() => {\n              console.log('🎯 [开始恢复] 开始执行状态恢复')\n              this.restoreSelectedState()\n            }, 200) // 增加延迟时间到200ms\n          })\n        }\n      }).catch(error => {\n        this.loading = false\n        this.isRestoringSelection = false // 出错时也要重置标记\n        console.error('❌ [加载失败] 获取处方列表失败:', error)\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      console.log('🔄 [选择变化] 触发选择变化事件', {\n        选中数量: selection.length,\n        选中处方: selection.map(item => ({ code: item.code, name: item.name })),\n        触发时间: new Date().toLocaleTimeString(),\n        正在恢复状态: this.isRestoringSelection\n      })\n\n      // 如果正在恢复选择状态，忽略这次选择变化事件\n      if (this.isRestoringSelection) {\n        console.log('🚫 [忽略事件] 正在恢复选择状态，忽略此次选择变化事件')\n        return\n      }\n\n      this.ids = selection.map(item => item.code)\n      this.selectedPrescriptions = selection\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n      console.log('multiple', this.multiple)\n      console.log('selection.length', selection.length)\n\n      console.log('💾 [状态更新] 更新组件状态', {\n        ids: this.ids,\n        single: this.single,\n        multiple: this.multiple\n      })\n\n      // 保存选择状态到本地存储\n      this.saveSelectedState()\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      console.log('🔄 [自动刷新] 切换自动刷新状态', {\n        开启: value,\n        刷新间隔: this.refreshInterval,\n        当前选择数量: this.ids.length\n      })\n\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          console.log('⏰ [自动刷新] 自动刷新定时器触发', {\n            触发时间: new Date().toLocaleTimeString(),\n            当前选择状态: this.ids\n          })\n\n          // 标记这是自动刷新触发的数据加载\n          this.isAutoRefreshTriggered = true\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    },\n\n    /** 保存选择状态到本地存储 */\n    saveSelectedState() {\n      const selectedCodes = this.ids\n      console.log('💾 [保存状态] 保存选择状态到localStorage', {\n        选中的处方编码: selectedCodes,\n        数量: selectedCodes.length,\n        保存时间: new Date().toLocaleTimeString()\n      })\n\n      localStorage.setItem('prescription_review_selected', JSON.stringify(selectedCodes))\n\n      // 验证保存是否成功\n      const saved = localStorage.getItem('prescription_review_selected')\n      console.log('✅ [保存验证] localStorage保存结果', {\n        保存的数据: saved,\n        解析后: JSON.parse(saved || '[]')\n      })\n    },\n\n    /** 从本地存储加载选择状态 */\n    loadSelectedState() {\n      console.log('📖 [加载状态] 开始从localStorage加载选择状态')\n\n      try {\n        const savedSelection = localStorage.getItem('prescription_review_selected')\n        console.log('📖 [加载状态] localStorage中的数据', {\n          原始数据: savedSelection,\n          数据类型: typeof savedSelection\n        })\n\n        if (savedSelection) {\n          const parsedIds = JSON.parse(savedSelection)\n          this.ids = parsedIds\n          this.multiple = this.ids.length === 0\n          this.single = this.ids.length !== 1\n\n          console.log('✅ [加载成功] 状态加载完成', {\n            加载的ids: this.ids,\n            数量: this.ids.length,\n            multiple: this.multiple,\n            single: this.single\n          })\n        } else {\n          console.log('ℹ️ [加载状态] localStorage中没有保存的选择状态')\n          this.ids = []\n        }\n      } catch (error) {\n        console.error('❌ [加载失败] 加载选择状态失败:', error)\n        this.ids = []\n      }\n    },\n\n    /** 恢复表格选择状态 */\n    restoreSelectedState() {\n      console.log('🎯 [状态恢复] 开始恢复表格选择状态', {\n        需要恢复的ids: this.ids,\n        ids数量: this.ids.length,\n        表格ref存在: !!this.$refs.prescriptionTable,\n        处方列表长度: this.prescriptionList.length,\n        处方列表编码: this.prescriptionList.map(item => item.code)\n      })\n\n      // 检查前置条件\n      if (this.ids.length === 0) {\n        console.log('ℹ️ [状态恢复] 没有需要恢复的选择状态')\n        return\n      }\n\n      if (!this.$refs.prescriptionTable) {\n        console.error('❌ [状态恢复] 表格ref不存在')\n        return\n      }\n\n      if (this.prescriptionList.length === 0) {\n        console.log('ℹ️ [状态恢复] 处方列表为空，无法恢复状态')\n        return\n      }\n\n      try {\n        // 清除当前选择\n        console.log('🧹 [清除选择] 清除表格当前选择状态')\n        this.$refs.prescriptionTable.clearSelection()\n\n        // 恢复选择状态\n        let restoredCount = 0\n        let matchedRows = []\n        let unmatchedIds = []\n\n        this.prescriptionList.forEach(row => {\n          if (this.ids.includes(row.code)) {\n            console.log(`✅ [匹配成功] 找到匹配的处方: ${row.code} - ${row.name}`)\n            this.$refs.prescriptionTable.toggleRowSelection(row, true)\n            restoredCount++\n            matchedRows.push({ code: row.code, name: row.name })\n          }\n        })\n\n        // 检查未匹配的ID\n        this.ids.forEach(id => {\n          if (!this.prescriptionList.find(row => row.code === id)) {\n            unmatchedIds.push(id)\n          }\n        })\n\n        // 更新选择状态统计\n        this.multiple = this.ids.length === 0\n        this.single = this.ids.length !== 1\n\n        console.log('📊 [恢复结果] 状态恢复完成', {\n          恢复成功数量: restoredCount,\n          匹配的处方: matchedRows,\n          未匹配的ID: unmatchedIds,\n          最终状态: {\n            multiple: this.multiple,\n            single: this.single,\n            ids: this.ids\n          }\n        })\n\n        // 验证恢复结果\n        this.$nextTick(() => {\n          const selectedRows = this.$refs.prescriptionTable.selection || []\n          console.log('🔍 [恢复验证] 验证表格选择状态', {\n            表格选中行数: selectedRows.length,\n            表格选中编码: selectedRows.map(row => row.code),\n            期望选中数量: this.ids.length,\n            状态一致: selectedRows.length === restoredCount\n          })\n\n          if (selectedRows.length !== restoredCount) {\n            console.warn('⚠️ [状态不一致] 表格选择状态与预期不符，尝试重新恢复')\n            // 如果状态不一致，再次尝试恢复\n            setTimeout(() => {\n              this.restoreSelectedStateRetry()\n            }, 100)\n          } else {\n            // 恢复成功，重置恢复状态标记\n            console.log('✅ [恢复完成] 状态恢复成功，重置恢复标记')\n            this.isRestoringSelection = false\n          }\n        })\n\n      } catch (error) {\n        console.error('❌ [恢复失败] 恢复选择状态失败:', error)\n      }\n    },\n\n    /** 清空选择 */\n    clearSelection() {\n      // 设置恢复标记，防止clearSelection触发选择变化事件\n      this.isRestoringSelection = true\n\n      this.ids = []\n      this.selectedPrescriptions = []\n      this.multiple = true\n      this.single = true\n\n      // 清除表格选择\n      if (this.$refs.prescriptionTable) {\n        this.$refs.prescriptionTable.clearSelection()\n      }\n\n      // 清除本地存储\n      localStorage.removeItem('prescription_review_selected')\n\n      console.log('🧹 [清空选择] 选择状态已清空', {\n        清空时间: new Date().toLocaleTimeString()\n      })\n\n      // 重置恢复标记\n      setTimeout(() => {\n        this.isRestoringSelection = false\n      }, 100)\n    },\n\n    /** 重试恢复选择状态 */\n    restoreSelectedStateRetry() {\n      console.log('🔄 [重试恢复] 开始重试恢复选择状态')\n\n      if (!this.$refs.prescriptionTable) {\n        console.error('❌ [重试失败] 表格ref仍然不存在')\n        this.isRestoringSelection = false\n        return\n      }\n\n      // 强制清除选择\n      this.$refs.prescriptionTable.clearSelection()\n\n      // 重新恢复\n      let retryCount = 0\n      this.prescriptionList.forEach(row => {\n        if (this.ids.includes(row.code)) {\n          this.$refs.prescriptionTable.toggleRowSelection(row, true)\n          retryCount++\n          console.log(`🔄 [重试恢复] 重新选择处方: ${row.code}`)\n        }\n      })\n\n      console.log(`✅ [重试完成] 重试恢复了 ${retryCount} 个处方的选择状态`)\n\n      // 重试完成后重置标记\n      this.isRestoringSelection = false\n    },\n\n    /** 自动刷新时的特殊处理 */\n    handleAutoRefreshRestore() {\n      console.log('🔄 [自动刷新] 自动刷新触发，特殊处理选择状态恢复')\n\n      // 在自动刷新时，给更多时间让表格完全渲染\n      this.$nextTick(() => {\n        setTimeout(() => {\n          console.log('🎯 [自动刷新恢复] 开始自动刷新后的状态恢复')\n          this.restoreSelectedState()\n\n          // 额外验证\n          setTimeout(() => {\n            const selectedRows = this.$refs.prescriptionTable?.selection || []\n            if (selectedRows.length === 0 && this.ids.length > 0) {\n              console.warn('⚠️ [自动刷新] 第一次恢复失败，进行第二次尝试')\n              this.restoreSelectedStateRetry()\n            } else {\n              // 自动刷新恢复成功，重置标记\n              console.log('✅ [自动刷新] 自动刷新恢复成功，重置恢复标记')\n              this.isRestoringSelection = false\n            }\n          }, 300)\n        }, 300) // 自动刷新时使用更长的延迟\n      })\n    },\n\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 窗口大小变化时，强制重新计算表格高度\n      this.$nextTick(() => {\n        if (this.$refs.prescriptionTable) {\n          this.$refs.prescriptionTable.doLayout()\n        }\n      })\n    },\n\n    /** 检查侧边栏状态 */\n    checkSidebarStatus() {\n      // 检查body是否有hideSidebar类\n      const body = document.body\n      if (body.classList.contains('hideSidebar')) {\n        // 侧边栏已收起\n        this.updateFixedAreaPosition(true)\n      } else {\n        // 侧边栏展开\n        this.updateFixedAreaPosition(false)\n      }\n\n      // 监听侧边栏状态变化\n      const observer = new MutationObserver((mutations) => {\n        mutations.forEach((mutation) => {\n          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {\n            const isHidden = body.classList.contains('hideSidebar')\n            this.updateFixedAreaPosition(isHidden)\n          }\n        })\n      })\n\n      observer.observe(body, {\n        attributes: true,\n        attributeFilter: ['class']\n      })\n\n      // 保存observer引用以便清理\n      this.sidebarObserver = observer\n    },\n\n    /** 更新固定区域位置 */\n    updateFixedAreaPosition(isHidden) {\n      const fixedArea = document.querySelector('.review-actions-fixed')\n      if (fixedArea) {\n        if (isHidden) {\n          fixedArea.style.left = '54px'\n          fixedArea.style.maxWidth = 'calc(100vw - 54px)'\n        } else {\n          fixedArea.style.left = '200px'\n          fixedArea.style.maxWidth = 'calc(100vw - 200px)'\n        }\n      }\n    },\n\n    /** 处理页面可见性变化 */\n    handleVisibilityChange() {\n      if (document.hidden) {\n        console.log('👁️ [页面状态] 页面变为不可见，暂停自动刷新')\n        // 页面不可见时暂停自动刷新\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n          this.wasAutoRefreshActive = this.autoRefresh\n        }\n      } else {\n        console.log('👁️ [页面状态] 页面变为可见，恢复自动刷新')\n        // 页面可见时恢复自动刷新\n        if (this.wasAutoRefreshActive && this.autoRefresh) {\n          this.toggleAutoRefresh(true)\n        }\n\n        // 页面重新可见时，如果有选择状态需要恢复，立即恢复\n        if (this.ids.length > 0) {\n          console.log('🔄 [页面可见] 页面重新可见，检查选择状态')\n          setTimeout(() => {\n            this.restoreSelectedState()\n          }, 100)\n        }\n      }\n    }\n  },\n  beforeDestroy() {\n    console.log('🧹 [组件销毁] 开始清理组件资源')\n\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n      console.log('🧹 [组件销毁] 清理自动刷新定时器')\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('resize', this.handleResize)\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange)\n    console.log('🧹 [组件销毁] 清理事件监听器')\n\n    // 清理侧边栏观察器\n    if (this.sidebarObserver) {\n      this.sidebarObserver.disconnect()\n      console.log('🧹 [组件销毁] 清理侧边栏观察器')\n    }\n\n    console.log('✅ [组件销毁] 组件资源清理完成')\n  }\n}\n</script>\n\n<style scoped>\n/* 主要内容区域 */\n.app-container {\n  /*padding-bottom: 80px; !* 为固定的审核区域留出空间 *!*/\n}\n\n.main-content {\n  height: calc(100vh - 230px); /* 顶部导航(50px) + 面包屑(40px) + 搜索区域(80px) + 底部审核区域(80px) - 一些边距，移除工具栏高度 */\n}\n\n/* 处方列表卡片 */\n.list-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.list-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.pagination-container {\n  margin-top: 10px;\n  text-align: center;\n  flex-shrink: 0;\n}\n\n/* 处方详情卡片 */\n.detail-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  overflow: hidden;\n}\n\n.detail-content {\n  padding-right: 5px;\n}\n\n/* 统一信息区域样式 */\n.unified-info-section {\n  border: 1px solid #ebeef5;\n  border-radius: 6px;\n  padding: 12px;\n  background-color: #fafafa;\n  margin-bottom: 12px;\n}\n\n.info-group {\n  margin-bottom: 12px;\n}\n\n.info-group:last-child {\n  margin-bottom: 0;\n}\n\n.group-title {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 13px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 4px;\n}\n\n.compact-row {\n  margin-bottom: 6px;\n  font-size: 12px;\n  line-height: 1.3;\n}\n\n.compact-row:last-child {\n  margin-bottom: 0;\n}\n\n.label {\n  font-weight: 500;\n  color: #606266;\n  margin-right: 4px;\n}\n\n/* 保留原有信息区域样式以兼容其他部分 */\n.info-section {\n  margin-bottom: 12px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 8px;\n  background-color: #fafafa;\n}\n\n.section-title {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 13px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 4px;\n}\n\n.info-row {\n  margin-bottom: 6px;\n  font-size: 12px;\n  line-height: 1.3;\n}\n\n/* 紧凑的标签页 */\n.compact-tabs .el-tabs__header {\n  margin-bottom: 10px;\n}\n\n.compact-tabs .el-tab-pane {\n  padding: 0;\n}\n\n/* 固定在底部的审核操作区域 */\n.review-actions-fixed {\n  position: fixed;\n  bottom: 0;\n  left: 200px; /* 侧边栏宽度 */\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(to top, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);\n  backdrop-filter: blur(5px);\n  border-top: 1px solid #e4e7ed;\n  box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.1);\n  max-width: calc(100vw - 200px); /* 限制最大宽度，减去侧边栏宽度 */\n}\n\n/* 当侧边栏收起时的适配 */\n.hideSidebar .review-actions-fixed {\n  left: 54px; /* 收起后的侧边栏宽度 */\n  max-width: calc(100vw - 54px);\n}\n\n/* 移动端适配 */\n@media (max-width: 768px) {\n  .review-actions-fixed {\n    left: 0;\n    right: 0;\n    max-width: 100vw;\n  }\n}\n\n.actions-card {\n  margin: 0;\n  border: none;\n  border-radius: 0;\n  box-shadow: none;\n}\n\n.actions-card .el-card__body {\n  padding: 15px 20px;\n}\n\n.selection-info {\n  color: #606266;\n  font-size: 14px;\n}\n\n.selection-info strong {\n  color: #409eff;\n}\n\n/* 头部信息样式 */\n.header-info {\n  color: #909399;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .main-content .el-col:first-child {\n    margin-bottom: 20px;\n  }\n\n  .info-row .el-col {\n    margin-bottom: 5px;\n  }\n\n  /* 调整小屏幕下的高度计算 */\n  .main-content {\n    height: calc(100vh - 280px);\n  }\n}\n\n@media (max-width: 768px) {\n  .main-content {\n    height: calc(100vh - 320px);\n  }\n\n  .review-actions-fixed .el-row .el-col {\n    margin-bottom: 10px;\n  }\n\n  .actions-card .el-card__body {\n    padding: 10px 15px;\n  }\n}\n\n/* 超大屏幕优化 */\n@media (min-width: 1920px) {\n  .main-content {\n    height: calc(100vh - 220px);\n  }\n}\n\n/* 通用样式 */\n.text-center {\n  text-align: center;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-table {\n  margin-bottom: 5px;\n}\n\n/* 表格优化 */\n.el-table .cell {\n  padding: 0 5px;\n}\n\n.el-table--mini td {\n  padding: 4px 0;\n}\n\n/* 徽章样式 */\n.el-badge {\n  margin-left: 5px;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAoTA,IAAAA,mBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,qBAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,gBAAA;MACA;MACAC,mBAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,YAAA;MACA;MACAC,gBAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,eAAA;MACA;MACAC,sBAAA;MACA;MACAC,oBAAA;MACA;MACAC,oBAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,OAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA;MACA;IACA;IACA,eACAC,YAAA,WAAAA,aAAA;MACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;IACA;IACA,KAAAC,kBAAA;IACA;IACAC,QAAA,CAAAH,gBAAA,0BAAAI,sBAAA;EACA;EACAC,OAAA;IACA,aACAZ,OAAA,WAAAA,QAAA;MAAA,IAAAa,KAAA;MACAC,OAAA,CAAAC,GAAA;QACAC,MAAA,OAAAjC,WAAA;QACAkC,IAAA,OAAA3B,WAAA,CAAAC,OAAA;QACA2B,IAAA,OAAA5B,WAAA,CAAAE,QAAA;QACA2B,IAAA,MAAAC,IAAA,GAAAC,kBAAA;QACAC,MAAA,OAAArD;MACA;;MAEA;MACA,IAAAsD,QAAA,OAAAC,mBAAA,CAAAC,OAAA,OAAAxD,GAAA;MACA6C,OAAA,CAAAC,GAAA;QAAAW,MAAA,EAAAH;MAAA;MAEA,KAAAvD,OAAA;MACA;MACA,KAAAoB,oBAAA;;MAEA;MACA,IAAAuC,MAAA,OAAAC,cAAA,CAAAH,OAAA,WAAAnC,WAAA;;MAEA;MACA,IAAAqC,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAAE,QAAA,GAAAF,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAA7D,IAAA,GAAA6D,MAAA,CAAAhC,OAAA;MACA;MAEA,IAAAmC,2CAAA,EAAAH,MAAA,EAAAI,IAAA,WAAAC,QAAA;QACAlB,OAAA,CAAAC,GAAA;UACAkB,IAAA,EAAAD,QAAA,CAAAE,IAAA,CAAAC,MAAA;UACAC,EAAA,EAAAJ,QAAA,CAAA1D,KAAA;UACA+D,MAAA,EAAAL,QAAA,CAAAE,IAAA,CAAAI,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,IAAA;UAAA;QACA;QAEA3B,KAAA,CAAAtC,gBAAA,GAAAyD,QAAA,CAAAE,IAAA;QACArB,KAAA,CAAAvC,KAAA,GAAA0D,QAAA,CAAA1D,KAAA;QACAuC,KAAA,CAAA7C,OAAA;;QAEA;QACA6C,KAAA,CAAA5C,GAAA,GAAAsD,QAAA;QAEAT,OAAA,CAAAC,GAAA;UACAW,MAAA,EAAAH,QAAA;UACAkB,KAAA,EAAA5B,KAAA,CAAA5C,GAAA;UACAyE,KAAA,EAAA7B,KAAA,CAAA5C,GAAA,CAAAkE,MAAA;UACAQ,OAAA,IAAA9B,KAAA,CAAA+B,KAAA,CAAAC,iBAAA;UACAC,QAAA,EAAAjC,KAAA,CAAA3B;QACA;;QAEA;QACA,IAAA2B,KAAA,CAAA3B,sBAAA;UACA4B,OAAA,CAAAC,GAAA;UACAF,KAAA,CAAAkC,wBAAA;UACAlC,KAAA,CAAA3B,sBAAA;QACA;UACA4B,OAAA,CAAAC,GAAA;UACA;UACAF,KAAA,CAAAmC,SAAA;YACAlC,OAAA,CAAAC,GAAA;YACA;YACAkC,UAAA;cACAnC,OAAA,CAAAC,GAAA;cACAF,KAAA,CAAAqC,oBAAA;YACA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAvC,KAAA,CAAA7C,OAAA;QACA6C,KAAA,CAAAzB,oBAAA;QACA0B,OAAA,CAAAsC,KAAA,uBAAAA,KAAA;MACA;IACA;IAEA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA/D,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IAEA,aACAsD,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IAEA,cACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA3C,OAAA,CAAAC,GAAA;QACA2C,IAAA,EAAAD,SAAA,CAAAtB,MAAA;QACAwB,IAAA,EAAAF,SAAA,CAAAnB,GAAA,WAAAC,IAAA;UAAA;YAAAC,IAAA,EAAAD,IAAA,CAAAC,IAAA;YAAA1E,IAAA,EAAAyE,IAAA,CAAAzE;UAAA;QAAA;QACA8F,IAAA,MAAAxC,IAAA,GAAAC,kBAAA;QACAwC,MAAA,OAAAzE;MACA;;MAEA;MACA,SAAAA,oBAAA;QACA0B,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,KAAA9C,GAAA,GAAAwF,SAAA,CAAAnB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA;MAAA;MACA,KAAAtE,qBAAA,GAAAuF,SAAA;MACA,KAAAtF,MAAA,GAAAsF,SAAA,CAAAtB,MAAA;MACA,KAAA/D,QAAA,IAAAqF,SAAA,CAAAtB,MAAA;MACArB,OAAA,CAAAC,GAAA,kBAAA3C,QAAA;MACA0C,OAAA,CAAAC,GAAA,qBAAA0C,SAAA,CAAAtB,MAAA;MAEArB,OAAA,CAAAC,GAAA;QACA9C,GAAA,OAAAA,GAAA;QACAE,MAAA,OAAAA,MAAA;QACAC,QAAA,OAAAA;MACA;;MAEA;MACA,KAAA0F,iBAAA;IACA;IAEA,YACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAAxF,mBAAA,GAAAwF,GAAA;MACA,KAAAC,yBAAA,CAAAD,GAAA,CAAAxB,IAAA;IACA;IAEA,aACAyB,yBAAA,WAAAA,0BAAAzB,IAAA;MAAA,IAAA0B,MAAA;MACA,IAAAC,yCAAA,EAAA3B,IAAA,EAAAT,IAAA,WAAAC,QAAA;QACAkC,MAAA,CAAA1F,mBAAA,GAAAwD,QAAA,CAAAjE,IAAA,CAAAqG,YAAA;QACAF,MAAA,CAAAzF,cAAA,GAAAuD,QAAA,CAAAjE,IAAA,CAAAsG,WAAA;QACAH,MAAA,CAAAxF,eAAA,GAAAsD,QAAA,CAAAjE,IAAA,CAAAW,eAAA;QACAwF,MAAA,CAAAvF,aAAA,GAAAqD,QAAA,CAAAjE,IAAA,CAAAY,aAAA;MACA;IACA;IAEA,aACAsB,iBAAA,WAAAA,kBAAA;MAAA,IAAAqE,MAAA;MACA,IAAAC,kCAAA,IAAAxC,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAA1F,cAAA,GAAAoD,QAAA,CAAAjE,IAAA;MACA;IACA;IAEA,eACAmC,kBAAA,WAAAA,mBAAA;MAAA,IAAAsE,MAAA;MACA,IAAAC,mCAAA,IAAA1C,IAAA,WAAAC,QAAA;QACAwC,MAAA,CAAA3F,YAAA,GAAAmD,QAAA,CAAAjE,IAAA;MACA;IACA;IAEA,aACAoC,gBAAA,WAAAA,iBAAA;MAAA,IAAAuE,MAAA;MACA,IAAAC,kCAAA,IAAA5C,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAAzF,eAAA,GAAA+C,QAAA,CAAAjE,IAAA;MACA;IACA;IAEA,aACA6G,iBAAA,WAAAA,kBAAAC,KAAA;MAAA,IAAAC,MAAA;MACAhE,OAAA,CAAAC,GAAA;QACAgE,EAAA,EAAAF,KAAA;QACAG,IAAA,OAAA/F,eAAA;QACAgG,MAAA,OAAAhH,GAAA,CAAAkE;MACA;MAEA,IAAA0C,KAAA;QACA,KAAA7F,YAAA,GAAAkG,WAAA;UACApE,OAAA,CAAAC,GAAA;YACA6C,IAAA,MAAAxC,IAAA,GAAAC,kBAAA;YACAC,MAAA,EAAAwD,MAAA,CAAA7G;UACA;;UAEA;UACA6G,MAAA,CAAA5F,sBAAA;UACA4F,MAAA,CAAA9E,OAAA;QACA,QAAAf,eAAA;MACA;QACA,SAAAD,YAAA;UACAmG,aAAA,MAAAnG,YAAA;UACA,KAAAA,YAAA;QACA;MACA;IACA;IAEA,WACAoG,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAApH,GAAA,CAAAkE,MAAA;QACA,KAAAmD,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAD,MAAA,CAAAE,OAAA,mBAAAzD,IAAA;QACA,WAAA0D,wCAAA,EAAAJ,MAAA,CAAApH,GAAA;MACA,GAAA8D,IAAA;QACAsD,MAAA,CAAArF,OAAA;QACAqF,MAAA,CAAAK,cAAA;QACAL,MAAA,CAAAC,MAAA,CAAAK,UAAA;MACA,GAAAxC,KAAA;IACA;IAEA,WACAyC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAA5H,GAAA,CAAAkE,MAAA;QACA,KAAAmD,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,SAAAzG,gBAAA,CAAAqD,MAAA;QACA,KAAAmD,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAD,MAAA,CAAAE,OAAA,iBAAAzD,IAAA;QACA,IAAAhE,IAAA;UACA+H,KAAA,EAAAD,MAAA,CAAA5H,GAAA;UACA8H,YAAA,EAAAF,MAAA,CAAA/G;QACA;QACA,WAAAkH,uCAAA,EAAAjI,IAAA;MACA,GAAAgE,IAAA;QACA8D,MAAA,CAAA7F,OAAA;QACA6F,MAAA,CAAA/G,gBAAA;QACA+G,MAAA,CAAAH,cAAA;QACAG,MAAA,CAAAP,MAAA,CAAAK,UAAA;MACA,GAAAxC,KAAA;IACA;IAEA,mBACA8C,yBAAA,WAAAA,0BAAAC,KAAA;MACA,YAAAxH,eAAA,CAAAyH,MAAA,WAAA5D,IAAA;QAAA,OAAAA,IAAA,CAAA6D,KAAA,KAAAF,KAAA;MAAA;IACA;IAEA,eACAG,eAAA,WAAAA,gBAAAH,KAAA;MACA,QAAAA,KAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA,eACAI,eAAA,WAAAA,gBAAAJ,KAAA;MACA,OAAAA,KAAA;IACA;IAEA,WACAK,YAAA,WAAAA,aAAAC,SAAA;MACA,KAAAA,SAAA;MACA,IAAAC,KAAA,OAAArF,IAAA,CAAAoF,SAAA;MACA,IAAAE,GAAA,OAAAtF,IAAA;MACA,IAAAuF,GAAA,GAAAD,GAAA,CAAAE,WAAA,KAAAH,KAAA,CAAAG,WAAA;MACA,OAAAD,GAAA;IACA;IAEA,kBACA7C,iBAAA,WAAAA,kBAAA;MACA,IAAA+C,aAAA,QAAA5I,GAAA;MACA6C,OAAA,CAAAC,GAAA;QACA+F,OAAA,EAAAD,aAAA;QACAE,EAAA,EAAAF,aAAA,CAAA1E,MAAA;QACA6E,IAAA,MAAA5F,IAAA,GAAAC,kBAAA;MACA;MAEA4F,YAAA,CAAAC,OAAA,iCAAAC,IAAA,CAAAC,SAAA,CAAAP,aAAA;;MAEA;MACA,IAAAQ,KAAA,GAAAJ,YAAA,CAAAK,OAAA;MACAxG,OAAA,CAAAC,GAAA;QACAwG,KAAA,EAAAF,KAAA;QACAG,GAAA,EAAAL,IAAA,CAAAM,KAAA,CAAAJ,KAAA;MACA;IACA;IAEA,kBACAjH,iBAAA,WAAAA,kBAAA;MACAU,OAAA,CAAAC,GAAA;MAEA;QACA,IAAA2G,cAAA,GAAAT,YAAA,CAAAK,OAAA;QACAxG,OAAA,CAAAC,GAAA;UACA4G,IAAA,EAAAD,cAAA;UACAE,IAAA,MAAAC,QAAA,CAAApG,OAAA,EAAAiG,cAAA;QACA;QAEA,IAAAA,cAAA;UACA,IAAAI,SAAA,GAAAX,IAAA,CAAAM,KAAA,CAAAC,cAAA;UACA,KAAAzJ,GAAA,GAAA6J,SAAA;UACA,KAAA1J,QAAA,QAAAH,GAAA,CAAAkE,MAAA;UACA,KAAAhE,MAAA,QAAAF,GAAA,CAAAkE,MAAA;UAEArB,OAAA,CAAAC,GAAA;YACAgH,MAAA,OAAA9J,GAAA;YACA8I,EAAA,OAAA9I,GAAA,CAAAkE,MAAA;YACA/D,QAAA,OAAAA,QAAA;YACAD,MAAA,OAAAA;UACA;QACA;UACA2C,OAAA,CAAAC,GAAA;UACA,KAAA9C,GAAA;QACA;MACA,SAAAmF,KAAA;QACAtC,OAAA,CAAAsC,KAAA,uBAAAA,KAAA;QACA,KAAAnF,GAAA;MACA;IACA;IAEA,eACAiF,oBAAA,WAAAA,qBAAA;MAAA,IAAA8E,MAAA;MACAlH,OAAA,CAAAC,GAAA;QACAkH,QAAA,OAAAhK,GAAA;QACAyE,KAAA,OAAAzE,GAAA,CAAAkE,MAAA;QACAQ,OAAA,SAAAC,KAAA,CAAAC,iBAAA;QACAqF,MAAA,OAAA3J,gBAAA,CAAA4D,MAAA;QACAgG,MAAA,OAAA5J,gBAAA,CAAA+D,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,IAAA;QAAA;MACA;;MAEA;MACA,SAAAvE,GAAA,CAAAkE,MAAA;QACArB,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,UAAA6B,KAAA,CAAAC,iBAAA;QACA/B,OAAA,CAAAsC,KAAA;QACA;MACA;MAEA,SAAA7E,gBAAA,CAAA4D,MAAA;QACArB,OAAA,CAAAC,GAAA;QACA;MACA;MAEA;QACA;QACAD,OAAA,CAAAC,GAAA;QACA,KAAA6B,KAAA,CAAAC,iBAAA,CAAA6C,cAAA;;QAEA;QACA,IAAA0C,aAAA;QACA,IAAAC,WAAA;QACA,IAAAC,YAAA;QAEA,KAAA/J,gBAAA,CAAAgK,OAAA,WAAAvE,GAAA;UACA,IAAAgE,MAAA,CAAA/J,GAAA,CAAAuK,QAAA,CAAAxE,GAAA,CAAAxB,IAAA;YACA1B,OAAA,CAAAC,GAAA,kFAAA0H,MAAA,CAAAzE,GAAA,CAAAxB,IAAA,SAAAiG,MAAA,CAAAzE,GAAA,CAAAlG,IAAA;YACAkK,MAAA,CAAApF,KAAA,CAAAC,iBAAA,CAAA6F,kBAAA,CAAA1E,GAAA;YACAoE,aAAA;YACAC,WAAA,CAAAM,IAAA;cAAAnG,IAAA,EAAAwB,GAAA,CAAAxB,IAAA;cAAA1E,IAAA,EAAAkG,GAAA,CAAAlG;YAAA;UACA;QACA;;QAEA;QACA,KAAAG,GAAA,CAAAsK,OAAA,WAAAK,EAAA;UACA,KAAAZ,MAAA,CAAAzJ,gBAAA,CAAAsK,IAAA,WAAA7E,GAAA;YAAA,OAAAA,GAAA,CAAAxB,IAAA,KAAAoG,EAAA;UAAA;YACAN,YAAA,CAAAK,IAAA,CAAAC,EAAA;UACA;QACA;;QAEA;QACA,KAAAxK,QAAA,QAAAH,GAAA,CAAAkE,MAAA;QACA,KAAAhE,MAAA,QAAAF,GAAA,CAAAkE,MAAA;QAEArB,OAAA,CAAAC,GAAA;UACA+H,MAAA,EAAAV,aAAA;UACAW,KAAA,EAAAV,WAAA;UACAW,MAAA,EAAAV,YAAA;UACAW,IAAA;YACA7K,QAAA,OAAAA,QAAA;YACAD,MAAA,OAAAA,MAAA;YACAF,GAAA,OAAAA;UACA;QACA;;QAEA;QACA,KAAA+E,SAAA;UACA,IAAAkG,YAAA,GAAAlB,MAAA,CAAApF,KAAA,CAAAC,iBAAA,CAAAY,SAAA;UACA3C,OAAA,CAAAC,GAAA;YACAoI,MAAA,EAAAD,YAAA,CAAA/G,MAAA;YACAiH,MAAA,EAAAF,YAAA,CAAA5G,GAAA,WAAA0B,GAAA;cAAA,OAAAA,GAAA,CAAAxB,IAAA;YAAA;YACA6G,MAAA,EAAArB,MAAA,CAAA/J,GAAA,CAAAkE,MAAA;YACAmH,IAAA,EAAAJ,YAAA,CAAA/G,MAAA,KAAAiG;UACA;UAEA,IAAAc,YAAA,CAAA/G,MAAA,KAAAiG,aAAA;YACAtH,OAAA,CAAAyI,IAAA;YACA;YACAtG,UAAA;cACA+E,MAAA,CAAAwB,yBAAA;YACA;UACA;YACA;YACA1I,OAAA,CAAAC,GAAA;YACAiH,MAAA,CAAA5I,oBAAA;UACA;QACA;MAEA,SAAAgE,KAAA;QACAtC,OAAA,CAAAsC,KAAA,uBAAAA,KAAA;MACA;IACA;IAEA,WACAsC,cAAA,WAAAA,eAAA;MAAA,IAAA+D,MAAA;MACA;MACA,KAAArK,oBAAA;MAEA,KAAAnB,GAAA;MACA,KAAAC,qBAAA;MACA,KAAAE,QAAA;MACA,KAAAD,MAAA;;MAEA;MACA,SAAAyE,KAAA,CAAAC,iBAAA;QACA,KAAAD,KAAA,CAAAC,iBAAA,CAAA6C,cAAA;MACA;;MAEA;MACAuB,YAAA,CAAAyC,UAAA;MAEA5I,OAAA,CAAAC,GAAA;QACA4I,IAAA,MAAAvI,IAAA,GAAAC,kBAAA;MACA;;MAEA;MACA4B,UAAA;QACAwG,MAAA,CAAArK,oBAAA;MACA;IACA;IAEA,eACAoK,yBAAA,WAAAA,0BAAA;MAAA,IAAAI,MAAA;MACA9I,OAAA,CAAAC,GAAA;MAEA,UAAA6B,KAAA,CAAAC,iBAAA;QACA/B,OAAA,CAAAsC,KAAA;QACA,KAAAhE,oBAAA;QACA;MACA;;MAEA;MACA,KAAAwD,KAAA,CAAAC,iBAAA,CAAA6C,cAAA;;MAEA;MACA,IAAAmE,UAAA;MACA,KAAAtL,gBAAA,CAAAgK,OAAA,WAAAvE,GAAA;QACA,IAAA4F,MAAA,CAAA3L,GAAA,CAAAuK,QAAA,CAAAxE,GAAA,CAAAxB,IAAA;UACAoH,MAAA,CAAAhH,KAAA,CAAAC,iBAAA,CAAA6F,kBAAA,CAAA1E,GAAA;UACA6F,UAAA;UACA/I,OAAA,CAAAC,GAAA,kFAAA0H,MAAA,CAAAzE,GAAA,CAAAxB,IAAA;QACA;MACA;MAEA1B,OAAA,CAAAC,GAAA,qEAAA0H,MAAA,CAAAoB,UAAA;;MAEA;MACA,KAAAzK,oBAAA;IACA;IAEA,iBACA2D,wBAAA,WAAAA,yBAAA;MAAA,IAAA+G,OAAA;MACAhJ,OAAA,CAAAC,GAAA;;MAEA;MACA,KAAAiC,SAAA;QACAC,UAAA;UACAnC,OAAA,CAAAC,GAAA;UACA+I,OAAA,CAAA5G,oBAAA;;UAEA;UACAD,UAAA;YAAA,IAAA8G,qBAAA;YACA,IAAAb,YAAA,KAAAa,qBAAA,GAAAD,OAAA,CAAAlH,KAAA,CAAAC,iBAAA,cAAAkH,qBAAA,uBAAAA,qBAAA,CAAAtG,SAAA;YACA,IAAAyF,YAAA,CAAA/G,MAAA,UAAA2H,OAAA,CAAA7L,GAAA,CAAAkE,MAAA;cACArB,OAAA,CAAAyI,IAAA;cACAO,OAAA,CAAAN,yBAAA;YACA;cACA;cACA1I,OAAA,CAAAC,GAAA;cACA+I,OAAA,CAAA1K,oBAAA;YACA;UACA;QACA;MACA;IACA;IAEA,eACAoB,YAAA,WAAAA,aAAA;MAAA,IAAAwJ,OAAA;MACA;MACA,KAAAhH,SAAA;QACA,IAAAgH,OAAA,CAAApH,KAAA,CAAAC,iBAAA;UACAmH,OAAA,CAAApH,KAAA,CAAAC,iBAAA,CAAAoH,QAAA;QACA;MACA;IACA;IAEA,cACAxJ,kBAAA,WAAAA,mBAAA;MAAA,IAAAyJ,OAAA;MACA;MACA,IAAAC,IAAA,GAAAzJ,QAAA,CAAAyJ,IAAA;MACA,IAAAA,IAAA,CAAAC,SAAA,CAAAC,QAAA;QACA;QACA,KAAAC,uBAAA;MACA;QACA;QACA,KAAAA,uBAAA;MACA;;MAEA;MACA,IAAAC,QAAA,OAAAC,gBAAA,WAAAC,SAAA;QACAA,SAAA,CAAAlC,OAAA,WAAAmC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,qBAAAD,QAAA,CAAAE,aAAA;YACA,IAAAC,QAAA,GAAAV,IAAA,CAAAC,SAAA,CAAAC,QAAA;YACAH,OAAA,CAAAI,uBAAA,CAAAO,QAAA;UACA;QACA;MACA;MAEAN,QAAA,CAAAO,OAAA,CAAAX,IAAA;QACAY,UAAA;QACAC,eAAA;MACA;;MAEA;MACA,KAAAC,eAAA,GAAAV,QAAA;IACA;IAEA,eACAD,uBAAA,WAAAA,wBAAAO,QAAA;MACA,IAAAK,SAAA,GAAAxK,QAAA,CAAAyK,aAAA;MACA,IAAAD,SAAA;QACA,IAAAL,QAAA;UACAK,SAAA,CAAAE,KAAA,CAAAC,IAAA;UACAH,SAAA,CAAAE,KAAA,CAAAE,QAAA;QACA;UACAJ,SAAA,CAAAE,KAAA,CAAAC,IAAA;UACAH,SAAA,CAAAE,KAAA,CAAAE,QAAA;QACA;MACA;IACA;IAEA,gBACA3K,sBAAA,WAAAA,uBAAA;MAAA,IAAA4K,OAAA;MACA,IAAA7K,QAAA,CAAA8K,MAAA;QACA1K,OAAA,CAAAC,GAAA;QACA;QACA,SAAA/B,YAAA;UACAmG,aAAA,MAAAnG,YAAA;UACA,KAAAA,YAAA;UACA,KAAAG,oBAAA,QAAAJ,WAAA;QACA;MACA;QACA+B,OAAA,CAAAC,GAAA;QACA;QACA,SAAA5B,oBAAA,SAAAJ,WAAA;UACA,KAAA6F,iBAAA;QACA;;QAEA;QACA,SAAA3G,GAAA,CAAAkE,MAAA;UACArB,OAAA,CAAAC,GAAA;UACAkC,UAAA;YACAsI,OAAA,CAAArI,oBAAA;UACA;QACA;MACA;IACA;EACA;EACAuI,aAAA,WAAAA,cAAA;IACA3K,OAAA,CAAAC,GAAA;IAEA,SAAA/B,YAAA;MACAmG,aAAA,MAAAnG,YAAA;MACA8B,OAAA,CAAAC,GAAA;IACA;;IAEA;IACAT,MAAA,CAAAoL,mBAAA,gBAAAlL,YAAA;IACAE,QAAA,CAAAgL,mBAAA,0BAAA/K,sBAAA;IACAG,OAAA,CAAAC,GAAA;;IAEA;IACA,SAAAkK,eAAA;MACA,KAAAA,eAAA,CAAAU,UAAA;MACA7K,OAAA,CAAAC,GAAA;IACA;IAEAD,OAAA,CAAAC,GAAA;EACA;AACA", "ignoreList": []}]}