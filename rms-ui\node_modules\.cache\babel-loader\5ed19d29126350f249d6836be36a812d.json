{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752933907771}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\babel.config.js", "mtime": 1748394285000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_prescriptionreview", "require", "name", "data", "loading", "ids", "selectedPrescriptions", "single", "multiple", "showSearch", "total", "prescriptionList", "currentPrescription", "medicationList", "analysisResults", "reviewHistory", "departmentList", "problemTypes", "selectedProblems", "autoRefresh", "refreshTimer", "refreshInterval", "activeTab", "queryParams", "pageNum", "pageSize", "deptCode", "prescriptionType", "keyword", "created", "getList", "getDepartmentList", "getProblemTypeList", "getRefreshConfig", "loadSelectedState", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "_this", "params", "_objectSpread2", "default", "doctName", "getPendingPrescriptions", "then", "response", "rows", "$nextTick", "restoreSelectedState", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "code", "length", "saveSelectedState", "handleRowClick", "row", "getPrescriptionDetailData", "_this2", "getPrescriptionDetail", "prescription", "medications", "_this3", "getDepartments", "_this4", "getProblemTypes", "_this5", "getRefreshTime", "toggleAutoRefresh", "value", "_this6", "setInterval", "handleApprove", "_this7", "$modal", "msgError", "confirm", "approvePrescriptions", "msgSuccess", "catch", "handleReject", "_this8", "codes", "problemNames", "rejectPrescriptions", "getAnalysisResultsByLevel", "level", "filter", "wtlvl", "getSeverityType", "getSeverityText", "calculateAge", "birthDate", "birth", "Date", "now", "age", "getFullYear"], "sources": ["src/views/rms/prescription/review/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item label=\"科室\" prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"关键字\" prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"医生姓名或患者姓名\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 工具栏 -->\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"自动刷新\"\n          inactive-text=\"手动刷新\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <!-- 主要内容区域 -->\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 处方列表 -->\n      <el-col :span=\"9\">\n        <el-card class=\"list-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>待审核处方列表</span>\n            <span class=\"header-info\">（共 {{ total }} 条）</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"prescriptionList\"\n            @selection-change=\"handleSelectionChange\"\n            @row-click=\"handleRowClick\"\n            highlight-current-row\n            height=\"calc(100vh - 320px)\"\n            ref=\"prescriptionTable\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"100\" show-overflow-tooltip />\n            <el-table-column label=\"医生\" align=\"center\" prop=\"doctName\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"患者\" align=\"center\" prop=\"name\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"严重程度\" align=\"center\" width=\"90\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                  {{ getSeverityText(scope.row.level) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n            class=\"pagination-container\"\n          />\n        </el-card>\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"15\">\n        <el-card class=\"detail-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n            <span class=\"header-info\">（{{ currentPrescription.code }}）</span>\n          </div>\n\n          <div class=\"detail-content\" style=\"height: calc(100vh - 320px); overflow-y: auto;\">\n            <!-- 基本信息 - 紧凑布局 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">处方号：</span>{{ currentPrescription.presId }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"16\"><span class=\"label\">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 诊断信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">诊断信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 医生和患者信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">医生和患者信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">科室：</span>{{ currentPrescription.deptName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">医生：</span>{{ currentPrescription.doctName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">患者：</span>{{ currentPrescription.name }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"6\"><span class=\"label\">性别：</span>{{ currentPrescription.sex }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">年龄：</span>{{ calculateAge(currentPrescription.birth) }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">身高：</span>{{ currentPrescription.height || '未知' }}cm</el-col>\n                <el-col :span=\"6\"><span class=\"label\">体重：</span>{{ currentPrescription.weight || '未知' }}kg</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 药品明细 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">药品明细</h4>\n              <el-table :data=\"medicationList\" size=\"mini\" border max-height=\"200\">\n                <el-table-column label=\"药名\" prop=\"medName\" min-width=\"120\" show-overflow-tooltip />\n                <el-table-column label=\"组号\" prop=\"group\" width=\"50\" />\n                <el-table-column label=\"规格\" prop=\"spec\" width=\"80\" show-overflow-tooltip />\n                <el-table-column label=\"给药途径\" prop=\"administer\" width=\"70\" />\n                <el-table-column label=\"单次量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.dose }}{{ scope.row.doseUnit }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"频次\" prop=\"freq\" width=\"50\" />\n                <el-table-column label=\"天数\" prop=\"days\" width=\"50\" />\n                <el-table-column label=\"开药数量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.ordQty }}{{ scope.row.ordUom }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"金额\" prop=\"money\" width=\"60\" />\n                <el-table-column label=\"用药说明\" prop=\"yysm\" min-width=\"100\" show-overflow-tooltip />\n              </el-table>\n            </div>\n\n            <!-- 处方分析结果 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方分析结果</h4>\n              <el-tabs v-model=\"activeTab\" type=\"card\" class=\"compact-tabs\">\n                <el-tab-pane name=\"important\">\n                  <span slot=\"label\">\n                    重要问题 <el-badge :value=\"getAnalysisResultsByLevel('重要').length\" v-if=\"getAnalysisResultsByLevel('重要').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"general\">\n                  <span slot=\"label\">\n                    一般问题 <el-badge :value=\"getAnalysisResultsByLevel('一般').length\" v-if=\"getAnalysisResultsByLevel('一般').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"other\">\n                  <span slot=\"label\">\n                    其它问题 <el-badge :value=\"getAnalysisResultsByLevel('其它').length\" v-if=\"getAnalysisResultsByLevel('其它').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n              </el-tabs>\n            </div>\n\n            <!-- 历史审核记录 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">历史审核记录</h4>\n              <el-table :data=\"reviewHistory\" size=\"mini\" border max-height=\"120\">\n                <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"80\" />\n                <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"130\">\n                  <template slot-scope=\"scope\">\n                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"审核意见\" prop=\"text\" min-width=\"150\" show-overflow-tooltip />\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n        <el-card v-else class=\"detail-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 固定在底部的审核操作区域 -->\n    <div class=\"review-actions-fixed\">\n      <el-card class=\"actions-card\">\n        <el-row :gutter=\"15\" align=\"middle\" type=\"flex\">\n          <el-col :span=\"8\">\n            <el-select\n              v-model=\"selectedProblems\"\n              multiple\n              placeholder=\"选择问题类型\"\n              style=\"width: 100%\"\n              size=\"small\"\n              collapse-tags>\n              <el-option\n                v-for=\"problem in problemTypes\"\n                :key=\"problem.cfwtbh\"\n                :label=\"problem.cfwtname\"\n                :value=\"problem.cfwtname\">\n              </el-option>\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"danger\"\n              icon=\"el-icon-close\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleReject\"\n              v-hasPermi=\"['rms:prescription:review:reject']\"\n              style=\"width: 100%\">\n              审核打回\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-check\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleApprove\"\n              v-hasPermi=\"['rms:prescription:review:approve']\"\n              style=\"width: 100%\">\n              审核通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"8\" class=\"text-right\">\n            <span class=\"selection-info\">\n              已选择 <strong>{{ ids.length }}</strong> 条处方\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"clearSelection\"\n                v-if=\"ids.length > 0\">\n                清空选择\n              </el-button>\n            </span>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 选中的处方对象数组（用于状态持久化）\n      selectedPrescriptions: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n    this.loadSelectedState()\n  },\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n    }\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      this.loading = true\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n\n        // 恢复选择状态\n        this.$nextTick(() => {\n          this.restoreSelectedState()\n        })\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.code)\n      this.selectedPrescriptions = selection\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n\n      // 保存选择状态到本地存储\n      this.saveSelectedState()\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.box-card {\n  height: 600px;\n  overflow-y: auto;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-descriptions {\n  margin-bottom: 20px;\n}\n\n.el-table {\n  margin-bottom: 10px;\n}\n\nh4 {\n  margin: 20px 0 10px 0;\n  color: #303133;\n  font-weight: 500;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;AAyTA,IAAAA,mBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,qBAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,gBAAA;MACA;MACAC,mBAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,YAAA;MACA;MACAC,gBAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,eAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,OAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,iBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAf,YAAA;MACAgB,aAAA,MAAAhB,YAAA;IACA;EACA;EACAiB,OAAA;IACA,aACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,KAAA;MACA,KAAAlC,OAAA;MACA;MACA,IAAAmC,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAlB,WAAA;;MAEA;MACA,IAAAgB,MAAA,CAAAX,OAAA;QACAW,MAAA,CAAAG,QAAA,GAAAH,MAAA,CAAAX,OAAA;QACAW,MAAA,CAAArC,IAAA,GAAAqC,MAAA,CAAAX,OAAA;MACA;MAEA,IAAAe,2CAAA,EAAAJ,MAAA,EAAAK,IAAA,WAAAC,QAAA;QACAP,KAAA,CAAA3B,gBAAA,GAAAkC,QAAA,CAAAC,IAAA;QACAR,KAAA,CAAA5B,KAAA,GAAAmC,QAAA,CAAAnC,KAAA;QACA4B,KAAA,CAAAlC,OAAA;;QAEA;QACAkC,KAAA,CAAAS,SAAA;UACAT,KAAA,CAAAU,oBAAA;QACA;MACA;IACA;IAEA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA1B,WAAA,CAAAC,OAAA;MACA,KAAAM,OAAA;IACA;IAEA,aACAoB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IAEA,cACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhD,GAAA,GAAAgD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA;MAAA;MACA,KAAAlD,qBAAA,GAAA+C,SAAA;MACA,KAAA9C,MAAA,GAAA8C,SAAA,CAAAI,MAAA;MACA,KAAAjD,QAAA,IAAA6C,SAAA,CAAAI,MAAA;;MAEA;MACA,KAAAC,iBAAA;IACA;IAEA,YACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAAhD,mBAAA,GAAAgD,GAAA;MACA,KAAAC,yBAAA,CAAAD,GAAA,CAAAJ,IAAA;IACA;IAEA,aACAK,yBAAA,WAAAA,0BAAAL,IAAA;MAAA,IAAAM,MAAA;MACA,IAAAC,yCAAA,EAAAP,IAAA,EAAAZ,IAAA,WAAAC,QAAA;QACAiB,MAAA,CAAAlD,mBAAA,GAAAiC,QAAA,CAAA1C,IAAA,CAAA6D,YAAA;QACAF,MAAA,CAAAjD,cAAA,GAAAgC,QAAA,CAAA1C,IAAA,CAAA8D,WAAA;QACAH,MAAA,CAAAhD,eAAA,GAAA+B,QAAA,CAAA1C,IAAA,CAAAW,eAAA;QACAgD,MAAA,CAAA/C,aAAA,GAAA8B,QAAA,CAAA1C,IAAA,CAAAY,aAAA;MACA;IACA;IAEA,aACAgB,iBAAA,WAAAA,kBAAA;MAAA,IAAAmC,MAAA;MACA,IAAAC,kCAAA,IAAAvB,IAAA,WAAAC,QAAA;QACAqB,MAAA,CAAAlD,cAAA,GAAA6B,QAAA,CAAA1C,IAAA;MACA;IACA;IAEA,eACA6B,kBAAA,WAAAA,mBAAA;MAAA,IAAAoC,MAAA;MACA,IAAAC,mCAAA,IAAAzB,IAAA,WAAAC,QAAA;QACAuB,MAAA,CAAAnD,YAAA,GAAA4B,QAAA,CAAA1C,IAAA;MACA;IACA;IAEA,aACA8B,gBAAA,WAAAA,iBAAA;MAAA,IAAAqC,MAAA;MACA,IAAAC,kCAAA,IAAA3B,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAAjD,eAAA,GAAAwB,QAAA,CAAA1C,IAAA;MACA;IACA;IAEA,aACAqE,iBAAA,WAAAA,kBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA;QACA,KAAArD,YAAA,GAAAuD,WAAA;UACAD,MAAA,CAAA5C,OAAA;QACA,QAAAT,eAAA;MACA;QACA,SAAAD,YAAA;UACAgB,aAAA,MAAAhB,YAAA;UACA,KAAAA,YAAA;QACA;MACA;IACA;IAEA,WACAwD,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAAxE,GAAA,CAAAoD,MAAA;QACA,KAAAqB,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAD,MAAA,CAAAE,OAAA,mBAAApC,IAAA;QACA,WAAAqC,wCAAA,EAAAJ,MAAA,CAAAxE,GAAA;MACA,GAAAuC,IAAA;QACAiC,MAAA,CAAA/C,OAAA;QACA+C,MAAA,CAAAC,MAAA,CAAAI,UAAA;MACA,GAAAC,KAAA;IACA;IAEA,WACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAhF,GAAA,CAAAoD,MAAA;QACA,KAAAqB,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,SAAA7D,gBAAA,CAAAuC,MAAA;QACA,KAAAqB,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAD,MAAA,CAAAE,OAAA,iBAAApC,IAAA;QACA,IAAAzC,IAAA;UACAmF,KAAA,EAAAD,MAAA,CAAAhF,GAAA;UACAkF,YAAA,EAAAF,MAAA,CAAAnE;QACA;QACA,WAAAsE,uCAAA,EAAArF,IAAA;MACA,GAAAyC,IAAA;QACAyC,MAAA,CAAAvD,OAAA;QACAuD,MAAA,CAAAnE,gBAAA;QACAmE,MAAA,CAAAP,MAAA,CAAAI,UAAA;MACA,GAAAC,KAAA;IACA;IAEA,mBACAM,yBAAA,WAAAA,0BAAAC,KAAA;MACA,YAAA5E,eAAA,CAAA6E,MAAA,WAAApC,IAAA;QAAA,OAAAA,IAAA,CAAAqC,KAAA,KAAAF,KAAA;MAAA;IACA;IAEA,eACAG,eAAA,WAAAA,gBAAAH,KAAA;MACA,QAAAA,KAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA,eACAI,eAAA,WAAAA,gBAAAJ,KAAA;MACA,OAAAA,KAAA;IACA;IAEA,WACAK,YAAA,WAAAA,aAAAC,SAAA;MACA,KAAAA,SAAA;MACA,IAAAC,KAAA,OAAAC,IAAA,CAAAF,SAAA;MACA,IAAAG,GAAA,OAAAD,IAAA;MACA,IAAAE,GAAA,GAAAD,GAAA,CAAAE,WAAA,KAAAJ,KAAA,CAAAI,WAAA;MACA,OAAAD,GAAA;IACA;EACA;AACA", "ignoreList": []}]}