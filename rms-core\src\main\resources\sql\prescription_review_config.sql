-- 处方审核模块系统参数配置和数据库优化
-- 插入处方刷新时间配置参数

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
VALUES ('处方审核刷新时间', 'rms.pres.refreshTime', '5', 'N', 'admin', NOW(), '处方审核页面自动刷新时间间隔，单位：秒，默认5秒')
ON DUPLICATE KEY UPDATE
config_value = VALUES(config_value),
update_by = 'admin',
update_time = NOW(),
remark = VALUES(remark);

-- 处方审核相关数据库索引优化
-- 为处方表添加审核相关索引

-- 处方状态索引（用于快速查询待审核处方）
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_flag ON rms_t_pres(flag);

-- 科室索引（用于科室筛选）
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_dept ON rms_t_pres(dept_code, dept_name);

-- 药品类型索引（用于药品类型筛选）
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_type ON rms_t_pres(prescription_type);

-- 医生姓名索引（用于医生姓名搜索）
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_doctor ON rms_t_pres(doct_name);

-- 患者姓名索引（用于患者姓名搜索）
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_patient ON rms_t_pres(name);

-- 处方时间索引（用于排序）
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_time ON rms_t_pres(pres_time);

-- 严重程度索引（用于排序）
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_level ON rms_t_pres(level);

-- 复合索引：状态+严重程度+时间（用于处方审核列表查询优化）
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_review ON rms_t_pres(flag, level, pres_time);

-- 处方明细表索引
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_med_code ON rms_t_pres_med(code);

-- 处方分析结果表索引
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_fx_code ON rms_t_pres_fx(code);
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_fx_level ON rms_t_pres_fx(wtlvl);

-- 处方审核记录表索引
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_sh_code ON rms_t_pres_sh(code);
CREATE INDEX IF NOT EXISTS idx_rms_t_pres_sh_time ON rms_t_pres_sh(create_time);
