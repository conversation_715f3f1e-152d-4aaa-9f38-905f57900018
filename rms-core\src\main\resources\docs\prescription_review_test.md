# 处方审核模块测试文档

## 功能概述

处方审核模块是合理用药系统的核心功能之一，主要包括：
- 待审核处方列表查询和筛选
- 处方详情查看（包含药品明细、分析结果、审核历史）
- 批量审核通过和打回操作
- 自动刷新机制

## 测试前准备

### 1. 数据库配置
执行以下SQL脚本初始化系统参数和索引：
```sql
-- 执行文件：rms-core/src/main/resources/sql/prescription_review_config.sql
```

### 2. 权限配置
确保用户具有以下权限：
- `rms:prescription:review:list` - 查看待审核处方列表
- `rms:prescription:review:query` - 查看处方详情
- `rms:prescription:review:approve` - 审核通过
- `rms:prescription:review:reject` - 审核打回

### 3. 测试数据准备
确保数据库中存在以下测试数据：
- `rms_t_pres` 表中有 `flag=10` 的处方记录
- `rms_t_pres_med` 表中有对应的药品明细
- `rms_t_pres_fx` 表中有分析结果
- `rms_cfwtlb` 表中有问题类型数据

## 测试用例

### 1. 待审核处方列表测试

#### 1.1 基本列表查询
- **测试步骤**：访问处方审核页面
- **预期结果**：显示所有 `flag=10` 的处方，按严重程度降序排列
- **验证点**：
  - 列表显示科室、医生姓名、患者姓名、严重程度
  - 严重程度标签颜色正确（重要-红色，一般-橙色，其它-灰色）
  - 分页功能正常

#### 1.2 科室筛选测试
- **测试步骤**：选择特定科室进行筛选
- **预期结果**：只显示该科室的处方
- **验证点**：筛选结果准确，清空筛选后恢复全部数据

#### 1.3 药品类型筛选测试
- **测试步骤**：选择"西药"、"中药"等类型筛选
- **预期结果**：只显示对应类型的处方
- **验证点**：筛选逻辑正确

#### 1.4 关键字搜索测试
- **测试步骤**：输入医生姓名或患者姓名进行搜索
- **预期结果**：显示匹配的处方记录
- **验证点**：模糊搜索功能正常

### 2. 处方详情查看测试

#### 2.1 基本信息展示
- **测试步骤**：点击处方列表中的任一行
- **预期结果**：右侧显示处方详细信息
- **验证点**：
  - 处方信息完整显示
  - 诊断信息正确
  - 医生和患者信息准确

#### 2.2 药品明细展示
- **测试步骤**：查看药品明细表格
- **预期结果**：显示所有药品信息
- **验证点**：药名、规格、用量、频次等信息完整

#### 2.3 分析结果展示
- **测试步骤**：切换"重要问题"、"一般问题"、"其它问题"标签页
- **预期结果**：按问题等级分类显示分析结果
- **验证点**：分类正确，详情信息完整

#### 2.4 审核历史展示
- **测试步骤**：查看历史审核记录
- **预期结果**：显示所有历史审核记录
- **验证点**：审核医师、时间、意见信息准确

### 3. 审核操作测试

#### 3.1 审核通过测试
- **测试步骤**：
  1. 选择一个或多个处方
  2. 点击"审核通过"按钮
  3. 确认操作
- **预期结果**：
  - 处方状态更新为 `flag=12`
  - 生成审核记录
  - 列表中移除已审核处方
- **验证点**：数据库状态正确更新

#### 3.2 审核打回测试
- **测试步骤**：
  1. 选择问题类型
  2. 选择一个或多个处方
  3. 点击"审核打回"按钮
  4. 确认操作
- **预期结果**：
  - 处方状态更新为 `flag=11`
  - 生成审核记录，包含问题描述
  - 列表中移除已审核处方
- **验证点**：问题类型正确记录

#### 3.3 批量操作测试
- **测试步骤**：同时选择多个处方进行审核
- **预期结果**：所有选中处方都被正确处理
- **验证点**：批量操作的事务一致性

### 4. 自动刷新测试

#### 4.1 刷新开关测试
- **测试步骤**：切换自动刷新开关
- **预期结果**：
  - 开启时定时刷新列表
  - 关闭时停止自动刷新
- **验证点**：刷新间隔符合系统配置

#### 4.2 刷新时间配置测试
- **测试步骤**：修改系统参数 `rms.pres.refreshTime`
- **预期结果**：刷新间隔相应改变
- **验证点**：配置生效

### 5. 异常情况测试

#### 5.1 权限测试
- **测试步骤**：使用无权限用户访问
- **预期结果**：显示权限不足提示
- **验证点**：权限控制有效

#### 5.2 数据异常测试
- **测试步骤**：处理不存在的处方编码
- **预期结果**：显示友好错误提示
- **验证点**：异常处理正确

#### 5.3 网络异常测试
- **测试步骤**：模拟网络中断
- **预期结果**：显示网络错误提示
- **验证点**：错误处理机制有效

## 性能测试

### 1. 列表查询性能
- **测试数据量**：1000+处方记录
- **预期响应时间**：< 2秒
- **验证点**：查询效率，索引使用情况

### 2. 批量操作性能
- **测试数据量**：同时处理50+处方
- **预期响应时间**：< 5秒
- **验证点**：批量更新效率

## 测试结果记录

| 测试用例 | 测试结果 | 问题描述 | 解决方案 |
|---------|---------|---------|---------|
| 基本列表查询 | ✅ | - | - |
| 科室筛选 | ✅ | - | - |
| 药品类型筛选 | ✅ | - | - |
| 关键字搜索 | ✅ | - | - |
| 处方详情展示 | ✅ | - | - |
| 审核通过操作 | ✅ | - | - |
| 审核打回操作 | ✅ | - | - |
| 自动刷新功能 | ✅ | - | - |

## 注意事项

1. 测试前确保数据库连接正常
2. 测试过程中注意观察浏览器控制台错误
3. 测试完成后清理测试数据
4. 记录性能测试数据用于后续优化

## 已知问题

1. 暂无已知问题

## 后续优化建议

1. 考虑添加处方审核统计功能
2. 优化大数据量下的查询性能
3. 增加审核流程的可配置性
