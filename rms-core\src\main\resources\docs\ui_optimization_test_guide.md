# 处方审核界面优化测试指南

## 测试环境准备

### 1. 浏览器要求
- **推荐浏览器**：Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **屏幕分辨率**：测试1920x1080, 1366x768, 1024x768等常见分辨率
- **缩放比例**：测试100%, 125%, 150%等不同缩放比例

### 2. 测试数据准备
- 确保数据库中有足够的测试数据（建议20+条处方记录）
- 准备不同严重程度的处方数据
- 确保有药品明细、分析结果、审核历史等完整数据

## 测试用例

### 1. 布局优化测试

#### 1.1 基本布局测试
**测试步骤：**
1. 打开处方审核页面
2. 观察左右两个区域的宽度比例
3. 检查处方详情区域是否能在一屏内显示完整内容

**预期结果：**
- 处方列表占约37.5%宽度（9列）
- 处方详情占约62.5%宽度（15列）
- 详情内容紧凑，减少垂直滚动需求

#### 1.2 响应式布局测试
**测试步骤：**
1. 调整浏览器窗口大小
2. 测试不同屏幕分辨率下的显示效果
3. 检查移动端显示效果

**预期结果：**
- 在不同屏幕尺寸下布局保持合理
- 小屏幕下内容不会被截断
- 移动端有适当的适配

### 2. 选择状态持久化测试

#### 2.1 基本持久化测试
**测试步骤：**
1. 在处方列表中选择几个处方（勾选复选框）
2. 手动刷新页面（F5或Ctrl+R）
3. 观察选择状态是否保持

**预期结果：**
- 页面刷新后，之前选中的处方仍然保持选中状态
- 底部显示的选中数量正确

#### 2.2 自动刷新持久化测试
**测试步骤：**
1. 开启自动刷新功能
2. 选择几个处方
3. 等待自动刷新触发
4. 观察选择状态是否保持

**预期结果：**
- 自动刷新后选择状态保持不变
- 新增的处方不会被自动选中

#### 2.3 跨页面持久化测试
**测试步骤：**
1. 在第一页选择几个处方
2. 切换到第二页
3. 再切换回第一页
4. 观察选择状态

**预期结果：**
- 切换页面后选择状态保持
- 分页操作不影响已选择的处方

#### 2.4 审核后清空测试
**测试步骤：**
1. 选择几个处方
2. 执行审核通过或审核打回操作
3. 观察选择状态是否被清空

**预期结果：**
- 审核操作完成后，选择状态自动清空
- 本地存储中的选择数据被清除

### 3. 固定审核区域测试

#### 3.1 固定定位测试
**测试步骤：**
1. 打开处方审核页面
2. 滚动页面内容
3. 观察底部审核区域是否始终可见

**预期结果：**
- 审核区域固定在页面底部
- 滚动页面时审核区域不会移动
- 审核区域不会遮挡主要内容

#### 3.2 审核功能测试
**测试步骤：**
1. 选择几个处方
2. 在问题类型下拉框中选择问题类型
3. 点击"审核打回"按钮
4. 确认操作

**预期结果：**
- 问题类型选择正常工作
- 审核按钮状态正确（选中处方后启用）
- 审核操作成功执行

#### 3.3 选择状态显示测试
**测试步骤：**
1. 逐个选择处方
2. 观察底部选择数量的变化
3. 点击"清空选择"按钮

**预期结果：**
- 选择数量实时更新
- "清空选择"按钮正常工作
- 清空后所有选择状态被重置

### 4. 详情区域优化测试

#### 4.1 信息展示测试
**测试步骤：**
1. 点击任意处方行
2. 观察右侧详情区域的信息展示
3. 检查各个信息区域的布局

**预期结果：**
- 处方信息、诊断信息、医生患者信息布局紧凑
- 信息显示完整，无截断
- 各区域之间有适当的分隔

#### 4.2 药品明细测试
**测试步骤：**
1. 选择有多个药品的处方
2. 观察药品明细表格的显示
3. 测试表格滚动功能

**预期结果：**
- 药品明细表格显示完整
- 表格高度限制在200px，超出时显示滚动条
- 表格列宽合理，内容不会挤压

#### 4.3 分析结果测试
**测试步骤：**
1. 选择有分析结果的处方
2. 切换不同的问题等级标签页
3. 观察问题数量徽章显示

**预期结果：**
- 标签页切换正常
- 问题数量徽章显示正确
- 不同等级的问题正确分类显示

#### 4.4 审核历史测试
**测试步骤：**
1. 选择有审核历史的处方
2. 观察审核历史表格显示
3. 检查时间格式和内容显示

**预期结果：**
- 审核历史按时间倒序显示
- 时间格式简洁（月-日 时:分）
- 审核意见完整显示

### 5. 性能测试

#### 5.1 加载性能测试
**测试步骤：**
1. 清除浏览器缓存
2. 打开处方审核页面
3. 记录页面加载时间

**预期结果：**
- 页面初始加载时间 < 3秒
- 数据刷新时间 < 2秒
- 界面响应流畅，无明显卡顿

#### 5.2 滚动性能测试
**测试步骤：**
1. 加载大量处方数据（50+条）
2. 快速滚动处方列表
3. 快速滚动处方详情区域

**预期结果：**
- 滚动流畅，无卡顿现象
- 表格渲染正常，无闪烁
- 内存使用稳定

#### 5.3 长时间使用测试
**测试步骤：**
1. 连续使用页面30分钟以上
2. 进行各种操作（选择、审核、刷新等）
3. 监控浏览器内存使用情况

**预期结果：**
- 内存使用保持稳定
- 无内存泄漏现象
- 页面响应速度不会随时间降低

### 6. 兼容性测试

#### 6.1 浏览器兼容性
**测试浏览器：**
- Chrome（最新版本和前两个版本）
- Firefox（最新版本和前两个版本）
- Safari（最新版本）
- Edge（最新版本）

**测试内容：**
- 基本功能是否正常
- 样式显示是否一致
- 交互操作是否流畅

#### 6.2 设备兼容性
**测试设备：**
- 桌面电脑（1920x1080, 1366x768）
- 笔记本电脑（1366x768, 1440x900）
- 平板设备（iPad, Android平板）
- 手机设备（iPhone, Android手机）

## 问题记录模板

### 问题报告格式
```
问题标题：[简短描述问题]
测试环境：[浏览器版本、屏幕分辨率、操作系统]
重现步骤：
1. [步骤1]
2. [步骤2]
3. [步骤3]

预期结果：[期望看到的结果]
实际结果：[实际看到的结果]
严重程度：[高/中/低]
截图：[如果有的话]
```

### 常见问题排查

#### 1. 选择状态不保持
- 检查浏览器是否禁用了localStorage
- 检查控制台是否有JavaScript错误
- 确认数据刷新后DOM是否正确更新

#### 2. 固定区域显示异常
- 检查CSS样式是否正确加载
- 确认z-index层级是否冲突
- 检查页面底部padding是否足够

#### 3. 布局显示问题
- 检查屏幕分辨率和缩放比例
- 确认CSS媒体查询是否生效
- 检查Flexbox和Grid布局支持

#### 4. 性能问题
- 检查网络连接状况
- 确认数据量是否过大
- 监控浏览器开发者工具的性能面板

## 测试完成标准

### 通过标准
- 所有核心功能正常工作
- 界面布局在主流浏览器和设备上显示正常
- 性能指标达到预期要求
- 用户体验明显改善

### 验收标准
- 布局优化：详情区域能在一屏内显示完整内容
- 状态持久化：选择状态在页面刷新后保持
- 固定定位：审核区域始终可见且不影响操作
- 响应式设计：在不同设备上都有良好的显示效果
