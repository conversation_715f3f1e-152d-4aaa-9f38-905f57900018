{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\layout\\components\\AppMain.vue?vue&type=style&index=0&id=078753dd&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\layout\\components\\AppMain.vue", "mtime": 1752943354833}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYXBwLW1haW4gewogIC8qIDUwPSBuYXZiYXIgIDUwICAqLwogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA1MHB4KTsKICB3aWR0aDogMTAwJTsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmFwcC1tYWluOmhhcyguY29weXJpZ2h0KSB7CiAgcGFkZGluZy1ib3R0b206IDM2cHg7Cn0KCi5maXhlZC1oZWFkZXIgKyAuYXBwLW1haW4gewogIHBhZGRpbmctdG9wOiA1MHB4Owp9CgouaGFzVGFnc1ZpZXcgewogIC5hcHAtbWFpbiB7CiAgICAvKiA4NCA9IG5hdmJhciArIHRhZ3MtdmlldyA9IDUwICsgMzQgKi8KICAgIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4NHB4KTsKICB9CgogIC5maXhlZC1oZWFkZXIgKyAuYXBwLW1haW4gewogICAgcGFkZGluZy10b3A6IDg0cHg7CiAgfQp9Cg=="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <section class=\"app-main\">\n    <transition name=\"fade-transform\" mode=\"out-in\">\n      <keep-alive :include=\"cachedViews\">\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\n      </keep-alive>\n    </transition>\n    <iframe-toggle />\n    <copyright />\n  </section>\n</template>\n\n<script>\nimport copyright from \"./Copyright/index\"\nimport iframeToggle from \"./IframeToggle/index\"\n\nexport default {\n  name: 'AppMain',\n  components: { iframeToggle, copyright },\n  computed: {\n    cachedViews() {\n      return this.$store.state.tagsView.cachedViews\n    },\n    key() {\n      return this.$route.path\n    }\n  },\n  watch: {\n    $route() {\n      this.addIframe()\n    }\n  },\n  mounted() {\n    this.addIframe()\n  },\n  methods: {\n    addIframe() {\n      const { name } = this.$route\n      if (name && this.$route.meta.link) {\n        this.$store.dispatch('tagsView/addIframeView', this.$route)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-main {\n  /* 50= navbar  50  */\n  min-height: calc(100vh - 50px);\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.app-main:has(.copyright) {\n  padding-bottom: 36px;\n}\n\n.fixed-header + .app-main {\n  padding-top: 50px;\n}\n\n.hasTagsView {\n  .app-main {\n    /* 84 = navbar + tags-view = 50 + 34 */\n    min-height: calc(100vh - 84px);\n  }\n\n  .fixed-header + .app-main {\n    padding-top: 84px;\n  }\n}\n</style>\n\n<style lang=\"scss\">\n// fix css style bug in open el-dialog\n.el-popup-parent--hidden {\n  .fixed-header {\n    padding-right: 6px;\n  }\n}\n\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background-color: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background-color: #c0c0c0;\n  border-radius: 3px;\n}\n</style>\n"]}]}