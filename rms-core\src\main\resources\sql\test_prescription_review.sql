-- 处方审核模块测试SQL脚本
-- 用于验证SQL语法修复是否有效

-- 1. 测试处方主表查询（包含reason字段）
SELECT code, hosp_code, dept_code, dept_name, doct_code, doct_name, 
       name, birth, sex, `reason`, pres_time, level, flag
FROM rms_t_pres 
WHERE flag = 10 
LIMIT 5;

-- 2. 测试处方药品明细查询（包含group和reason字段）
SELECT code, med_name, spec, `group`, `reason`, dose_unit, dose, 
       ord_qty, ord_uom, freq, administer, days, money
FROM rms_t_pres_med 
WHERE code IN (
    SELECT code FROM rms_t_pres WHERE flag = 10 LIMIT 1
);

-- 3. 测试处方分析结果查询
SELECT code, ywa, ywb, wtlvl, wtname, title, detail
FROM rms_t_pres_fx 
WHERE code IN (
    SELECT code FROM rms_t_pres WHERE flag = 10 LIMIT 1
);

-- 4. 测试处方审核记录查询
SELECT code, flag, text, nick_name, create_time
FROM rms_t_pres_sh 
WHERE code IN (
    SELECT code FROM rms_t_pres WHERE flag IN (11, 12) LIMIT 1
)
ORDER BY create_time DESC;

-- 5. 测试科室列表查询
SELECT DISTINCT dept_code, dept_name
FROM rms_t_pres 
WHERE dept_code IS NOT NULL AND dept_name IS NOT NULL
ORDER BY dept_name
LIMIT 10;

-- 6. 测试问题类型查询
SELECT cfwtbh, cfwtname
FROM rms_cfwtlb
ORDER BY cfwtbh
LIMIT 10;

-- 7. 测试按严重程度排序的处方查询
SELECT code, dept_name, doct_name, name, level, pres_time
FROM rms_t_pres 
WHERE flag = 10
ORDER BY 
    CASE 
        WHEN level = '重要' THEN 1
        WHEN level = '一般' THEN 2
        WHEN level = '其它' THEN 3
        ELSE 4
    END ASC,
    pres_time DESC
LIMIT 10;

-- 8. 测试批量更新处方状态（模拟审核通过）
-- 注意：这是测试SQL，实际使用时请谨慎
/*
UPDATE rms_t_pres 
SET flag = 12, update_time = NOW()
WHERE code IN ('TEST_CODE_1', 'TEST_CODE_2')
AND flag = 10;
*/

-- 9. 测试插入审核记录
-- 注意：这是测试SQL，实际使用时请谨慎
/*
INSERT INTO rms_t_pres_sh (code, flag, text, user_id, nick_name, create_time)
VALUES ('TEST_CODE_1', 12, '审核通过', 1, 'admin', NOW());
*/

-- 10. 验证系统参数配置
SELECT config_name, config_key, config_value, remark
FROM sys_config 
WHERE config_key = 'rms.pres.refreshTime';

-- 11. 检查索引是否创建成功
SHOW INDEX FROM rms_t_pres WHERE Key_name LIKE 'idx_rms_t_pres%';
SHOW INDEX FROM rms_t_pres_med WHERE Key_name LIKE 'idx_rms_t_pres_med%';
SHOW INDEX FROM rms_t_pres_fx WHERE Key_name LIKE 'idx_rms_t_pres_fx%';
SHOW INDEX FROM rms_t_pres_sh WHERE Key_name LIKE 'idx_rms_t_pres_sh%';

-- 12. 测试复杂查询（模拟前端筛选功能）
SELECT p.code, p.dept_name, p.doct_name, p.name, p.level, p.pres_time,
       COUNT(fx.id) as problem_count
FROM rms_t_pres p
LEFT JOIN rms_t_pres_fx fx ON p.code = fx.code
WHERE p.flag = 10
  AND p.dept_code = 'DEPT001'  -- 科室筛选
  AND p.prescription_type = '1'  -- 药品类型筛选（西药）
  AND (p.doct_name LIKE '%张%' OR p.name LIKE '%张%')  -- 关键字搜索
GROUP BY p.code, p.dept_name, p.doct_name, p.name, p.level, p.pres_time
ORDER BY 
    CASE 
        WHEN p.level = '重要' THEN 1
        WHEN p.level = '一般' THEN 2
        WHEN p.level = '其它' THEN 3
        ELSE 4
    END ASC,
    p.pres_time DESC
LIMIT 20;
