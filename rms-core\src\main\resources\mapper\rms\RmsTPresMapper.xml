<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTPresMapper">
    
    <resultMap type="RmsTPres" id="RmsTPresResult">
        <result property="code"    column="code"    />
        <result property="hospCode"    column="hosp_code"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="deptName"    column="dept_name"    />
        <result property="doctCode"    column="doct_code"    />
        <result property="doctName"    column="doct_name"    />
        <result property="doctType"    column="doct_type"    />
        <result property="doctTypeName"    column="doct_type_name"    />
        <result property="hisTime"    column="his_time"    />
        <result property="hospFlag"    column="hosp_flag"    />
        <result property="treatType"    column="treat_type"    />
        <result property="treatCode"    column="treat_code"    />
        <result property="bedNo"    column="bed_no"    />
        <result property="name"    column="name"    />
        <result property="birth"    column="birth"    />
        <result property="sex"    column="sex"    />
        <result property="weight"    column="weight"    />
        <result property="height"    column="height"    />
        <result property="idCard"    column="id_card"    />
        <result property="medicalRecord"    column="medical_record"    />
        <result property="cardType"    column="card_type"    />
        <result property="cardCode"    column="card_code"    />
        <result property="pregnantUnit"    column="pregnant_unit"    />
        <result property="pregnant"    column="pregnant"    />
        <result property="allInfo"    column="all_info"    />
        <result property="diaInfo"    column="dia_info"    />
        <result property="presId"    column="pres_id"    />
        <result property="reason"    column="reason"    />
        <result property="isUrgent"    column="is_urgent"    />
        <result property="isNew"    column="is_new"    />
        <result property="isCurrent"    column="is_current"    />
        <result property="presType"    column="pres_type"    />
        <result property="presTime"    column="pres_time"    />
        <result property="dischargeDrug"    column="discharge_drug"    />
        <result property="admType"    column="adm_type"    />
        <result property="requir"    column="requir"    />
        <result property="cs1"    column="cs1"    />
        <result property="ts"    column="ts"    />
        <result property="solvent"    column="solvent"    />
        <result property="jl"    column="jl"    />
        <result property="cs2"    column="cs2"    />
        <result property="lb"    column="lb"    />
        <result property="fs1"    column="fs1"    />
        <result property="fs2"    column="fs2"    />
        <result property="prescriptionType"    column="prescription_type"    />
        <result property="level"    column="level"    />
        <result property="flag"    column="flag"    />
        <result property="isReadDoc"    column="is_read_doc"    />
        <result property="isReadYs"    column="is_read_ys"    />
        <result property="text"    column="text"    />
        <result property="zyzb"    column="zyzb"    />
        <result property="zyzbCode"    column="zyzb_code"    />
        <result property="zyzz"    column="zyzz"    />
        <result property="zyzzCode"    column="zyzz_code"    />
        <result property="reason1"    column="reason1"    />
        <result property="presSm"    column="pres_sm"    />
        <result property="reason2"    column="reason2"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRmsTPresVo">
        select code, hosp_code, dept_code, dept_name, doct_code, doct_name, doct_type, doct_type_name, his_time, hosp_flag, treat_type, treat_code, bed_no, name, birth, sex, weight, height, id_card, medical_record, card_type, card_code, pregnant_unit, pregnant, all_info, dia_info, pres_id, reason, is_urgent, is_new, is_current, pres_type, pres_time, discharge_drug, adm_type, requir, cs1, ts, solvent, jl, cs2, lb, fs1, fs2, prescription_type, level, flag, is_read_doc, is_read_ys, text, zyzb, zyzb_code, zyzz, zyzz_code, reason1, pres_sm, reason2, create_time, update_time from rms_t_pres
    </sql>

    <select id="selectRmsTPresList" parameterType="RmsTPres" resultMap="RmsTPresResult">
        <include refid="selectRmsTPresVo"/>
        <where>
            <if test="hospCode != null  and hospCode != ''"> and hosp_code = #{hospCode}</if>
            <if test="deptCode != null  and deptCode != ''"> and dept_code = #{deptCode}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="doctCode != null  and doctCode != ''"> and doct_code = #{doctCode}</if>
            <if test="doctName != null  and doctName != ''"> and doct_name like concat('%', #{doctName}, '%')</if>
            <if test="doctType != null  and doctType != ''"> and doct_type = #{doctType}</if>
            <if test="doctTypeName != null  and doctTypeName != ''"> and doct_type_name like concat('%', #{doctTypeName}, '%')</if>
            <if test="hisTime != null "> and his_time = #{hisTime}</if>
            <if test="hospFlag != null  and hospFlag != ''"> and hosp_flag = #{hospFlag}</if>
            <if test="treatType != null  and treatType != ''"> and treat_type = #{treatType}</if>
            <if test="treatCode != null  and treatCode != ''"> and treat_code = #{treatCode}</if>
            <if test="bedNo != null  and bedNo != ''"> and bed_no = #{bedNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="birth != null  and birth != ''"> and birth = #{birth}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="weight != null  and weight != ''"> and weight = #{weight}</if>
            <if test="height != null  and height != ''"> and height = #{height}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="medicalRecord != null  and medicalRecord != ''"> and medical_record = #{medicalRecord}</if>
            <if test="cardType != null  and cardType != ''"> and card_type = #{cardType}</if>
            <if test="cardCode != null  and cardCode != ''"> and card_code = #{cardCode}</if>
            <if test="pregnantUnit != null  and pregnantUnit != ''"> and pregnant_unit = #{pregnantUnit}</if>
            <if test="pregnant != null  and pregnant != ''"> and pregnant = #{pregnant}</if>
            <if test="allInfo != null  and allInfo != ''"> and all_info = #{allInfo}</if>
            <if test="diaInfo != null  and diaInfo != ''"> and dia_info = #{diaInfo}</if>
            <if test="presId != null  and presId != ''"> and pres_id = #{presId}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="isUrgent != null  and isUrgent != ''"> and is_urgent = #{isUrgent}</if>
            <if test="isNew != null  and isNew != ''"> and is_new = #{isNew}</if>
            <if test="isCurrent != null  and isCurrent != ''"> and is_current = #{isCurrent}</if>
            <if test="presType != null  and presType != ''"> and pres_type = #{presType}</if>
            <if test="presTime != null "> and pres_time = #{presTime}</if>
            <if test="dischargeDrug != null  and dischargeDrug != ''"> and discharge_drug = #{dischargeDrug}</if>
            <if test="admType != null  and admType != ''"> and adm_type = #{admType}</if>
            <if test="requir != null  and requir != ''"> and requir = #{requir}</if>
            <if test="cs1 != null  and cs1 != ''"> and cs1 = #{cs1}</if>
            <if test="ts != null  and ts != ''"> and ts = #{ts}</if>
            <if test="solvent != null  and solvent != ''"> and solvent = #{solvent}</if>
            <if test="jl != null  and jl != ''"> and jl = #{jl}</if>
            <if test="cs2 != null  and cs2 != ''"> and cs2 = #{cs2}</if>
            <if test="lb != null  and lb != ''"> and lb = #{lb}</if>
            <if test="fs1 != null  and fs1 != ''"> and fs1 = #{fs1}</if>
            <if test="fs2 != null  and fs2 != ''"> and fs2 = #{fs2}</if>
            <if test="prescriptionType != null  and prescriptionType != ''"> and prescription_type = #{prescriptionType}</if>
            <if test="level != null  and level != ''"> and level = #{level}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="isReadDoc != null  and isReadDoc != ''"> and is_read_doc = #{isReadDoc}</if>
            <if test="isReadYs != null  and isReadYs != ''"> and is_read_ys = #{isReadYs}</if>
            <if test="text != null  and text != ''"> and text = #{text}</if>
            <if test="zyzb != null  and zyzb != ''"> and zyzb = #{zyzb}</if>
            <if test="zyzbCode != null  and zyzbCode != ''"> and zyzb_code = #{zyzbCode}</if>
            <if test="zyzz != null  and zyzz != ''"> and zyzz = #{zyzz}</if>
            <if test="zyzzCode != null  and zyzzCode != ''"> and zyzz_code = #{zyzzCode}</if>
            <if test="reason1 != null  and reason1 != ''"> and reason1 = #{reason1}</if>
            <if test="presSm != null  and presSm != ''"> and pres_sm = #{presSm}</if>
            <if test="reason2 != null  and reason2 != ''"> and reason2 = #{reason2}</if>
        </where>
        <!-- 处方审核专用排序：按严重程度降序，处方时间降序 -->
        <if test="flag != null and flag == 10">
            ORDER BY
            CASE
                WHEN level = '重要' THEN 1
                WHEN level = '一般' THEN 2
                WHEN level = '其它' THEN 3
                ELSE 4
            END ASC,
            pres_time DESC
        </if>
    </select>
    
    <select id="selectRmsTPresByCode" parameterType="String" resultMap="RmsTPresResult">
        <include refid="selectRmsTPresVo"/>
        where code = #{code}
    </select>

    <insert id="insertRmsTPres" parameterType="RmsTPres">
        insert into rms_t_pres
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="hospCode != null">hosp_code,</if>
            <if test="deptCode != null">dept_code,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="doctCode != null">doct_code,</if>
            <if test="doctName != null">doct_name,</if>
            <if test="doctType != null">doct_type,</if>
            <if test="doctTypeName != null">doct_type_name,</if>
            <if test="hisTime != null">his_time,</if>
            <if test="hospFlag != null">hosp_flag,</if>
            <if test="treatType != null">treat_type,</if>
            <if test="treatCode != null">treat_code,</if>
            <if test="bedNo != null">bed_no,</if>
            <if test="name != null">name,</if>
            <if test="birth != null">birth,</if>
            <if test="sex != null">sex,</if>
            <if test="weight != null">weight,</if>
            <if test="height != null">height,</if>
            <if test="idCard != null">id_card,</if>
            <if test="medicalRecord != null">medical_record,</if>
            <if test="cardType != null">card_type,</if>
            <if test="cardCode != null">card_code,</if>
            <if test="pregnantUnit != null">pregnant_unit,</if>
            <if test="pregnant != null">pregnant,</if>
            <if test="allInfo != null">all_info,</if>
            <if test="diaInfo != null">dia_info,</if>
            <if test="presId != null">pres_id,</if>
            <if test="reason != null">reason,</if>
            <if test="isUrgent != null">is_urgent,</if>
            <if test="isNew != null">is_new,</if>
            <if test="isCurrent != null">is_current,</if>
            <if test="presType != null">pres_type,</if>
            <if test="presTime != null">pres_time,</if>
            <if test="dischargeDrug != null">discharge_drug,</if>
            <if test="admType != null">adm_type,</if>
            <if test="requir != null">requir,</if>
            <if test="cs1 != null">cs1,</if>
            <if test="ts != null">ts,</if>
            <if test="solvent != null">solvent,</if>
            <if test="jl != null">jl,</if>
            <if test="cs2 != null">cs2,</if>
            <if test="lb != null">lb,</if>
            <if test="fs1 != null">fs1,</if>
            <if test="fs2 != null">fs2,</if>
            <if test="prescriptionType != null">prescription_type,</if>
            <if test="level != null">level,</if>
            <if test="flag != null">flag,</if>
            <if test="isReadDoc != null">is_read_doc,</if>
            <if test="isReadYs != null">is_read_ys,</if>
            <if test="text != null">text,</if>
            <if test="zyzb != null">zyzb,</if>
            <if test="zyzbCode != null">zyzb_code,</if>
            <if test="zyzz != null">zyzz,</if>
            <if test="zyzzCode != null">zyzz_code,</if>
            <if test="reason1 != null">reason1,</if>
            <if test="presSm != null">pres_sm,</if>
            <if test="reason2 != null">reason2,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="hospCode != null">#{hospCode},</if>
            <if test="deptCode != null">#{deptCode},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="doctCode != null">#{doctCode},</if>
            <if test="doctName != null">#{doctName},</if>
            <if test="doctType != null">#{doctType},</if>
            <if test="doctTypeName != null">#{doctTypeName},</if>
            <if test="hisTime != null">#{hisTime},</if>
            <if test="hospFlag != null">#{hospFlag},</if>
            <if test="treatType != null">#{treatType},</if>
            <if test="treatCode != null">#{treatCode},</if>
            <if test="bedNo != null">#{bedNo},</if>
            <if test="name != null">#{name},</if>
            <if test="birth != null">#{birth},</if>
            <if test="sex != null">#{sex},</if>
            <if test="weight != null">#{weight},</if>
            <if test="height != null">#{height},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="medicalRecord != null">#{medicalRecord},</if>
            <if test="cardType != null">#{cardType},</if>
            <if test="cardCode != null">#{cardCode},</if>
            <if test="pregnantUnit != null">#{pregnantUnit},</if>
            <if test="pregnant != null">#{pregnant},</if>
            <if test="allInfo != null">#{allInfo},</if>
            <if test="diaInfo != null">#{diaInfo},</if>
            <if test="presId != null">#{presId},</if>
            <if test="reason != null">#{reason},</if>
            <if test="isUrgent != null">#{isUrgent},</if>
            <if test="isNew != null">#{isNew},</if>
            <if test="isCurrent != null">#{isCurrent},</if>
            <if test="presType != null">#{presType},</if>
            <if test="presTime != null">#{presTime},</if>
            <if test="dischargeDrug != null">#{dischargeDrug},</if>
            <if test="admType != null">#{admType},</if>
            <if test="requir != null">#{requir},</if>
            <if test="cs1 != null">#{cs1},</if>
            <if test="ts != null">#{ts},</if>
            <if test="solvent != null">#{solvent},</if>
            <if test="jl != null">#{jl},</if>
            <if test="cs2 != null">#{cs2},</if>
            <if test="lb != null">#{lb},</if>
            <if test="fs1 != null">#{fs1},</if>
            <if test="fs2 != null">#{fs2},</if>
            <if test="prescriptionType != null">#{prescriptionType},</if>
            <if test="level != null">#{level},</if>
            <if test="flag != null">#{flag},</if>
            <if test="isReadDoc != null">#{isReadDoc},</if>
            <if test="isReadYs != null">#{isReadYs},</if>
            <if test="text != null">#{text},</if>
            <if test="zyzb != null">#{zyzb},</if>
            <if test="zyzbCode != null">#{zyzbCode},</if>
            <if test="zyzz != null">#{zyzz},</if>
            <if test="zyzzCode != null">#{zyzzCode},</if>
            <if test="reason1 != null">#{reason1},</if>
            <if test="presSm != null">#{presSm},</if>
            <if test="reason2 != null">#{reason2},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRmsTPres" parameterType="RmsTPres">
        update rms_t_pres
        <trim prefix="SET" suffixOverrides=",">
            <if test="hospCode != null">hosp_code = #{hospCode},</if>
            <if test="deptCode != null">dept_code = #{deptCode},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="doctCode != null">doct_code = #{doctCode},</if>
            <if test="doctName != null">doct_name = #{doctName},</if>
            <if test="doctType != null">doct_type = #{doctType},</if>
            <if test="doctTypeName != null">doct_type_name = #{doctTypeName},</if>
            <if test="hisTime != null">his_time = #{hisTime},</if>
            <if test="hospFlag != null">hosp_flag = #{hospFlag},</if>
            <if test="treatType != null">treat_type = #{treatType},</if>
            <if test="treatCode != null">treat_code = #{treatCode},</if>
            <if test="bedNo != null">bed_no = #{bedNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="birth != null">birth = #{birth},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="height != null">height = #{height},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="medicalRecord != null">medical_record = #{medicalRecord},</if>
            <if test="cardType != null">card_type = #{cardType},</if>
            <if test="cardCode != null">card_code = #{cardCode},</if>
            <if test="pregnantUnit != null">pregnant_unit = #{pregnantUnit},</if>
            <if test="pregnant != null">pregnant = #{pregnant},</if>
            <if test="allInfo != null">all_info = #{allInfo},</if>
            <if test="diaInfo != null">dia_info = #{diaInfo},</if>
            <if test="presId != null">pres_id = #{presId},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="isUrgent != null">is_urgent = #{isUrgent},</if>
            <if test="isNew != null">is_new = #{isNew},</if>
            <if test="isCurrent != null">is_current = #{isCurrent},</if>
            <if test="presType != null">pres_type = #{presType},</if>
            <if test="presTime != null">pres_time = #{presTime},</if>
            <if test="dischargeDrug != null">discharge_drug = #{dischargeDrug},</if>
            <if test="admType != null">adm_type = #{admType},</if>
            <if test="requir != null">requir = #{requir},</if>
            <if test="cs1 != null">cs1 = #{cs1},</if>
            <if test="ts != null">ts = #{ts},</if>
            <if test="solvent != null">solvent = #{solvent},</if>
            <if test="jl != null">jl = #{jl},</if>
            <if test="cs2 != null">cs2 = #{cs2},</if>
            <if test="lb != null">lb = #{lb},</if>
            <if test="fs1 != null">fs1 = #{fs1},</if>
            <if test="fs2 != null">fs2 = #{fs2},</if>
            <if test="prescriptionType != null">prescription_type = #{prescriptionType},</if>
            <if test="level != null">level = #{level},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="isReadDoc != null">is_read_doc = #{isReadDoc},</if>
            <if test="isReadYs != null">is_read_ys = #{isReadYs},</if>
            <if test="text != null">text = #{text},</if>
            <if test="zyzb != null">zyzb = #{zyzb},</if>
            <if test="zyzbCode != null">zyzb_code = #{zyzbCode},</if>
            <if test="zyzz != null">zyzz = #{zyzz},</if>
            <if test="zyzzCode != null">zyzz_code = #{zyzzCode},</if>
            <if test="reason1 != null">reason1 = #{reason1},</if>
            <if test="presSm != null">pres_sm = #{presSm},</if>
            <if test="reason2 != null">reason2 = #{reason2},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteRmsTPresByCode" parameterType="String">
        delete from rms_t_pres where code = #{code}
    </delete>

    <delete id="deleteRmsTPresByCodes" parameterType="String">
        delete from rms_t_pres where code in
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>

    <!-- 批量更新处方状态 -->
    <update id="batchUpdatePrescriptionStatus">
        update rms_t_pres set flag = #{flag}, update_time = now()
        where code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </update>

    <!-- 查询所有有处方的科室 -->
    <select id="selectDistinctDepartments" resultType="java.util.Map">
        select distinct dept_code as deptCode, dept_name as deptName
        from rms_t_pres
        where dept_code is not null and dept_name is not null
        order by dept_name
    </select>
</mapper>