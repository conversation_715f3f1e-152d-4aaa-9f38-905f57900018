<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTPresMedMapper">
    
    <resultMap type="RmsTPresMed" id="RmsTPresMedResult">
        <result property="code"    column="code"    />
        <result property="medName"    column="med_name"    />
        <result property="hisCode"    column="his_code"    />
        <result property="insurCode"    column="insur_code"    />
        <result property="approval"    column="approval"    />
        <result property="spec"    column="spec"    />
        <result property="group"    column="group"    />
        <result property="reason"    column="reason"    />
        <result property="doseUnit"    column="dose_unit"    />
        <result property="dose"    column="dose"    />
        <result property="ordQty"    column="ord_qty"    />
        <result property="ordUom"    column="ord_uom"    />
        <result property="freq"    column="freq"    />
        <result property="administer"    column="administer"    />
        <result property="beginTime"    column="begin_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="days"    column="days"    />
        <result property="decoctionCode"    column="decoction_code"    />
        <result property="money"    column="money"    />
        <result property="presId"    column="pres_id"    />
        <result property="medReason1"    column="med_reason1"    />
        <result property="yysm"    column="yysm"    />
        <result property="bz"    column="bz"    />
    </resultMap>

    <sql id="selectRmsTPresMedVo">
        select code, med_name, his_code, insur_code, approval, spec, group, reason, dose_unit, dose, ord_qty, ord_uom, freq, administer, begin_time, end_time, days, decoction_code, money, pres_id, med_reason1, yysm, bz from rms_t_pres_med
    </sql>

    <select id="selectRmsTPresMedList" parameterType="RmsTPresMed" resultMap="RmsTPresMedResult">
        <include refid="selectRmsTPresMedVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="medName != null  and medName != ''"> and med_name like concat('%', #{medName}, '%')</if>
            <if test="hisCode != null  and hisCode != ''"> and his_code = #{hisCode}</if>
            <if test="insurCode != null  and insurCode != ''"> and insur_code = #{insurCode}</if>
            <if test="approval != null  and approval != ''"> and approval = #{approval}</if>
            <if test="spec != null  and spec != ''"> and spec = #{spec}</if>
            <if test="group != null  and group != ''"> and group = #{group}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="doseUnit != null  and doseUnit != ''"> and dose_unit = #{doseUnit}</if>
            <if test="dose != null  and dose != ''"> and dose = #{dose}</if>
            <if test="ordQty != null  and ordQty != ''"> and ord_qty = #{ordQty}</if>
            <if test="ordUom != null  and ordUom != ''"> and ord_uom = #{ordUom}</if>
            <if test="freq != null  and freq != ''"> and freq = #{freq}</if>
            <if test="administer != null  and administer != ''"> and administer = #{administer}</if>
            <if test="beginTime != null  and beginTime != ''"> and begin_time = #{beginTime}</if>
            <if test="endTime != null  and endTime != ''"> and end_time = #{endTime}</if>
            <if test="days != null  and days != ''"> and days = #{days}</if>
            <if test="decoctionCode != null  and decoctionCode != ''"> and decoction_code = #{decoctionCode}</if>
            <if test="money != null  and money != ''"> and money = #{money}</if>
            <if test="presId != null  and presId != ''"> and pres_id = #{presId}</if>
            <if test="medReason1 != null  and medReason1 != ''"> and med_reason1 = #{medReason1}</if>
            <if test="yysm != null  and yysm != ''"> and yysm = #{yysm}</if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
        </where>
    </select>
    
    <select id="selectRmsTPresMedByCode" parameterType="String" resultMap="RmsTPresMedResult">
        <include refid="selectRmsTPresMedVo"/>
        where code = #{code}
    </select>

    <insert id="insertRmsTPresMed" parameterType="RmsTPresMed">
        insert into rms_t_pres_med
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="medName != null">med_name,</if>
            <if test="hisCode != null">his_code,</if>
            <if test="insurCode != null">insur_code,</if>
            <if test="approval != null">approval,</if>
            <if test="spec != null">spec,</if>
            <if test="group != null">group,</if>
            <if test="reason != null">reason,</if>
            <if test="doseUnit != null">dose_unit,</if>
            <if test="dose != null">dose,</if>
            <if test="ordQty != null">ord_qty,</if>
            <if test="ordUom != null">ord_uom,</if>
            <if test="freq != null">freq,</if>
            <if test="administer != null">administer,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="days != null">days,</if>
            <if test="decoctionCode != null">decoction_code,</if>
            <if test="money != null">money,</if>
            <if test="presId != null">pres_id,</if>
            <if test="medReason1 != null">med_reason1,</if>
            <if test="yysm != null">yysm,</if>
            <if test="bz != null">bz,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="medName != null">#{medName},</if>
            <if test="hisCode != null">#{hisCode},</if>
            <if test="insurCode != null">#{insurCode},</if>
            <if test="approval != null">#{approval},</if>
            <if test="spec != null">#{spec},</if>
            <if test="group != null">#{group},</if>
            <if test="reason != null">#{reason},</if>
            <if test="doseUnit != null">#{doseUnit},</if>
            <if test="dose != null">#{dose},</if>
            <if test="ordQty != null">#{ordQty},</if>
            <if test="ordUom != null">#{ordUom},</if>
            <if test="freq != null">#{freq},</if>
            <if test="administer != null">#{administer},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="days != null">#{days},</if>
            <if test="decoctionCode != null">#{decoctionCode},</if>
            <if test="money != null">#{money},</if>
            <if test="presId != null">#{presId},</if>
            <if test="medReason1 != null">#{medReason1},</if>
            <if test="yysm != null">#{yysm},</if>
            <if test="bz != null">#{bz},</if>
         </trim>
    </insert>

    <update id="updateRmsTPresMed" parameterType="RmsTPresMed">
        update rms_t_pres_med
        <trim prefix="SET" suffixOverrides=",">
            <if test="medName != null">med_name = #{medName},</if>
            <if test="hisCode != null">his_code = #{hisCode},</if>
            <if test="insurCode != null">insur_code = #{insurCode},</if>
            <if test="approval != null">approval = #{approval},</if>
            <if test="spec != null">spec = #{spec},</if>
            <if test="group != null">group = #{group},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="doseUnit != null">dose_unit = #{doseUnit},</if>
            <if test="dose != null">dose = #{dose},</if>
            <if test="ordQty != null">ord_qty = #{ordQty},</if>
            <if test="ordUom != null">ord_uom = #{ordUom},</if>
            <if test="freq != null">freq = #{freq},</if>
            <if test="administer != null">administer = #{administer},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="days != null">days = #{days},</if>
            <if test="decoctionCode != null">decoction_code = #{decoctionCode},</if>
            <if test="money != null">money = #{money},</if>
            <if test="presId != null">pres_id = #{presId},</if>
            <if test="medReason1 != null">med_reason1 = #{medReason1},</if>
            <if test="yysm != null">yysm = #{yysm},</if>
            <if test="bz != null">bz = #{bz},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteRmsTPresMedByCode" parameterType="String">
        delete from rms_t_pres_med where code = #{code}
    </delete>

    <delete id="deleteRmsTPresMedByCodes" parameterType="String">
        delete from rms_t_pres_med where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>