# 处方审核模块部署说明

## 模块概述

处方审核模块是合理用药系统的核心功能，提供待审核处方的查看、筛选、详情展示和审核操作功能。

## 部署清单

### 后端文件
```
rms-core/src/main/java/com/rms/core/controller/RmsPrescriptionReviewController.java
rms-core/src/main/java/com/rms/core/service/IRmsTPresService.java (已扩展)
rms-core/src/main/java/com/rms/core/service/impl/RmsTPresServiceImpl.java (已扩展)
rms-core/src/main/java/com/rms/core/mapper/RmsTPresMapper.java (已扩展)
rms-core/src/main/resources/mapper/rms/RmsTPresMapper.xml (已扩展)
```

### 前端文件
```
rms-ui/src/api/rms/prescriptionreview.js (已更新)
rms-ui/src/views/rms/prescription/review/index.vue (已更新)
```

### 数据库脚本
```
rms-core/src/main/resources/sql/prescription_review_config.sql
```

### 文档文件
```
rms-core/src/main/resources/docs/prescription_review_test.md
rms-core/src/main/resources/docs/prescription_review_deployment.md
```

## 部署步骤

### 1. 数据库初始化

执行数据库脚本：
```sql
-- 执行系统参数配置和索引优化脚本
source rms-core/src/main/resources/sql/prescription_review_config.sql;
```

### 2. 后端部署

#### 2.1 编译项目
```bash
mvn clean compile
```

#### 2.2 检查依赖
确保以下依赖已正确配置：
- Spring Boot
- MyBatis
- Spring Security
- Redis (用于系统参数缓存)

#### 2.3 启动后端服务
```bash
mvn spring-boot:run
```

### 3. 前端部署

#### 3.1 安装依赖
```bash
cd rms-ui
npm install
```

#### 3.2 启动开发服务器
```bash
npm run dev
```

#### 3.3 生产环境构建
```bash
npm run build
```

### 4. 权限配置

在系统管理中添加以下权限：

#### 4.1 菜单权限
```
菜单名称：处方审核
菜单路径：/rms/prescription/review
权限标识：rms:prescription:review
```

#### 4.2 按钮权限
```
rms:prescription:review:list     - 查看处方列表
rms:prescription:review:query    - 查看处方详情
rms:prescription:review:approve  - 审核通过
rms:prescription:review:reject   - 审核打回
```

### 5. 系统参数配置

在系统管理 > 参数设置中确认以下参数：

| 参数键名 | 参数名称 | 参数值 | 说明 |
|---------|---------|-------|------|
| rms.pres.refreshTime | 处方审核刷新时间 | 5 | 自动刷新间隔(秒) |

## 配置说明

### 1. 数据库配置

#### 1.1 必需的表结构
- `rms_t_pres` - 处方主表
- `rms_t_pres_med` - 处方药品明细表
- `rms_t_pres_fx` - 处方分析结果表
- `rms_t_pres_sh` - 处方审核记录表
- `rms_cfwtlb` - 处方问题类别表
- `sys_config` - 系统参数配置表

#### 1.2 关键字段说明
- `rms_t_pres.flag`: 处方状态
  - 10: 进入审方中心
  - 11: 审核不通过
  - 12: 审核通过
- `rms_t_pres.level`: 严重程度 (重要/一般/其它)
- `rms_t_pres.prescription_type`: 药品类型 (1-西药/2-中药)

### 2. 应用配置

#### 2.1 日志配置
建议在 `logback-spring.xml` 中添加处方审核相关日志：
```xml
<logger name="com.rms.core.controller.RmsPrescriptionReviewController" level="INFO"/>
<logger name="com.rms.core.service.impl.RmsTPresServiceImpl" level="DEBUG"/>
```

#### 2.2 缓存配置
系统参数会自动缓存到Redis，确保Redis服务正常运行。

### 3. 前端路由配置

在 `rms-ui/src/router/index.js` 中确认路由配置：
```javascript
{
  path: '/rms/prescription/review',
  component: () => import('@/views/rms/prescription/review/index'),
  name: 'PrescriptionReview',
  meta: { title: '处方审核', icon: 'el-icon-s-check' }
}
```

## 验证部署

### 1. 后端接口验证

使用Postman或curl测试以下接口：

```bash
# 获取待审核处方列表
GET /rms/prescription/review/pending

# 获取处方详情
GET /rms/prescription/review/detail/{code}

# 获取问题类型
GET /rms/prescription/review/problemTypes

# 获取刷新时间配置
GET /rms/prescription/review/refreshTime

# 审核通过
POST /rms/prescription/review/approve

# 审核打回
POST /rms/prescription/review/reject

# 获取科室列表
GET /rms/prescription/review/departments
```

### 2. 前端功能验证

- 访问处方审核页面
- 测试筛选和搜索功能
- 测试处方详情展示
- 测试审核操作
- 测试自动刷新功能

### 3. 数据库验证

检查以下数据：
```sql
-- 检查系统参数是否正确插入
SELECT * FROM sys_config WHERE config_key = 'rms.pres.refreshTime';

-- 检查索引是否创建成功
SHOW INDEX FROM rms_t_pres;

-- 检查待审核处方数据
SELECT COUNT(*) FROM rms_t_pres WHERE flag = 10;
```

## 故障排除

### 1. 常见问题

#### 1.1 权限不足
- 检查用户是否具有相应权限
- 确认菜单和按钮权限配置正确

#### 1.2 数据不显示
- 检查数据库中是否有 `flag=10` 的处方数据
- 确认数据库连接正常

#### 1.3 自动刷新不工作
- 检查系统参数 `rms.pres.refreshTime` 是否配置
- 确认Redis缓存服务正常

#### 1.4 审核操作失败
- 检查数据库事务配置
- 确认用户权限和数据完整性

### 2. 日志查看

查看以下日志文件：
- 应用日志：`logs/rms.log`
- 错误日志：`logs/error.log`
- SQL日志：开启MyBatis SQL日志

### 3. 性能监控

监控以下指标：
- 数据库查询响应时间
- 接口响应时间
- 内存使用情况
- 并发用户数

## 维护说明

### 1. 定期维护

- 定期清理历史审核记录
- 监控数据库性能
- 更新系统参数配置

### 2. 数据备份

- 定期备份处方相关表数据
- 备份系统参数配置

### 3. 版本升级

- 保持代码版本控制
- 记录配置变更
- 测试新功能兼容性

## 联系信息

如有问题，请联系开发团队：
- 开发负责人：[姓名]
- 邮箱：[邮箱地址]
- 技术支持：[联系方式]
