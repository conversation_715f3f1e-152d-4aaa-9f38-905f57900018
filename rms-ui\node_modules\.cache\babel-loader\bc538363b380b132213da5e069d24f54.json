{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\api\\rms\\prescriptionreview.js", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\api\\rms\\prescriptionreview.js", "mtime": 1752930821875}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\babel.config.js", "mtime": 1748394285000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getPendingPrescriptions", "query", "request", "url", "method", "params", "getPrescriptionDetail", "code", "getProblemTypes", "getRefreshTime", "approvePrescriptions", "codes", "data", "rejectPrescriptions", "getDepartments"], "sources": ["D:/RationalMedicationSystem/rational-medication-system/rms-ui/src/api/rms/prescriptionreview.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询待审核处方列表\nexport function getPendingPrescriptions(query) {\n  return request({\n    url: '/rms/prescription/review/pending',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询处方详细信息\nexport function getPrescriptionDetail(code) {\n  return request({\n    url: '/rms/prescription/review/detail/' + code,\n    method: 'get'\n  })\n}\n\n// 获取处方问题类别列表\nexport function getProblemTypes() {\n  return request({\n    url: '/rms/prescription/review/problemTypes',\n    method: 'get'\n  })\n}\n\n// 获取系统刷新时间配置\nexport function getRefreshTime() {\n  return request({\n    url: '/rms/prescription/review/refreshTime',\n    method: 'get'\n  })\n}\n\n// 批量审核通过\nexport function approvePrescriptions(codes) {\n  return request({\n    url: '/rms/prescription/review/approve',\n    method: 'post',\n    data: codes\n  })\n}\n\n// 批量审核打回\nexport function rejectPrescriptions(data) {\n  return request({\n    url: '/rms/prescription/review/reject',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取科室列表\nexport function getDepartments() {\n  return request({\n    url: '/rms/prescription/review/departments',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,uBAAuBA,CAACC,KAAK,EAAE;EAC7C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGI,IAAI;IAC9CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,eAAeA,CAAA,EAAG;EAChC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAED;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,mBAAmBA,CAACD,IAAI,EAAE;EACxC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}