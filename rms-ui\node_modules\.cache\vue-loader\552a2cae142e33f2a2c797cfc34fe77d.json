{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752981982761}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGVuZGluZ1ByZXNjcmlwdGlvbnMsCiAgZ2V0UHJlc2NyaXB0aW9uRGV0YWlsLAogIGdldFByb2JsZW1UeXBlcywKICBnZXRSZWZyZXNoVGltZSwKICBhcHByb3ZlUHJlc2NyaXB0aW9ucywKICByZWplY3RQcmVzY3JpcHRpb25zLAogIGdldERlcGFydG1lbnRzCn0gZnJvbSAiQC9hcGkvcm1zL3ByZXNjcmlwdGlvbnJldmlldyIKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUHJlc2NyaXB0aW9uUmV2aWV3IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5Yqg6L2954q25oCBCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpgInkuK3nmoTlpITmlrnlr7nosaHmlbDnu4TvvIjnlKjkuo7nirbmgIHmjIHkuYXljJbvvIkKICAgICAgc2VsZWN0ZWRQcmVzY3JpcHRpb25zOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlpITmlrnliJfooagKICAgICAgcHJlc2NyaXB0aW9uTGlzdDogW10sCiAgICAgIC8vIOW9k+WJjemAieS4reeahOWkhOaWuQogICAgICBjdXJyZW50UHJlc2NyaXB0aW9uOiBudWxsLAogICAgICAvLyDoja/lk4HmmI7nu4bliJfooagKICAgICAgbWVkaWNhdGlvbkxpc3Q6IFtdLAogICAgICAvLyDliIbmnpDnu5PmnpzliJfooagKICAgICAgYW5hbHlzaXNSZXN1bHRzOiBbXSwKICAgICAgLy8g5a6h5qC45Y6G5Y+y5YiX6KGoCiAgICAgIHJldmlld0hpc3Rvcnk6IFtdLAogICAgICAvLyDnp5HlrqTliJfooagKICAgICAgZGVwYXJ0bWVudExpc3Q6IFtdLAogICAgICAvLyDpl67popjnsbvlnovliJfooagKICAgICAgcHJvYmxlbVR5cGVzOiBbXSwKICAgICAgLy8g6YCJ5Lit55qE6Zeu6aKY57G75Z6LCiAgICAgIHNlbGVjdGVkUHJvYmxlbXM6IFtdLAogICAgICAvLyDoh6rliqjliLfmlrDlvIDlhbMKICAgICAgYXV0b1JlZnJlc2g6IGZhbHNlLAogICAgICAvLyDliLfmlrDlrprml7blmagKICAgICAgcmVmcmVzaFRpbWVyOiBudWxsLAogICAgICAvLyDliLfmlrDpl7TpmpTvvIjmr6vnp5LvvIkKICAgICAgcmVmcmVzaEludGVydmFsOiA1MDAwLAogICAgICAvLyDmmK/lkKbnlLHoh6rliqjliLfmlrDop6blj5HnmoTmoIforrAKICAgICAgaXNBdXRvUmVmcmVzaFRyaWdnZXJlZDogZmFsc2UsCiAgICAgIC8vIOmhtemdouS4jeWPr+ingeWJjeaYr+WQpuW8gOWQr+S6huiHquWKqOWIt+aWsAogICAgICB3YXNBdXRvUmVmcmVzaEFjdGl2ZTogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuato+WcqOaBouWkjemAieaLqeeKtuaAge+8iOeUqOS6jumYu+atouaEj+WklueahOmAieaLqeWPmOWMluS6i+S7tu+8iQogICAgICBpc1Jlc3RvcmluZ1NlbGVjdGlvbjogZmFsc2UsCiAgICAgIC8vIOW9k+WJjea0u+i3g+eahOagh+etvumhtQogICAgICBhY3RpdmVUYWI6ICdpbXBvcnRhbnQnLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBkZXB0Q29kZTogbnVsbCwKICAgICAgICBwcmVzY3JpcHRpb25UeXBlOiAnJywKICAgICAgICBrZXl3b3JkOiBudWxsCiAgICAgIH0KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAvKiog6K6h566X6KGo5qC86auY5bqmICovCiAgICB0YWJsZUhlaWdodCgpIHsKICAgICAgLy8gMTAwdmggLSDpobbpg6jlr7zoiKooNTBweCkgLSDpnaLljIXlsZEoNDBweCkgLSDmkJzntKLljLrln58oMTIwcHgpIC0g5Y2h54mH5aS06YOoKDYwcHgpIC0g5YiG6aG15Yy65Z+fKDUwcHgpIC0g5bqV6YOo5a6h5qC45Yy65Z+fKDgwcHgpIC0g6L656LedKDQwcHgpCiAgICAgIHJldHVybiAnY2FsYygxMDB2aCAtIDQ0MHB4KScKICAgIH0sCiAgICAvKiog6K6h566X6K+m5oOF5Yy65Z+f6auY5bqmICovCiAgICBkZXRhaWxIZWlnaHQoKSB7CiAgICAgIC8vIOS4juihqOagvOmrmOW6puS/neaMgeS4gOiHtAogICAgICByZXR1cm4gJ2NhbGMoMTAwdmggLSAzODBweCknCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCkKICAgIHRoaXMuZ2V0RGVwYXJ0bWVudExpc3QoKQogICAgdGhpcy5nZXRQcm9ibGVtVHlwZUxpc3QoKQogICAgdGhpcy5nZXRSZWZyZXNoQ29uZmlnKCkKICAgIHRoaXMubG9hZFNlbGVjdGVkU3RhdGUoKQogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8vIOebkeWQrOeql+WPo+Wkp+Wwj+WPmOWMlgogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKQogICAgLy8g55uR5ZCs5L6n6L655qCP54q25oCB5Y+Y5YyWCiAgICB0aGlzLmNoZWNrU2lkZWJhclN0YXR1cygpCiAgICAvLyDnm5HlkKzpobXpnaLlj6/op4HmgKflj5jljJYKICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3Zpc2liaWxpdHljaGFuZ2UnLCB0aGlzLmhhbmRsZVZpc2liaWxpdHlDaGFuZ2UpCiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5aSE5pa55YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICBjb25zb2xlLmxvZygn8J+UhCBb5pWw5o2u5Yqg6L29XSDlvIDlp4vliqDovb3lpITmlrnliJfooagnLCB7CiAgICAgICAg5piv5ZCm6Ieq5Yqo5Yi35pawOiB0aGlzLmF1dG9SZWZyZXNoLAogICAgICAgIOW9k+WJjemhteeggTogdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtLAogICAgICAgIOmhtemdouWkp+WwjzogdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZSwKICAgICAgICDliqDovb3ml7bpl7Q6IG5ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKCksCiAgICAgICAg5b2T5YmN6YCJ5oup54q25oCBOiB0aGlzLmlkcwogICAgICB9KQoKICAgICAgLy8g5Zyo5pWw5o2u5Yqg6L295YmN5L+d5a2Y5b2T5YmN55qE6YCJ5oup54q25oCBCiAgICAgIGNvbnN0IHNhdmVkSWRzID0gWy4uLnRoaXMuaWRzXQogICAgICBjb25zb2xlLmxvZygn8J+SviBb54q25oCB5aSH5Lu9XSDlpIfku73lvZPliY3pgInmi6nnirbmgIEnLCB7IOWkh+S7veeahGlkczogc2F2ZWRJZHMgfSkKCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgLy8g6K6+572u5oGi5aSN54q25oCB5qCH6K6w77yM6Ziy5q2i6KGo5qC86YeN5paw5riy5p+T5pe25riF56m66YCJ5oupCiAgICAgIHRoaXMuaXNSZXN0b3JpbmdTZWxlY3Rpb24gPSB0cnVlCgogICAgICAvLyDmnoTlu7rmn6Xor6Llj4LmlbAKICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9CgogICAgICAvLyDlpITnkIblhbPplK7lrZfmkJzntKIKICAgICAgaWYgKHBhcmFtcy5rZXl3b3JkKSB7CiAgICAgICAgcGFyYW1zLmRvY3ROYW1lID0gcGFyYW1zLmtleXdvcmQKICAgICAgICBwYXJhbXMubmFtZSA9IHBhcmFtcy5rZXl3b3JkCiAgICAgIH0KCiAgICAgIGdldFBlbmRpbmdQcmVzY3JpcHRpb25zKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ogW+aVsOaNruWKoOi9vV0g5aSE5pa55YiX6KGo5Yqg6L295a6M5oiQJywgewogICAgICAgICAg5pWw5o2u5p2h5pWwOiByZXNwb25zZS5yb3dzLmxlbmd0aCwKICAgICAgICAgIOaAu+aVsDogcmVzcG9uc2UudG90YWwsCiAgICAgICAgICDlpITmlrnnvJbnoIHliJfooag6IHJlc3BvbnNlLnJvd3MubWFwKGl0ZW0gPT4gaXRlbS5jb2RlKQogICAgICAgIH0pCgogICAgICAgIHRoaXMucHJlc2NyaXB0aW9uTGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQoKICAgICAgICAvLyDmgaLlpI3lpIfku73nmoTpgInmi6nnirbmgIEKICAgICAgICB0aGlzLmlkcyA9IHNhdmVkSWRzCgogICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFvnirbmgIHmgaLlpI1dIOWHhuWkh+aBouWkjemAieaLqeeKtuaAgScsIHsKICAgICAgICAgIOWkh+S7veeahGlkczogc2F2ZWRJZHMsCiAgICAgICAgICDlvZPliY1pZHM6IHRoaXMuaWRzLAogICAgICAgICAgaWRz5pWw6YePOiB0aGlzLmlkcy5sZW5ndGgsCiAgICAgICAgICDooajmoLxyZWblrZjlnKg6ICEhdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZSwKICAgICAgICAgIOaYr+WQpuiHquWKqOWIt+aWsOinpuWPkTogdGhpcy5pc0F1dG9SZWZyZXNoVHJpZ2dlcmVkCiAgICAgICAgfSkKCiAgICAgICAgLy8g5qC55o2u5piv5ZCm6Ieq5Yqo5Yi35paw5L2/55So5LiN5ZCM55qE5oGi5aSN562W55WlCiAgICAgICAgaWYgKHRoaXMuaXNBdXRvUmVmcmVzaFRyaWdnZXJlZCkgewogICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgW+iHquWKqOWIt+aWsF0g5L2/55So6Ieq5Yqo5Yi35paw5LiT55So5oGi5aSN5py65Yi2JykKICAgICAgICAgIHRoaXMuaGFuZGxlQXV0b1JlZnJlc2hSZXN0b3JlKCkKICAgICAgICAgIHRoaXMuaXNBdXRvUmVmcmVzaFRyaWdnZXJlZCA9IGZhbHNlIC8vIOmHjee9ruagh+iusAogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmxvZygn8J+RhiBb5omL5Yqo5pON5L2cXSDkvb/nlKjluLjop4TmgaLlpI3mnLrliLYnKQogICAgICAgICAgLy8g5oGi5aSN6YCJ5oup54q25oCBIC0g56Gu5L+dRE9N5a6M5YWo5pu05paw5ZCO5YaN5oGi5aSNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfij7AgW+W7tui/n+aBouWkjV0gJG5leHRUaWNr5omn6KGM77yM5YeG5aSH5bu26L+f5oGi5aSN54q25oCBJykKICAgICAgICAgICAgLy8g5bu26L+f5LiA5bCP5q615pe26Ze056Gu5L+d6KGo5qC85a6M5YWo5riy5p+TCiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn46vIFvlvIDlp4vmgaLlpI1dIOW8gOWni+aJp+ihjOeKtuaAgeaBouWkjScpCiAgICAgICAgICAgICAgdGhpcy5yZXN0b3JlU2VsZWN0ZWRTdGF0ZSgpCiAgICAgICAgICAgIH0sIDIwMCkgLy8g5aKe5Yqg5bu26L+f5pe26Ze05YiwMjAwbXMKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgICB0aGlzLmlzUmVzdG9yaW5nU2VsZWN0aW9uID0gZmFsc2UgLy8g5Ye66ZSZ5pe25Lmf6KaB6YeN572u5qCH6K6wCiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFvliqDovb3lpLHotKVdIOiOt+WPluWkhOaWueWIl+ihqOWksei0pTonLCBlcnJvcikKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCgogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFvpgInmi6nlj5jljJZdIOinpuWPkemAieaLqeWPmOWMluS6i+S7ticsIHsKICAgICAgICDpgInkuK3mlbDph486IHNlbGVjdGlvbi5sZW5ndGgsCiAgICAgICAg6YCJ5Lit5aSE5pa5OiBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gKHsgY29kZTogaXRlbS5jb2RlLCBuYW1lOiBpdGVtLm5hbWUgfSkpLAogICAgICAgIOinpuWPkeaXtumXtDogbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKSwKICAgICAgICDmraPlnKjmgaLlpI3nirbmgIE6IHRoaXMuaXNSZXN0b3JpbmdTZWxlY3Rpb24KICAgICAgfSkKCiAgICAgIC8vIOWmguaenOato+WcqOaBouWkjemAieaLqeeKtuaAge+8jOW/veeVpei/measoemAieaLqeWPmOWMluS6i+S7tgogICAgICBpZiAodGhpcy5pc1Jlc3RvcmluZ1NlbGVjdGlvbikgewogICAgICAgIGNvbnNvbGUubG9nKCfwn5qrIFvlv73nlaXkuovku7ZdIOato+WcqOaBouWkjemAieaLqeeKtuaAge+8jOW/veeVpeatpOasoemAieaLqeWPmOWMluS6i+S7ticpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uY29kZSkKICAgICAgdGhpcy5zZWxlY3RlZFByZXNjcmlwdGlvbnMgPSBzZWxlY3Rpb24KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAoKICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW+eKtuaAgeabtOaWsF0g5pu05paw57uE5Lu254q25oCBJywgewogICAgICAgIGlkczogdGhpcy5pZHMsCiAgICAgICAgc2luZ2xlOiB0aGlzLnNpbmdsZSwKICAgICAgICBtdWx0aXBsZTogdGhpcy5tdWx0aXBsZQogICAgICB9KQoKICAgICAgLy8g5L+d5a2Y6YCJ5oup54q25oCB5Yiw5pys5Zyw5a2Y5YKoCiAgICAgIHRoaXMuc2F2ZVNlbGVjdGVkU3RhdGUoKQogICAgfSwKCiAgICAvKiog6KGM54K55Ye75LqL5Lu2ICovCiAgICBoYW5kbGVSb3dDbGljayhyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50UHJlc2NyaXB0aW9uID0gcm93CiAgICAgIHRoaXMuZ2V0UHJlc2NyaXB0aW9uRGV0YWlsRGF0YShyb3cuY29kZSkKICAgIH0sCgogICAgLyoqIOiOt+WPluWkhOaWueivpuaDhSAqLwogICAgZ2V0UHJlc2NyaXB0aW9uRGV0YWlsRGF0YShjb2RlKSB7CiAgICAgIGdldFByZXNjcmlwdGlvbkRldGFpbChjb2RlKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmN1cnJlbnRQcmVzY3JpcHRpb24gPSByZXNwb25zZS5kYXRhLnByZXNjcmlwdGlvbgogICAgICAgIHRoaXMubWVkaWNhdGlvbkxpc3QgPSByZXNwb25zZS5kYXRhLm1lZGljYXRpb25zIHx8IFtdCiAgICAgICAgdGhpcy5hbmFseXNpc1Jlc3VsdHMgPSByZXNwb25zZS5kYXRhLmFuYWx5c2lzUmVzdWx0cyB8fCBbXQogICAgICAgIHRoaXMucmV2aWV3SGlzdG9yeSA9IHJlc3BvbnNlLmRhdGEucmV2aWV3SGlzdG9yeSB8fCBbXQogICAgICB9KQogICAgfSwKCiAgICAvKiog6I635Y+W56eR5a6k5YiX6KGoICovCiAgICBnZXREZXBhcnRtZW50TGlzdCgpIHsKICAgICAgZ2V0RGVwYXJ0bWVudHMoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRlcGFydG1lbnRMaXN0ID0gcmVzcG9uc2UuZGF0YSB8fCBbXQogICAgICB9KQogICAgfSwKCiAgICAvKiog6I635Y+W6Zeu6aKY57G75Z6L5YiX6KGoICovCiAgICBnZXRQcm9ibGVtVHlwZUxpc3QoKSB7CiAgICAgIGdldFByb2JsZW1UeXBlcygpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucHJvYmxlbVR5cGVzID0gcmVzcG9uc2UuZGF0YSB8fCBbXQogICAgICB9KQogICAgfSwKCiAgICAvKiog6I635Y+W5Yi35paw6YWN572uICovCiAgICBnZXRSZWZyZXNoQ29uZmlnKCkgewogICAgICBnZXRSZWZyZXNoVGltZSgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucmVmcmVzaEludGVydmFsID0gcmVzcG9uc2UuZGF0YSB8fCA1MDAwCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDliIfmjaLoh6rliqjliLfmlrAgKi8KICAgIHRvZ2dsZUF1dG9SZWZyZXNoKHZhbHVlKSB7CiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFvoh6rliqjliLfmlrBdIOWIh+aNouiHquWKqOWIt+aWsOeKtuaAgScsIHsKICAgICAgICDlvIDlkK86IHZhbHVlLAogICAgICAgIOWIt+aWsOmXtOmalDogdGhpcy5yZWZyZXNoSW50ZXJ2YWwsCiAgICAgICAg5b2T5YmN6YCJ5oup5pWw6YePOiB0aGlzLmlkcy5sZW5ndGgKICAgICAgfSkKCiAgICAgIGlmICh2YWx1ZSkgewogICAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgICAgY29uc29sZS5sb2coJ+KPsCBb6Ieq5Yqo5Yi35pawXSDoh6rliqjliLfmlrDlrprml7blmajop6blj5EnLCB7CiAgICAgICAgICAgIOinpuWPkeaXtumXtDogbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKSwKICAgICAgICAgICAg5b2T5YmN6YCJ5oup54q25oCBOiB0aGlzLmlkcwogICAgICAgICAgfSkKCiAgICAgICAgICAvLyDmoIforrDov5nmmK/oh6rliqjliLfmlrDop6blj5HnmoTmlbDmja7liqDovb0KICAgICAgICAgIHRoaXMuaXNBdXRvUmVmcmVzaFRyaWdnZXJlZCA9IHRydWUKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgfSwgdGhpcy5yZWZyZXNoSW50ZXJ2YWwpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKHRoaXMucmVmcmVzaFRpbWVyKSB7CiAgICAgICAgICBjbGVhckludGVydmFsKHRoaXMucmVmcmVzaFRpbWVyKQogICAgICAgICAgdGhpcy5yZWZyZXNoVGltZXIgPSBudWxsCiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8qKiDlrqHmoLjpgJrov4cgKi8KICAgIGhhbmRsZUFwcHJvdmUoKSB7CiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup6KaB5a6h5qC455qE5aSE5pa5IikKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a6h5qC46YCa6L+H6YCJ5Lit55qE5aSE5pa577yfJykudGhlbigoKSA9PiB7CiAgICAgICAgcmV0dXJuIGFwcHJvdmVQcmVzY3JpcHRpb25zKHRoaXMuaWRzKQogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgIHRoaXMuY2xlYXJTZWxlY3Rpb24oKQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWuoeaguOmAmui/h+aIkOWKnyIpCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgfSwKCiAgICAvKiog5a6h5qC45omT5ZueICovCiAgICBoYW5kbGVSZWplY3QoKSB7CiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup6KaB5omT5Zue55qE5aSE5pa5IikKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRQcm9ibGVtcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup6Zeu6aKY57G75Z6LIikKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5omT5Zue6YCJ5Lit55qE5aSE5pa577yfJykudGhlbigoKSA9PiB7CiAgICAgICAgY29uc3QgZGF0YSA9IHsKICAgICAgICAgIGNvZGVzOiB0aGlzLmlkcywKICAgICAgICAgIHByb2JsZW1OYW1lczogdGhpcy5zZWxlY3RlZFByb2JsZW1zCiAgICAgICAgfQogICAgICAgIHJldHVybiByZWplY3RQcmVzY3JpcHRpb25zKGRhdGEpCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgdGhpcy5zZWxlY3RlZFByb2JsZW1zID0gW10KICAgICAgICB0aGlzLmNsZWFyU2VsZWN0aW9uKCkKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlrqHmoLjmiZPlm57miJDlip8iKQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkKICAgIH0sCgogICAgLyoqIOagueaNrumXrumimOetiee6p+iOt+WPluWIhuaekOe7k+aenCAqLwogICAgZ2V0QW5hbHlzaXNSZXN1bHRzQnlMZXZlbChsZXZlbCkgewogICAgICByZXR1cm4gdGhpcy5hbmFseXNpc1Jlc3VsdHMuZmlsdGVyKGl0ZW0gPT4gaXRlbS53dGx2bCA9PT0gbGV2ZWwpCiAgICB9LAoKICAgIC8qKiDojrflj5bkuKXph43nqIvluqbnsbvlnosgKi8KICAgIGdldFNldmVyaXR5VHlwZShsZXZlbCkgewogICAgICBzd2l0Y2gobGV2ZWwpIHsKICAgICAgICBjYXNlICfph43opoEnOiByZXR1cm4gJ2RhbmdlcicKICAgICAgICBjYXNlICfkuIDoiKwnOiByZXR1cm4gJ3dhcm5pbmcnCiAgICAgICAgY2FzZSAn5YW25a6DJzogcmV0dXJuICdpbmZvJwogICAgICAgIGRlZmF1bHQ6IHJldHVybiAnaW5mbycKICAgICAgfQogICAgfSwKCiAgICAvKiog6I635Y+W5Lil6YeN56iL5bqm5paH5pysICovCiAgICBnZXRTZXZlcml0eVRleHQobGV2ZWwpIHsKICAgICAgcmV0dXJuIGxldmVsIHx8ICfmnKrnn6UnCiAgICB9LAoKICAgIC8qKiDorqHnrpflubTpvoQgKi8KICAgIGNhbGN1bGF0ZUFnZShiaXJ0aERhdGUpIHsKICAgICAgaWYgKCFiaXJ0aERhdGUpIHJldHVybiAn5pyq55+lJwogICAgICBjb25zdCBiaXJ0aCA9IG5ldyBEYXRlKGJpcnRoRGF0ZSkKICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKQogICAgICBjb25zdCBhZ2UgPSBub3cuZ2V0RnVsbFllYXIoKSAtIGJpcnRoLmdldEZ1bGxZZWFyKCkKICAgICAgcmV0dXJuIGFnZSArICflsoEnCiAgICB9LAoKICAgIC8qKiDkv53lrZjpgInmi6nnirbmgIHliLDmnKzlnLDlrZjlgqggKi8KICAgIHNhdmVTZWxlY3RlZFN0YXRlKCkgewogICAgICBjb25zdCBzZWxlY3RlZENvZGVzID0gdGhpcy5pZHMKICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW+S/neWtmOeKtuaAgV0g5L+d5a2Y6YCJ5oup54q25oCB5YiwbG9jYWxTdG9yYWdlJywgewogICAgICAgIOmAieS4reeahOWkhOaWuee8lueggTogc2VsZWN0ZWRDb2RlcywKICAgICAgICDmlbDph486IHNlbGVjdGVkQ29kZXMubGVuZ3RoLAogICAgICAgIOS/neWtmOaXtumXtDogbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKQogICAgICB9KQoKICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3ByZXNjcmlwdGlvbl9yZXZpZXdfc2VsZWN0ZWQnLCBKU09OLnN0cmluZ2lmeShzZWxlY3RlZENvZGVzKSkKCiAgICAgIC8vIOmqjOivgeS/neWtmOaYr+WQpuaIkOWKnwogICAgICBjb25zdCBzYXZlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdwcmVzY3JpcHRpb25fcmV2aWV3X3NlbGVjdGVkJykKICAgICAgY29uc29sZS5sb2coJ+KchSBb5L+d5a2Y6aqM6K+BXSBsb2NhbFN0b3JhZ2Xkv53lrZjnu5PmnpwnLCB7CiAgICAgICAg5L+d5a2Y55qE5pWw5o2uOiBzYXZlZCwKICAgICAgICDop6PmnpDlkI46IEpTT04ucGFyc2Uoc2F2ZWQgfHwgJ1tdJykKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOS7juacrOWcsOWtmOWCqOWKoOi9vemAieaLqeeKtuaAgSAqLwogICAgbG9hZFNlbGVjdGVkU3RhdGUoKSB7CiAgICAgIGNvbnNvbGUubG9nKCfwn5OWIFvliqDovb3nirbmgIFdIOW8gOWni+S7jmxvY2FsU3RvcmFnZeWKoOi9vemAieaLqeeKtuaAgScpCgogICAgICB0cnkgewogICAgICAgIGNvbnN0IHNhdmVkU2VsZWN0aW9uID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3ByZXNjcmlwdGlvbl9yZXZpZXdfc2VsZWN0ZWQnKQogICAgICAgIGNvbnNvbGUubG9nKCfwn5OWIFvliqDovb3nirbmgIFdIGxvY2FsU3RvcmFnZeS4reeahOaVsOaNricsIHsKICAgICAgICAgIOWOn+Wni+aVsOaNrjogc2F2ZWRTZWxlY3Rpb24sCiAgICAgICAgICDmlbDmja7nsbvlnos6IHR5cGVvZiBzYXZlZFNlbGVjdGlvbgogICAgICAgIH0pCgogICAgICAgIGlmIChzYXZlZFNlbGVjdGlvbikgewogICAgICAgICAgY29uc3QgcGFyc2VkSWRzID0gSlNPTi5wYXJzZShzYXZlZFNlbGVjdGlvbikKICAgICAgICAgIHRoaXMuaWRzID0gcGFyc2VkSWRzCiAgICAgICAgICB0aGlzLm11bHRpcGxlID0gdGhpcy5pZHMubGVuZ3RoID09PSAwCiAgICAgICAgICB0aGlzLnNpbmdsZSA9IHRoaXMuaWRzLmxlbmd0aCAhPT0gMQoKICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgW+WKoOi9veaIkOWKn10g54q25oCB5Yqg6L295a6M5oiQJywgewogICAgICAgICAgICDliqDovb3nmoRpZHM6IHRoaXMuaWRzLAogICAgICAgICAgICDmlbDph486IHRoaXMuaWRzLmxlbmd0aCwKICAgICAgICAgICAgbXVsdGlwbGU6IHRoaXMubXVsdGlwbGUsCiAgICAgICAgICAgIHNpbmdsZTogdGhpcy5zaW5nbGUKICAgICAgICAgIH0pCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUubG9nKCfihLnvuI8gW+WKoOi9veeKtuaAgV0gbG9jYWxTdG9yYWdl5Lit5rKh5pyJ5L+d5a2Y55qE6YCJ5oup54q25oCBJykKICAgICAgICAgIHRoaXMuaWRzID0gW10KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFvliqDovb3lpLHotKVdIOWKoOi9vemAieaLqeeKtuaAgeWksei0pTonLCBlcnJvcikKICAgICAgICB0aGlzLmlkcyA9IFtdCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOaBouWkjeihqOagvOmAieaLqeeKtuaAgSAqLwogICAgcmVzdG9yZVNlbGVjdGVkU3RhdGUoKSB7CiAgICAgIGNvbnNvbGUubG9nKCfwn46vIFvnirbmgIHmgaLlpI1dIOW8gOWni+aBouWkjeihqOagvOmAieaLqeeKtuaAgScsIHsKICAgICAgICDpnIDopoHmgaLlpI3nmoRpZHM6IHRoaXMuaWRzLAogICAgICAgIGlkc+aVsOmHjzogdGhpcy5pZHMubGVuZ3RoLAogICAgICAgIOihqOagvHJlZuWtmOWcqDogISF0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlLAogICAgICAgIOWkhOaWueWIl+ihqOmVv+W6pjogdGhpcy5wcmVzY3JpcHRpb25MaXN0Lmxlbmd0aCwKICAgICAgICDlpITmlrnliJfooajnvJbnoIE6IHRoaXMucHJlc2NyaXB0aW9uTGlzdC5tYXAoaXRlbSA9PiBpdGVtLmNvZGUpCiAgICAgIH0pCgogICAgICAvLyDmo4Dmn6XliY3nva7mnaHku7YKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNvbnNvbGUubG9nKCfihLnvuI8gW+eKtuaAgeaBouWkjV0g5rKh5pyJ6ZyA6KaB5oGi5aSN55qE6YCJ5oup54q25oCBJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgaWYgKCF0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFvnirbmgIHmgaLlpI1dIOihqOagvHJlZuS4jeWtmOWcqCcpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGlmICh0aGlzLnByZXNjcmlwdGlvbkxpc3QubGVuZ3RoID09PSAwKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+KEue+4jyBb54q25oCB5oGi5aSNXSDlpITmlrnliJfooajkuLrnqbrvvIzml6Dms5XmgaLlpI3nirbmgIEnKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0cnkgewogICAgICAgIC8vIOa4hemZpOW9k+WJjemAieaLqQogICAgICAgIGNvbnNvbGUubG9nKCfwn6e5IFvmuIXpmaTpgInmi6ldIOa4hemZpOihqOagvOW9k+WJjemAieaLqeeKtuaAgScpCiAgICAgICAgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZS5jbGVhclNlbGVjdGlvbigpCgogICAgICAgIC8vIOaBouWkjemAieaLqeeKtuaAgQogICAgICAgIGxldCByZXN0b3JlZENvdW50ID0gMAogICAgICAgIGxldCBtYXRjaGVkUm93cyA9IFtdCiAgICAgICAgbGV0IHVubWF0Y2hlZElkcyA9IFtdCgogICAgICAgIHRoaXMucHJlc2NyaXB0aW9uTGlzdC5mb3JFYWNoKHJvdyA9PiB7CiAgICAgICAgICBpZiAodGhpcy5pZHMuaW5jbHVkZXMocm93LmNvZGUpKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgW+WMuemFjeaIkOWKn10g5om+5Yiw5Yy56YWN55qE5aSE5pa5OiAke3Jvdy5jb2RlfSAtICR7cm93Lm5hbWV9YCkKICAgICAgICAgICAgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93LCB0cnVlKQogICAgICAgICAgICByZXN0b3JlZENvdW50KysKICAgICAgICAgICAgbWF0Y2hlZFJvd3MucHVzaCh7IGNvZGU6IHJvdy5jb2RlLCBuYW1lOiByb3cubmFtZSB9KQogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICAgIC8vIOajgOafpeacquWMuemFjeeahElECiAgICAgICAgdGhpcy5pZHMuZm9yRWFjaChpZCA9PiB7CiAgICAgICAgICBpZiAoIXRoaXMucHJlc2NyaXB0aW9uTGlzdC5maW5kKHJvdyA9PiByb3cuY29kZSA9PT0gaWQpKSB7CiAgICAgICAgICAgIHVubWF0Y2hlZElkcy5wdXNoKGlkKQogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICAgIC8vIOabtOaWsOmAieaLqeeKtuaAgee7n+iuoQogICAgICAgIHRoaXMubXVsdGlwbGUgPSB0aGlzLmlkcy5sZW5ndGggPT09IDAKICAgICAgICB0aGlzLnNpbmdsZSA9IHRoaXMuaWRzLmxlbmd0aCAhPT0gMQoKICAgICAgICBjb25zb2xlLmxvZygn8J+TiiBb5oGi5aSN57uT5p6cXSDnirbmgIHmgaLlpI3lrozmiJAnLCB7CiAgICAgICAgICDmgaLlpI3miJDlip/mlbDph486IHJlc3RvcmVkQ291bnQsCiAgICAgICAgICDljLnphY3nmoTlpITmlrk6IG1hdGNoZWRSb3dzLAogICAgICAgICAg5pyq5Yy56YWN55qESUQ6IHVubWF0Y2hlZElkcywKICAgICAgICAgIOacgOe7iOeKtuaAgTogewogICAgICAgICAgICBtdWx0aXBsZTogdGhpcy5tdWx0aXBsZSwKICAgICAgICAgICAgc2luZ2xlOiB0aGlzLnNpbmdsZSwKICAgICAgICAgICAgaWRzOiB0aGlzLmlkcwogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICAgIC8vIOmqjOivgeaBouWkjee7k+aenAogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIGNvbnN0IHNlbGVjdGVkUm93cyA9IHRoaXMuJHJlZnMucHJlc2NyaXB0aW9uVGFibGUuc2VsZWN0aW9uIHx8IFtdCiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBb5oGi5aSN6aqM6K+BXSDpqozor4HooajmoLzpgInmi6nnirbmgIEnLCB7CiAgICAgICAgICAgIOihqOagvOmAieS4reihjOaVsDogc2VsZWN0ZWRSb3dzLmxlbmd0aCwKICAgICAgICAgICAg6KGo5qC86YCJ5Lit57yW56CBOiBzZWxlY3RlZFJvd3MubWFwKHJvdyA9PiByb3cuY29kZSksCiAgICAgICAgICAgIOacn+acm+mAieS4reaVsOmHjzogdGhpcy5pZHMubGVuZ3RoLAogICAgICAgICAgICDnirbmgIHkuIDoh7Q6IHNlbGVjdGVkUm93cy5sZW5ndGggPT09IHJlc3RvcmVkQ291bnQKICAgICAgICAgIH0pCgogICAgICAgICAgaWYgKHNlbGVjdGVkUm93cy5sZW5ndGggIT09IHJlc3RvcmVkQ291bnQpIHsKICAgICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gW+eKtuaAgeS4jeS4gOiHtF0g6KGo5qC86YCJ5oup54q25oCB5LiO6aKE5pyf5LiN56ym77yM5bCd6K+V6YeN5paw5oGi5aSNJykKICAgICAgICAgICAgLy8g5aaC5p6c54q25oCB5LiN5LiA6Ie077yM5YaN5qyh5bCd6K+V5oGi5aSNCiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgIHRoaXMucmVzdG9yZVNlbGVjdGVkU3RhdGVSZXRyeSgpCiAgICAgICAgICAgIH0sIDEwMCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOaBouWkjeaIkOWKn++8jOmHjee9ruaBouWkjeeKtuaAgeagh+iusAogICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFvmgaLlpI3lrozmiJBdIOeKtuaAgeaBouWkjeaIkOWKn++8jOmHjee9ruaBouWkjeagh+iusCcpCiAgICAgICAgICAgIHRoaXMuaXNSZXN0b3JpbmdTZWxlY3Rpb24gPSBmYWxzZQogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBb5oGi5aSN5aSx6LSlXSDmgaLlpI3pgInmi6nnirbmgIHlpLHotKU6JywgZXJyb3IpCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOa4heepuumAieaLqSAqLwogICAgY2xlYXJTZWxlY3Rpb24oKSB7CiAgICAgIC8vIOiuvue9ruaBouWkjeagh+iusO+8jOmYsuatomNsZWFyU2VsZWN0aW9u6Kem5Y+R6YCJ5oup5Y+Y5YyW5LqL5Lu2CiAgICAgIHRoaXMuaXNSZXN0b3JpbmdTZWxlY3Rpb24gPSB0cnVlCgogICAgICB0aGlzLmlkcyA9IFtdCiAgICAgIHRoaXMuc2VsZWN0ZWRQcmVzY3JpcHRpb25zID0gW10KICAgICAgdGhpcy5tdWx0aXBsZSA9IHRydWUKICAgICAgdGhpcy5zaW5nbGUgPSB0cnVlCgogICAgICAvLyDmuIXpmaTooajmoLzpgInmi6kKICAgICAgaWYgKHRoaXMuJHJlZnMucHJlc2NyaXB0aW9uVGFibGUpIHsKICAgICAgICB0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlLmNsZWFyU2VsZWN0aW9uKCkKICAgICAgfQoKICAgICAgLy8g5riF6Zmk5pys5Zyw5a2Y5YKoCiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdwcmVzY3JpcHRpb25fcmV2aWV3X3NlbGVjdGVkJykKCiAgICAgIGNvbnNvbGUubG9nKCfwn6e5IFvmuIXnqbrpgInmi6ldIOmAieaLqeeKtuaAgeW3sua4heepuicsIHsKICAgICAgICDmuIXnqbrml7bpl7Q6IG5ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKCkKICAgICAgfSkKCiAgICAgIC8vIOmHjee9ruaBouWkjeagh+iusAogICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLmlzUmVzdG9yaW5nU2VsZWN0aW9uID0gZmFsc2UKICAgICAgfSwgMTAwKQogICAgfSwKCiAgICAvKiog6YeN6K+V5oGi5aSN6YCJ5oup54q25oCBICovCiAgICByZXN0b3JlU2VsZWN0ZWRTdGF0ZVJldHJ5KCkgewogICAgICBjb25zb2xlLmxvZygn8J+UhCBb6YeN6K+V5oGi5aSNXSDlvIDlp4vph43or5XmgaLlpI3pgInmi6nnirbmgIEnKQoKICAgICAgaWYgKCF0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFvph43or5XlpLHotKVdIOihqOagvHJlZuS7jeeEtuS4jeWtmOWcqCcpCiAgICAgICAgdGhpcy5pc1Jlc3RvcmluZ1NlbGVjdGlvbiA9IGZhbHNlCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOW8uuWItua4hemZpOmAieaLqQogICAgICB0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlLmNsZWFyU2VsZWN0aW9uKCkKCiAgICAgIC8vIOmHjeaWsOaBouWkjQogICAgICBsZXQgcmV0cnlDb3VudCA9IDAKICAgICAgdGhpcy5wcmVzY3JpcHRpb25MaXN0LmZvckVhY2gocm93ID0+IHsKICAgICAgICBpZiAodGhpcy5pZHMuaW5jbHVkZXMocm93LmNvZGUpKSB7CiAgICAgICAgICB0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihyb3csIHRydWUpCiAgICAgICAgICByZXRyeUNvdW50KysKICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFvph43or5XmgaLlpI1dIOmHjeaWsOmAieaLqeWkhOaWuTogJHtyb3cuY29kZX1gKQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIGNvbnNvbGUubG9nKGDinIUgW+mHjeivleWujOaIkF0g6YeN6K+V5oGi5aSN5LqGICR7cmV0cnlDb3VudH0g5Liq5aSE5pa555qE6YCJ5oup54q25oCBYCkKCiAgICAgIC8vIOmHjeivleWujOaIkOWQjumHjee9ruagh+iusAogICAgICB0aGlzLmlzUmVzdG9yaW5nU2VsZWN0aW9uID0gZmFsc2UKICAgIH0sCgogICAgLyoqIOiHquWKqOWIt+aWsOaXtueahOeJueauiuWkhOeQhiAqLwogICAgaGFuZGxlQXV0b1JlZnJlc2hSZXN0b3JlKCkgewogICAgICBjb25zb2xlLmxvZygn8J+UhCBb6Ieq5Yqo5Yi35pawXSDoh6rliqjliLfmlrDop6blj5HvvIznibnmrorlpITnkIbpgInmi6nnirbmgIHmgaLlpI0nKQoKICAgICAgLy8g5Zyo6Ieq5Yqo5Yi35paw5pe277yM57uZ5pu05aSa5pe26Ze06K6p6KGo5qC85a6M5YWo5riy5p+TCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgIGNvbnNvbGUubG9nKCfwn46vIFvoh6rliqjliLfmlrDmgaLlpI1dIOW8gOWni+iHquWKqOWIt+aWsOWQjueahOeKtuaAgeaBouWkjScpCiAgICAgICAgICB0aGlzLnJlc3RvcmVTZWxlY3RlZFN0YXRlKCkKCiAgICAgICAgICAvLyDpop3lpJbpqozor4EKICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICBjb25zdCBzZWxlY3RlZFJvd3MgPSB0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlPy5zZWxlY3Rpb24gfHwgW10KICAgICAgICAgICAgaWYgKHNlbGVjdGVkUm93cy5sZW5ndGggPT09IDAgJiYgdGhpcy5pZHMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIFvoh6rliqjliLfmlrBdIOesrOS4gOasoeaBouWkjeWksei0pe+8jOi/m+ihjOesrOS6jOasoeWwneivlScpCiAgICAgICAgICAgICAgdGhpcy5yZXN0b3JlU2VsZWN0ZWRTdGF0ZVJldHJ5KCkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAvLyDoh6rliqjliLfmlrDmgaLlpI3miJDlip/vvIzph43nva7moIforrAKICAgICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFvoh6rliqjliLfmlrBdIOiHquWKqOWIt+aWsOaBouWkjeaIkOWKn++8jOmHjee9ruaBouWkjeagh+iusCcpCiAgICAgICAgICAgICAgdGhpcy5pc1Jlc3RvcmluZ1NlbGVjdGlvbiA9IGZhbHNlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sIDMwMCkKICAgICAgICB9LCAzMDApIC8vIOiHquWKqOWIt+aWsOaXtuS9v+eUqOabtOmVv+eahOW7tui/nwogICAgICB9KQogICAgfSwKCiAgICAvKiog5aSE55CG56qX5Y+j5aSn5bCP5Y+Y5YyWICovCiAgICBoYW5kbGVSZXNpemUoKSB7CiAgICAgIC8vIOeql+WPo+Wkp+Wwj+WPmOWMluaXtu+8jOW8uuWItumHjeaWsOiuoeeul+ihqOagvOmrmOW6pgogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgaWYgKHRoaXMuJHJlZnMucHJlc2NyaXB0aW9uVGFibGUpIHsKICAgICAgICAgIHRoaXMuJHJlZnMucHJlc2NyaXB0aW9uVGFibGUuZG9MYXlvdXQoKQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLyoqIOajgOafpeS+p+i+ueagj+eKtuaAgSAqLwogICAgY2hlY2tTaWRlYmFyU3RhdHVzKCkgewogICAgICAvLyDmo4Dmn6Vib2R55piv5ZCm5pyJaGlkZVNpZGViYXLnsbsKICAgICAgY29uc3QgYm9keSA9IGRvY3VtZW50LmJvZHkKICAgICAgaWYgKGJvZHkuY2xhc3NMaXN0LmNvbnRhaW5zKCdoaWRlU2lkZWJhcicpKSB7CiAgICAgICAgLy8g5L6n6L655qCP5bey5pS26LW3CiAgICAgICAgdGhpcy51cGRhdGVGaXhlZEFyZWFQb3NpdGlvbih0cnVlKQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOS+p+i+ueagj+WxleW8gAogICAgICAgIHRoaXMudXBkYXRlRml4ZWRBcmVhUG9zaXRpb24oZmFsc2UpCiAgICAgIH0KCiAgICAgIC8vIOebkeWQrOS+p+i+ueagj+eKtuaAgeWPmOWMlgogICAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBNdXRhdGlvbk9ic2VydmVyKChtdXRhdGlvbnMpID0+IHsKICAgICAgICBtdXRhdGlvbnMuZm9yRWFjaCgobXV0YXRpb24pID0+IHsKICAgICAgICAgIGlmIChtdXRhdGlvbi50eXBlID09PSAnYXR0cmlidXRlcycgJiYgbXV0YXRpb24uYXR0cmlidXRlTmFtZSA9PT0gJ2NsYXNzJykgewogICAgICAgICAgICBjb25zdCBpc0hpZGRlbiA9IGJvZHkuY2xhc3NMaXN0LmNvbnRhaW5zKCdoaWRlU2lkZWJhcicpCiAgICAgICAgICAgIHRoaXMudXBkYXRlRml4ZWRBcmVhUG9zaXRpb24oaXNIaWRkZW4pCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSkKCiAgICAgIG9ic2VydmVyLm9ic2VydmUoYm9keSwgewogICAgICAgIGF0dHJpYnV0ZXM6IHRydWUsCiAgICAgICAgYXR0cmlidXRlRmlsdGVyOiBbJ2NsYXNzJ10KICAgICAgfSkKCiAgICAgIC8vIOS/neWtmG9ic2VydmVy5byV55So5Lul5L6/5riF55CGCiAgICAgIHRoaXMuc2lkZWJhck9ic2VydmVyID0gb2JzZXJ2ZXIKICAgIH0sCgogICAgLyoqIOabtOaWsOWbuuWumuWMuuWfn+S9jee9riAqLwogICAgdXBkYXRlRml4ZWRBcmVhUG9zaXRpb24oaXNIaWRkZW4pIHsKICAgICAgY29uc3QgZml4ZWRBcmVhID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLnJldmlldy1hY3Rpb25zLWZpeGVkJykKICAgICAgaWYgKGZpeGVkQXJlYSkgewogICAgICAgIGlmIChpc0hpZGRlbikgewogICAgICAgICAgZml4ZWRBcmVhLnN0eWxlLmxlZnQgPSAnNTRweCcKICAgICAgICAgIGZpeGVkQXJlYS5zdHlsZS5tYXhXaWR0aCA9ICdjYWxjKDEwMHZ3IC0gNTRweCknCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGZpeGVkQXJlYS5zdHlsZS5sZWZ0ID0gJzIwMHB4JwogICAgICAgICAgZml4ZWRBcmVhLnN0eWxlLm1heFdpZHRoID0gJ2NhbGMoMTAwdncgLSAyMDBweCknCiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8qKiDlpITnkIbpobXpnaLlj6/op4HmgKflj5jljJYgKi8KICAgIGhhbmRsZVZpc2liaWxpdHlDaGFuZ2UoKSB7CiAgICAgIGlmIChkb2N1bWVudC5oaWRkZW4pIHsKICAgICAgICBjb25zb2xlLmxvZygn8J+Rge+4jyBb6aG16Z2i54q25oCBXSDpobXpnaLlj5jkuLrkuI3lj6/op4HvvIzmmoLlgZzoh6rliqjliLfmlrAnKQogICAgICAgIC8vIOmhtemdouS4jeWPr+ingeaXtuaaguWBnOiHquWKqOWIt+aWsAogICAgICAgIGlmICh0aGlzLnJlZnJlc2hUaW1lcikgewogICAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnJlZnJlc2hUaW1lcikKICAgICAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gbnVsbAogICAgICAgICAgdGhpcy53YXNBdXRvUmVmcmVzaEFjdGl2ZSA9IHRoaXMuYXV0b1JlZnJlc2gKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc29sZS5sb2coJ/CfkYHvuI8gW+mhtemdoueKtuaAgV0g6aG16Z2i5Y+Y5Li65Y+v6KeB77yM5oGi5aSN6Ieq5Yqo5Yi35pawJykKICAgICAgICAvLyDpobXpnaLlj6/op4Hml7bmgaLlpI3oh6rliqjliLfmlrAKICAgICAgICBpZiAodGhpcy53YXNBdXRvUmVmcmVzaEFjdGl2ZSAmJiB0aGlzLmF1dG9SZWZyZXNoKSB7CiAgICAgICAgICB0aGlzLnRvZ2dsZUF1dG9SZWZyZXNoKHRydWUpCiAgICAgICAgfQoKICAgICAgICAvLyDpobXpnaLph43mlrDlj6/op4Hml7bvvIzlpoLmnpzmnInpgInmi6nnirbmgIHpnIDopoHmgaLlpI3vvIznq4vljbPmgaLlpI0KICAgICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID4gMCkgewogICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgW+mhtemdouWPr+ingV0g6aG16Z2i6YeN5paw5Y+v6KeB77yM5qOA5p+l6YCJ5oup54q25oCBJykKICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICB0aGlzLnJlc3RvcmVTZWxlY3RlZFN0YXRlKCkKICAgICAgICAgIH0sIDEwMCkKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICBjb25zb2xlLmxvZygn8J+nuSBb57uE5Lu26ZSA5q+BXSDlvIDlp4vmuIXnkIbnu4Tku7botYTmupAnKQoKICAgIGlmICh0aGlzLnJlZnJlc2hUaW1lcikgewogICAgICBjbGVhckludGVydmFsKHRoaXMucmVmcmVzaFRpbWVyKQogICAgICBjb25zb2xlLmxvZygn8J+nuSBb57uE5Lu26ZSA5q+BXSDmuIXnkIboh6rliqjliLfmlrDlrprml7blmagnKQogICAgfQoKICAgIC8vIOenu+mZpOS6i+S7tuebkeWQrOWZqAogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKQogICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigndmlzaWJpbGl0eWNoYW5nZScsIHRoaXMuaGFuZGxlVmlzaWJpbGl0eUNoYW5nZSkKICAgIGNvbnNvbGUubG9nKCfwn6e5IFvnu4Tku7bplIDmr4FdIOa4heeQhuS6i+S7tuebkeWQrOWZqCcpCgogICAgLy8g5riF55CG5L6n6L655qCP6KeC5a+f5ZmoCiAgICBpZiAodGhpcy5zaWRlYmFyT2JzZXJ2ZXIpIHsKICAgICAgdGhpcy5zaWRlYmFyT2JzZXJ2ZXIuZGlzY29ubmVjdCgpCiAgICAgIGNvbnNvbGUubG9nKCfwn6e5IFvnu4Tku7bplIDmr4FdIOa4heeQhuS+p+i+ueagj+inguWvn+WZqCcpCiAgICB9CgogICAgY29uc29sZS5sb2coJ+KchSBb57uE5Lu26ZSA5q+BXSDnu4Tku7botYTmupDmuIXnkIblrozmiJAnKQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/rms/prescription/review", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"搜索关键字\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 工具栏 -->\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"自动刷新\"\n          inactive-text=\"手动刷新\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <!-- 主要内容区域 -->\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 处方列表 -->\n      <el-col :span=\"8\">\n        <el-card class=\"list-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>待审核处方列表</span>\n            <span class=\"header-info\">（共 {{ total }} 条）</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"prescriptionList\"\n            @selection-change=\"handleSelectionChange\"\n            @row-click=\"handleRowClick\"\n            highlight-current-row\n            :height=\"tableHeight\"\n            ref=\"prescriptionTable\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"100\" show-overflow-tooltip />\n            <el-table-column label=\"医生\" align=\"center\" prop=\"doctName\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"患者\" align=\"center\" prop=\"name\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"严重程度\" align=\"center\" width=\"90\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                  {{ getSeverityText(scope.row.level) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n            class=\"pagination-container\"\n          />\n        </el-card>\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"16\">\n        <el-card class=\"detail-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n            <span class=\"header-info\">（{{ currentPrescription.code }}）</span>\n          </div>\n\n          <div class=\"detail-content\" :style=\"{ height: detailHeight, overflowY: 'auto' }\">\n            <!-- 基本信息 - 紧凑布局 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">处方号：</span>{{ currentPrescription.presId }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"16\"><span class=\"label\">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 诊断信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">诊断信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 医生和患者信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">医生和患者信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">科室：</span>{{ currentPrescription.deptName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">医生：</span>{{ currentPrescription.doctName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">患者：</span>{{ currentPrescription.name }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"6\"><span class=\"label\">性别：</span>{{ currentPrescription.sex }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">年龄：</span>{{ calculateAge(currentPrescription.birth) }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">身高：</span>{{ currentPrescription.height || '未知' }}cm</el-col>\n                <el-col :span=\"6\"><span class=\"label\">体重：</span>{{ currentPrescription.weight || '未知' }}kg</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 药品明细 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">药品明细</h4>\n              <el-table :data=\"medicationList\" size=\"mini\" border max-height=\"200\">\n                <el-table-column label=\"药名\" prop=\"medName\" min-width=\"120\" show-overflow-tooltip />\n                <el-table-column label=\"组号\" prop=\"group\" width=\"50\" />\n                <el-table-column label=\"规格\" prop=\"spec\" width=\"80\" show-overflow-tooltip />\n                <el-table-column label=\"给药途径\" prop=\"administer\" width=\"70\" />\n                <el-table-column label=\"单次量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.dose }}{{ scope.row.doseUnit }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"频次\" prop=\"freq\" width=\"50\" />\n                <el-table-column label=\"天数\" prop=\"days\" width=\"50\" />\n                <el-table-column label=\"开药数量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.ordQty }}{{ scope.row.ordUom }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"金额\" prop=\"money\" width=\"60\" />\n                <el-table-column label=\"用药说明\" prop=\"yysm\" min-width=\"100\" show-overflow-tooltip />\n              </el-table>\n            </div>\n\n            <!-- 处方分析结果 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方分析结果</h4>\n              <el-tabs v-model=\"activeTab\" type=\"card\" class=\"compact-tabs\">\n                <el-tab-pane name=\"important\">\n                  <span slot=\"label\">\n                    重要问题 <el-badge :value=\"getAnalysisResultsByLevel('重要').length\" v-if=\"getAnalysisResultsByLevel('重要').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"general\">\n                  <span slot=\"label\">\n                    一般问题 <el-badge :value=\"getAnalysisResultsByLevel('一般').length\" v-if=\"getAnalysisResultsByLevel('一般').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"other\">\n                  <span slot=\"label\">\n                    其它问题 <el-badge :value=\"getAnalysisResultsByLevel('其它').length\" v-if=\"getAnalysisResultsByLevel('其它').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n              </el-tabs>\n            </div>\n\n            <!-- 历史审核记录 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">历史审核记录</h4>\n              <el-table :data=\"reviewHistory\" size=\"mini\" border max-height=\"120\">\n                <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"80\" />\n                <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"130\">\n                  <template slot-scope=\"scope\">\n                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"审核意见\" prop=\"text\" min-width=\"150\" show-overflow-tooltip />\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n        <el-card v-else class=\"detail-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 固定在底部的审核操作区域 -->\n    <div class=\"review-actions-fixed\">\n      <el-card class=\"actions-card\">\n        <el-row :gutter=\"15\" align=\"middle\" type=\"flex\">\n          <el-col :span=\"8\">\n            <el-select\n              v-model=\"selectedProblems\"\n              multiple\n              placeholder=\"选择问题类型\"\n              style=\"width: 100%\"\n              size=\"small\"\n              collapse-tags>\n              <el-option\n                v-for=\"problem in problemTypes\"\n                :key=\"problem.cfwtbh\"\n                :label=\"problem.cfwtbh + ' ' +problem.cfwtname\"\n                :value=\"problem.cfwtname\">\n              </el-option>\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"danger\"\n              icon=\"el-icon-close\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleReject\"\n              v-hasPermi=\"['rms:prescription:review:reject']\"\n              style=\"width: 100%\">\n              审核打回\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-check\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleApprove\"\n              v-hasPermi=\"['rms:prescription:review:approve']\"\n              style=\"width: 100%\">\n              审核通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"8\" class=\"text-right\">\n            <span class=\"selection-info\">\n              已选择 <strong>{{ ids.length }}</strong> 条处方\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"clearSelection\"\n                v-if=\"ids.length > 0\">\n                清空选择\n              </el-button>\n            </span>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 选中的处方对象数组（用于状态持久化）\n      selectedPrescriptions: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 是否由自动刷新触发的标记\n      isAutoRefreshTriggered: false,\n      // 页面不可见前是否开启了自动刷新\n      wasAutoRefreshActive: false,\n      // 是否正在恢复选择状态（用于阻止意外的选择变化事件）\n      isRestoringSelection: false,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  computed: {\n    /** 计算表格高度 */\n    tableHeight() {\n      // 100vh - 顶部导航(50px) - 面包屑(40px) - 搜索区域(120px) - 卡片头部(60px) - 分页区域(50px) - 底部审核区域(80px) - 边距(40px)\n      return 'calc(100vh - 440px)'\n    },\n    /** 计算详情区域高度 */\n    detailHeight() {\n      // 与表格高度保持一致\n      return 'calc(100vh - 380px)'\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n    this.loadSelectedState()\n  },\n  mounted() {\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize)\n    // 监听侧边栏状态变化\n    this.checkSidebarStatus()\n    // 监听页面可见性变化\n    document.addEventListener('visibilitychange', this.handleVisibilityChange)\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      console.log('🔄 [数据加载] 开始加载处方列表', {\n        是否自动刷新: this.autoRefresh,\n        当前页码: this.queryParams.pageNum,\n        页面大小: this.queryParams.pageSize,\n        加载时间: new Date().toLocaleTimeString(),\n        当前选择状态: this.ids\n      })\n\n      // 在数据加载前保存当前的选择状态\n      const savedIds = [...this.ids]\n      console.log('💾 [状态备份] 备份当前选择状态', { 备份的ids: savedIds })\n\n      this.loading = true\n      // 设置恢复状态标记，防止表格重新渲染时清空选择\n      this.isRestoringSelection = true\n\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        console.log('📊 [数据加载] 处方列表加载完成', {\n          数据条数: response.rows.length,\n          总数: response.total,\n          处方编码列表: response.rows.map(item => item.code)\n        })\n\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n\n        // 恢复备份的选择状态\n        this.ids = savedIds\n\n        console.log('🔄 [状态恢复] 准备恢复选择状态', {\n          备份的ids: savedIds,\n          当前ids: this.ids,\n          ids数量: this.ids.length,\n          表格ref存在: !!this.$refs.prescriptionTable,\n          是否自动刷新触发: this.isAutoRefreshTriggered\n        })\n\n        // 根据是否自动刷新使用不同的恢复策略\n        if (this.isAutoRefreshTriggered) {\n          console.log('🔄 [自动刷新] 使用自动刷新专用恢复机制')\n          this.handleAutoRefreshRestore()\n          this.isAutoRefreshTriggered = false // 重置标记\n        } else {\n          console.log('👆 [手动操作] 使用常规恢复机制')\n          // 恢复选择状态 - 确保DOM完全更新后再恢复\n          this.$nextTick(() => {\n            console.log('⏰ [延迟恢复] $nextTick执行，准备延迟恢复状态')\n            // 延迟一小段时间确保表格完全渲染\n            setTimeout(() => {\n              console.log('🎯 [开始恢复] 开始执行状态恢复')\n              this.restoreSelectedState()\n            }, 200) // 增加延迟时间到200ms\n          })\n        }\n      }).catch(error => {\n        this.loading = false\n        this.isRestoringSelection = false // 出错时也要重置标记\n        console.error('❌ [加载失败] 获取处方列表失败:', error)\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      console.log('🔄 [选择变化] 触发选择变化事件', {\n        选中数量: selection.length,\n        选中处方: selection.map(item => ({ code: item.code, name: item.name })),\n        触发时间: new Date().toLocaleTimeString(),\n        正在恢复状态: this.isRestoringSelection\n      })\n\n      // 如果正在恢复选择状态，忽略这次选择变化事件\n      if (this.isRestoringSelection) {\n        console.log('🚫 [忽略事件] 正在恢复选择状态，忽略此次选择变化事件')\n        return\n      }\n\n      this.ids = selection.map(item => item.code)\n      this.selectedPrescriptions = selection\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n\n      console.log('💾 [状态更新] 更新组件状态', {\n        ids: this.ids,\n        single: this.single,\n        multiple: this.multiple\n      })\n\n      // 保存选择状态到本地存储\n      this.saveSelectedState()\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      console.log('🔄 [自动刷新] 切换自动刷新状态', {\n        开启: value,\n        刷新间隔: this.refreshInterval,\n        当前选择数量: this.ids.length\n      })\n\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          console.log('⏰ [自动刷新] 自动刷新定时器触发', {\n            触发时间: new Date().toLocaleTimeString(),\n            当前选择状态: this.ids\n          })\n\n          // 标记这是自动刷新触发的数据加载\n          this.isAutoRefreshTriggered = true\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    },\n\n    /** 保存选择状态到本地存储 */\n    saveSelectedState() {\n      const selectedCodes = this.ids\n      console.log('💾 [保存状态] 保存选择状态到localStorage', {\n        选中的处方编码: selectedCodes,\n        数量: selectedCodes.length,\n        保存时间: new Date().toLocaleTimeString()\n      })\n\n      localStorage.setItem('prescription_review_selected', JSON.stringify(selectedCodes))\n\n      // 验证保存是否成功\n      const saved = localStorage.getItem('prescription_review_selected')\n      console.log('✅ [保存验证] localStorage保存结果', {\n        保存的数据: saved,\n        解析后: JSON.parse(saved || '[]')\n      })\n    },\n\n    /** 从本地存储加载选择状态 */\n    loadSelectedState() {\n      console.log('📖 [加载状态] 开始从localStorage加载选择状态')\n\n      try {\n        const savedSelection = localStorage.getItem('prescription_review_selected')\n        console.log('📖 [加载状态] localStorage中的数据', {\n          原始数据: savedSelection,\n          数据类型: typeof savedSelection\n        })\n\n        if (savedSelection) {\n          const parsedIds = JSON.parse(savedSelection)\n          this.ids = parsedIds\n          this.multiple = this.ids.length === 0\n          this.single = this.ids.length !== 1\n\n          console.log('✅ [加载成功] 状态加载完成', {\n            加载的ids: this.ids,\n            数量: this.ids.length,\n            multiple: this.multiple,\n            single: this.single\n          })\n        } else {\n          console.log('ℹ️ [加载状态] localStorage中没有保存的选择状态')\n          this.ids = []\n        }\n      } catch (error) {\n        console.error('❌ [加载失败] 加载选择状态失败:', error)\n        this.ids = []\n      }\n    },\n\n    /** 恢复表格选择状态 */\n    restoreSelectedState() {\n      console.log('🎯 [状态恢复] 开始恢复表格选择状态', {\n        需要恢复的ids: this.ids,\n        ids数量: this.ids.length,\n        表格ref存在: !!this.$refs.prescriptionTable,\n        处方列表长度: this.prescriptionList.length,\n        处方列表编码: this.prescriptionList.map(item => item.code)\n      })\n\n      // 检查前置条件\n      if (this.ids.length === 0) {\n        console.log('ℹ️ [状态恢复] 没有需要恢复的选择状态')\n        return\n      }\n\n      if (!this.$refs.prescriptionTable) {\n        console.error('❌ [状态恢复] 表格ref不存在')\n        return\n      }\n\n      if (this.prescriptionList.length === 0) {\n        console.log('ℹ️ [状态恢复] 处方列表为空，无法恢复状态')\n        return\n      }\n\n      try {\n        // 清除当前选择\n        console.log('🧹 [清除选择] 清除表格当前选择状态')\n        this.$refs.prescriptionTable.clearSelection()\n\n        // 恢复选择状态\n        let restoredCount = 0\n        let matchedRows = []\n        let unmatchedIds = []\n\n        this.prescriptionList.forEach(row => {\n          if (this.ids.includes(row.code)) {\n            console.log(`✅ [匹配成功] 找到匹配的处方: ${row.code} - ${row.name}`)\n            this.$refs.prescriptionTable.toggleRowSelection(row, true)\n            restoredCount++\n            matchedRows.push({ code: row.code, name: row.name })\n          }\n        })\n\n        // 检查未匹配的ID\n        this.ids.forEach(id => {\n          if (!this.prescriptionList.find(row => row.code === id)) {\n            unmatchedIds.push(id)\n          }\n        })\n\n        // 更新选择状态统计\n        this.multiple = this.ids.length === 0\n        this.single = this.ids.length !== 1\n\n        console.log('📊 [恢复结果] 状态恢复完成', {\n          恢复成功数量: restoredCount,\n          匹配的处方: matchedRows,\n          未匹配的ID: unmatchedIds,\n          最终状态: {\n            multiple: this.multiple,\n            single: this.single,\n            ids: this.ids\n          }\n        })\n\n        // 验证恢复结果\n        this.$nextTick(() => {\n          const selectedRows = this.$refs.prescriptionTable.selection || []\n          console.log('🔍 [恢复验证] 验证表格选择状态', {\n            表格选中行数: selectedRows.length,\n            表格选中编码: selectedRows.map(row => row.code),\n            期望选中数量: this.ids.length,\n            状态一致: selectedRows.length === restoredCount\n          })\n\n          if (selectedRows.length !== restoredCount) {\n            console.warn('⚠️ [状态不一致] 表格选择状态与预期不符，尝试重新恢复')\n            // 如果状态不一致，再次尝试恢复\n            setTimeout(() => {\n              this.restoreSelectedStateRetry()\n            }, 100)\n          } else {\n            // 恢复成功，重置恢复状态标记\n            console.log('✅ [恢复完成] 状态恢复成功，重置恢复标记')\n            this.isRestoringSelection = false\n          }\n        })\n\n      } catch (error) {\n        console.error('❌ [恢复失败] 恢复选择状态失败:', error)\n      }\n    },\n\n    /** 清空选择 */\n    clearSelection() {\n      // 设置恢复标记，防止clearSelection触发选择变化事件\n      this.isRestoringSelection = true\n\n      this.ids = []\n      this.selectedPrescriptions = []\n      this.multiple = true\n      this.single = true\n\n      // 清除表格选择\n      if (this.$refs.prescriptionTable) {\n        this.$refs.prescriptionTable.clearSelection()\n      }\n\n      // 清除本地存储\n      localStorage.removeItem('prescription_review_selected')\n\n      console.log('🧹 [清空选择] 选择状态已清空', {\n        清空时间: new Date().toLocaleTimeString()\n      })\n\n      // 重置恢复标记\n      setTimeout(() => {\n        this.isRestoringSelection = false\n      }, 100)\n    },\n\n    /** 重试恢复选择状态 */\n    restoreSelectedStateRetry() {\n      console.log('🔄 [重试恢复] 开始重试恢复选择状态')\n\n      if (!this.$refs.prescriptionTable) {\n        console.error('❌ [重试失败] 表格ref仍然不存在')\n        this.isRestoringSelection = false\n        return\n      }\n\n      // 强制清除选择\n      this.$refs.prescriptionTable.clearSelection()\n\n      // 重新恢复\n      let retryCount = 0\n      this.prescriptionList.forEach(row => {\n        if (this.ids.includes(row.code)) {\n          this.$refs.prescriptionTable.toggleRowSelection(row, true)\n          retryCount++\n          console.log(`🔄 [重试恢复] 重新选择处方: ${row.code}`)\n        }\n      })\n\n      console.log(`✅ [重试完成] 重试恢复了 ${retryCount} 个处方的选择状态`)\n\n      // 重试完成后重置标记\n      this.isRestoringSelection = false\n    },\n\n    /** 自动刷新时的特殊处理 */\n    handleAutoRefreshRestore() {\n      console.log('🔄 [自动刷新] 自动刷新触发，特殊处理选择状态恢复')\n\n      // 在自动刷新时，给更多时间让表格完全渲染\n      this.$nextTick(() => {\n        setTimeout(() => {\n          console.log('🎯 [自动刷新恢复] 开始自动刷新后的状态恢复')\n          this.restoreSelectedState()\n\n          // 额外验证\n          setTimeout(() => {\n            const selectedRows = this.$refs.prescriptionTable?.selection || []\n            if (selectedRows.length === 0 && this.ids.length > 0) {\n              console.warn('⚠️ [自动刷新] 第一次恢复失败，进行第二次尝试')\n              this.restoreSelectedStateRetry()\n            } else {\n              // 自动刷新恢复成功，重置标记\n              console.log('✅ [自动刷新] 自动刷新恢复成功，重置恢复标记')\n              this.isRestoringSelection = false\n            }\n          }, 300)\n        }, 300) // 自动刷新时使用更长的延迟\n      })\n    },\n\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 窗口大小变化时，强制重新计算表格高度\n      this.$nextTick(() => {\n        if (this.$refs.prescriptionTable) {\n          this.$refs.prescriptionTable.doLayout()\n        }\n      })\n    },\n\n    /** 检查侧边栏状态 */\n    checkSidebarStatus() {\n      // 检查body是否有hideSidebar类\n      const body = document.body\n      if (body.classList.contains('hideSidebar')) {\n        // 侧边栏已收起\n        this.updateFixedAreaPosition(true)\n      } else {\n        // 侧边栏展开\n        this.updateFixedAreaPosition(false)\n      }\n\n      // 监听侧边栏状态变化\n      const observer = new MutationObserver((mutations) => {\n        mutations.forEach((mutation) => {\n          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {\n            const isHidden = body.classList.contains('hideSidebar')\n            this.updateFixedAreaPosition(isHidden)\n          }\n        })\n      })\n\n      observer.observe(body, {\n        attributes: true,\n        attributeFilter: ['class']\n      })\n\n      // 保存observer引用以便清理\n      this.sidebarObserver = observer\n    },\n\n    /** 更新固定区域位置 */\n    updateFixedAreaPosition(isHidden) {\n      const fixedArea = document.querySelector('.review-actions-fixed')\n      if (fixedArea) {\n        if (isHidden) {\n          fixedArea.style.left = '54px'\n          fixedArea.style.maxWidth = 'calc(100vw - 54px)'\n        } else {\n          fixedArea.style.left = '200px'\n          fixedArea.style.maxWidth = 'calc(100vw - 200px)'\n        }\n      }\n    },\n\n    /** 处理页面可见性变化 */\n    handleVisibilityChange() {\n      if (document.hidden) {\n        console.log('👁️ [页面状态] 页面变为不可见，暂停自动刷新')\n        // 页面不可见时暂停自动刷新\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n          this.wasAutoRefreshActive = this.autoRefresh\n        }\n      } else {\n        console.log('👁️ [页面状态] 页面变为可见，恢复自动刷新')\n        // 页面可见时恢复自动刷新\n        if (this.wasAutoRefreshActive && this.autoRefresh) {\n          this.toggleAutoRefresh(true)\n        }\n\n        // 页面重新可见时，如果有选择状态需要恢复，立即恢复\n        if (this.ids.length > 0) {\n          console.log('🔄 [页面可见] 页面重新可见，检查选择状态')\n          setTimeout(() => {\n            this.restoreSelectedState()\n          }, 100)\n        }\n      }\n    }\n  },\n  beforeDestroy() {\n    console.log('🧹 [组件销毁] 开始清理组件资源')\n\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n      console.log('🧹 [组件销毁] 清理自动刷新定时器')\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('resize', this.handleResize)\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange)\n    console.log('🧹 [组件销毁] 清理事件监听器')\n\n    // 清理侧边栏观察器\n    if (this.sidebarObserver) {\n      this.sidebarObserver.disconnect()\n      console.log('🧹 [组件销毁] 清理侧边栏观察器')\n    }\n\n    console.log('✅ [组件销毁] 组件资源清理完成')\n  }\n}\n</script>\n\n<style scoped>\n/* 主要内容区域 */\n.app-container {\n  /*padding-bottom: 80px; !* 为固定的审核区域留出空间 *!*/\n}\n\n.main-content {\n  height: calc(100vh - 240px); /* 顶部导航(50px) + 面包屑(40px) + 搜索区域(80px) + 工具栏(40px) + 底部审核区域(80px) - 一些边距 */\n}\n\n/* 处方列表卡片 */\n.list-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.list-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.pagination-container {\n  margin-top: 10px;\n  text-align: center;\n  flex-shrink: 0;\n}\n\n/* 处方详情卡片 */\n.detail-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  overflow: hidden;\n}\n\n.detail-content {\n  padding-right: 5px;\n}\n\n/* 信息区域样式 */\n.info-section {\n  margin-bottom: 15px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 10px;\n  background-color: #fafafa;\n}\n\n.section-title {\n  margin: 0 0 10px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 14px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 5px;\n}\n\n.info-row {\n  margin-bottom: 8px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.label {\n  font-weight: 500;\n  color: #606266;\n  margin-right: 5px;\n}\n\n/* 紧凑的标签页 */\n.compact-tabs .el-tabs__header {\n  margin-bottom: 10px;\n}\n\n.compact-tabs .el-tab-pane {\n  padding: 0;\n}\n\n/* 固定在底部的审核操作区域 */\n.review-actions-fixed {\n  position: fixed;\n  bottom: 0;\n  left: 200px; /* 侧边栏宽度 */\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(to top, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);\n  backdrop-filter: blur(5px);\n  border-top: 1px solid #e4e7ed;\n  box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.1);\n  max-width: calc(100vw - 200px); /* 限制最大宽度，减去侧边栏宽度 */\n}\n\n/* 当侧边栏收起时的适配 */\n.hideSidebar .review-actions-fixed {\n  left: 54px; /* 收起后的侧边栏宽度 */\n  max-width: calc(100vw - 54px);\n}\n\n/* 移动端适配 */\n@media (max-width: 768px) {\n  .review-actions-fixed {\n    left: 0;\n    right: 0;\n    max-width: 100vw;\n  }\n}\n\n.actions-card {\n  margin: 0;\n  border: none;\n  border-radius: 0;\n  box-shadow: none;\n}\n\n.actions-card .el-card__body {\n  padding: 15px 20px;\n}\n\n.selection-info {\n  color: #606266;\n  font-size: 14px;\n}\n\n.selection-info strong {\n  color: #409eff;\n}\n\n/* 头部信息样式 */\n.header-info {\n  color: #909399;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .main-content .el-col:first-child {\n    margin-bottom: 20px;\n  }\n\n  .info-row .el-col {\n    margin-bottom: 5px;\n  }\n\n  /* 调整小屏幕下的高度计算 */\n  .main-content {\n    height: calc(100vh - 280px);\n  }\n}\n\n@media (max-width: 768px) {\n  .main-content {\n    height: calc(100vh - 320px);\n  }\n\n  .review-actions-fixed .el-row .el-col {\n    margin-bottom: 10px;\n  }\n\n  .actions-card .el-card__body {\n    padding: 10px 15px;\n  }\n}\n\n/* 超大屏幕优化 */\n@media (min-width: 1920px) {\n  .main-content {\n    height: calc(100vh - 220px);\n  }\n}\n\n/* 通用样式 */\n.text-center {\n  text-align: center;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-table {\n  margin-bottom: 5px;\n}\n\n/* 表格优化 */\n.el-table .cell {\n  padding: 0 5px;\n}\n\n.el-table--mini td {\n  padding: 4px 0;\n}\n\n/* 徽章样式 */\n.el-badge {\n  margin-left: 5px;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"]}]}