{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752980811737}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGVuZGluZ1ByZXNjcmlwdGlvbnMsCiAgZ2V0UHJlc2NyaXB0aW9uRGV0YWlsLAogIGdldFByb2JsZW1UeXBlcywKICBnZXRSZWZyZXNoVGltZSwKICBhcHByb3ZlUHJlc2NyaXB0aW9ucywKICByZWplY3RQcmVzY3JpcHRpb25zLAogIGdldERlcGFydG1lbnRzCn0gZnJvbSAiQC9hcGkvcm1zL3ByZXNjcmlwdGlvbnJldmlldyIKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUHJlc2NyaXB0aW9uUmV2aWV3IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5Yqg6L2954q25oCBCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpgInkuK3nmoTlpITmlrnlr7nosaHmlbDnu4TvvIjnlKjkuo7nirbmgIHmjIHkuYXljJbvvIkKICAgICAgc2VsZWN0ZWRQcmVzY3JpcHRpb25zOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlpITmlrnliJfooagKICAgICAgcHJlc2NyaXB0aW9uTGlzdDogW10sCiAgICAgIC8vIOW9k+WJjemAieS4reeahOWkhOaWuQogICAgICBjdXJyZW50UHJlc2NyaXB0aW9uOiBudWxsLAogICAgICAvLyDoja/lk4HmmI7nu4bliJfooagKICAgICAgbWVkaWNhdGlvbkxpc3Q6IFtdLAogICAgICAvLyDliIbmnpDnu5PmnpzliJfooagKICAgICAgYW5hbHlzaXNSZXN1bHRzOiBbXSwKICAgICAgLy8g5a6h5qC45Y6G5Y+y5YiX6KGoCiAgICAgIHJldmlld0hpc3Rvcnk6IFtdLAogICAgICAvLyDnp5HlrqTliJfooagKICAgICAgZGVwYXJ0bWVudExpc3Q6IFtdLAogICAgICAvLyDpl67popjnsbvlnovliJfooagKICAgICAgcHJvYmxlbVR5cGVzOiBbXSwKICAgICAgLy8g6YCJ5Lit55qE6Zeu6aKY57G75Z6LCiAgICAgIHNlbGVjdGVkUHJvYmxlbXM6IFtdLAogICAgICAvLyDoh6rliqjliLfmlrDlvIDlhbMKICAgICAgYXV0b1JlZnJlc2g6IGZhbHNlLAogICAgICAvLyDliLfmlrDlrprml7blmagKICAgICAgcmVmcmVzaFRpbWVyOiBudWxsLAogICAgICAvLyDliLfmlrDpl7TpmpTvvIjmr6vnp5LvvIkKICAgICAgcmVmcmVzaEludGVydmFsOiA1MDAwLAogICAgICAvLyDlvZPliY3mtLvot4PnmoTmoIfnrb7pobUKICAgICAgYWN0aXZlVGFiOiAnaW1wb3J0YW50JywKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgZGVwdENvZGU6IG51bGwsCiAgICAgICAgcHJlc2NyaXB0aW9uVHlwZTogJycsCiAgICAgICAga2V5d29yZDogbnVsbAogICAgICB9CiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgLyoqIOiuoeeul+ihqOagvOmrmOW6piAqLwogICAgdGFibGVIZWlnaHQoKSB7CiAgICAgIC8vIDEwMHZoIC0g6aG26YOo5a+86IiqKDUwcHgpIC0g6Z2i5YyF5bGRKDQwcHgpIC0g5pCc57Si5Yy65Z+fKDEyMHB4KSAtIOWNoeeJh+WktOmDqCg2MHB4KSAtIOWIhumhteWMuuWfnyg1MHB4KSAtIOW6lemDqOWuoeaguOWMuuWfnyg4MHB4KSAtIOi+uei3nSg0MHB4KQogICAgICByZXR1cm4gJ2NhbGMoMTAwdmggLSA0NDBweCknCiAgICB9LAogICAgLyoqIOiuoeeul+ivpuaDheWMuuWfn+mrmOW6piAqLwogICAgZGV0YWlsSGVpZ2h0KCkgewogICAgICAvLyDkuI7ooajmoLzpq5jluqbkv53mjIHkuIDoh7QKICAgICAgcmV0dXJuICdjYWxjKDEwMHZoIC0gMzgwcHgpJwogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB0aGlzLmdldERlcGFydG1lbnRMaXN0KCkKICAgIHRoaXMuZ2V0UHJvYmxlbVR5cGVMaXN0KCkKICAgIHRoaXMuZ2V0UmVmcmVzaENvbmZpZygpCiAgICB0aGlzLmxvYWRTZWxlY3RlZFN0YXRlKCkKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvLyDnm5HlkKznqpflj6PlpKflsI/lj5jljJYKICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLmhhbmRsZVJlc2l6ZSkKICAgIC8vIOebkeWQrOS+p+i+ueagj+eKtuaAgeWPmOWMlgogICAgdGhpcy5jaGVja1NpZGViYXJTdGF0dXMoKQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouWkhOaWueWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICAvLyDmnoTlu7rmn6Xor6Llj4LmlbAKICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9CgogICAgICAvLyDlpITnkIblhbPplK7lrZfmkJzntKIKICAgICAgaWYgKHBhcmFtcy5rZXl3b3JkKSB7CiAgICAgICAgcGFyYW1zLmRvY3ROYW1lID0gcGFyYW1zLmtleXdvcmQKICAgICAgICBwYXJhbXMubmFtZSA9IHBhcmFtcy5rZXl3b3JkCiAgICAgIH0KCiAgICAgIGdldFBlbmRpbmdQcmVzY3JpcHRpb25zKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wcmVzY3JpcHRpb25MaXN0ID0gcmVzcG9uc2Uucm93cwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbAogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCgogICAgICAgIC8vIOaBouWkjemAieaLqeeKtuaAgSAtIOehruS/nURPTeWujOWFqOabtOaWsOWQjuWGjeaBouWkjQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIC8vIOW7tui/n+S4gOWwj+auteaXtumXtOehruS/neihqOagvOWujOWFqOa4suafkwogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMucmVzdG9yZVNlbGVjdGVkU3RhdGUoKQogICAgICAgICAgfSwgMTAwKQogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWkhOaWueWIl+ihqOWksei0pTonLCBlcnJvcikKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCgogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uY29kZSkKICAgICAgdGhpcy5zZWxlY3RlZFByZXNjcmlwdGlvbnMgPSBzZWxlY3Rpb24KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAoKICAgICAgLy8g5L+d5a2Y6YCJ5oup54q25oCB5Yiw5pys5Zyw5a2Y5YKoCiAgICAgIHRoaXMuc2F2ZVNlbGVjdGVkU3RhdGUoKQogICAgfSwKCiAgICAvKiog6KGM54K55Ye75LqL5Lu2ICovCiAgICBoYW5kbGVSb3dDbGljayhyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50UHJlc2NyaXB0aW9uID0gcm93CiAgICAgIHRoaXMuZ2V0UHJlc2NyaXB0aW9uRGV0YWlsRGF0YShyb3cuY29kZSkKICAgIH0sCgogICAgLyoqIOiOt+WPluWkhOaWueivpuaDhSAqLwogICAgZ2V0UHJlc2NyaXB0aW9uRGV0YWlsRGF0YShjb2RlKSB7CiAgICAgIGdldFByZXNjcmlwdGlvbkRldGFpbChjb2RlKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmN1cnJlbnRQcmVzY3JpcHRpb24gPSByZXNwb25zZS5kYXRhLnByZXNjcmlwdGlvbgogICAgICAgIHRoaXMubWVkaWNhdGlvbkxpc3QgPSByZXNwb25zZS5kYXRhLm1lZGljYXRpb25zIHx8IFtdCiAgICAgICAgdGhpcy5hbmFseXNpc1Jlc3VsdHMgPSByZXNwb25zZS5kYXRhLmFuYWx5c2lzUmVzdWx0cyB8fCBbXQogICAgICAgIHRoaXMucmV2aWV3SGlzdG9yeSA9IHJlc3BvbnNlLmRhdGEucmV2aWV3SGlzdG9yeSB8fCBbXQogICAgICB9KQogICAgfSwKCiAgICAvKiog6I635Y+W56eR5a6k5YiX6KGoICovCiAgICBnZXREZXBhcnRtZW50TGlzdCgpIHsKICAgICAgZ2V0RGVwYXJ0bWVudHMoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRlcGFydG1lbnRMaXN0ID0gcmVzcG9uc2UuZGF0YSB8fCBbXQogICAgICB9KQogICAgfSwKCiAgICAvKiog6I635Y+W6Zeu6aKY57G75Z6L5YiX6KGoICovCiAgICBnZXRQcm9ibGVtVHlwZUxpc3QoKSB7CiAgICAgIGdldFByb2JsZW1UeXBlcygpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucHJvYmxlbVR5cGVzID0gcmVzcG9uc2UuZGF0YSB8fCBbXQogICAgICB9KQogICAgfSwKCiAgICAvKiog6I635Y+W5Yi35paw6YWN572uICovCiAgICBnZXRSZWZyZXNoQ29uZmlnKCkgewogICAgICBnZXRSZWZyZXNoVGltZSgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucmVmcmVzaEludGVydmFsID0gcmVzcG9uc2UuZGF0YSB8fCA1MDAwCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDliIfmjaLoh6rliqjliLfmlrAgKi8KICAgIHRvZ2dsZUF1dG9SZWZyZXNoKHZhbHVlKSB7CiAgICAgIGlmICh2YWx1ZSkgewogICAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB9LCB0aGlzLnJlZnJlc2hJbnRlcnZhbCkKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAodGhpcy5yZWZyZXNoVGltZXIpIHsKICAgICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5yZWZyZXNoVGltZXIpCiAgICAgICAgICB0aGlzLnJlZnJlc2hUaW1lciA9IG51bGwKICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOWuoeaguOmAmui/hyAqLwogICAgaGFuZGxlQXBwcm92ZSgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHlrqHmoLjnmoTlpITmlrkiKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTlrqHmoLjpgJrov4fpgInkuK3nmoTlpITmlrnvvJ8nKS50aGVuKCgpID0+IHsKICAgICAgICByZXR1cm4gYXBwcm92ZVByZXNjcmlwdGlvbnModGhpcy5pZHMpCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgdGhpcy5jbGVhclNlbGVjdGlvbigpCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5a6h5qC46YCa6L+H5oiQ5YqfIikKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pCiAgICB9LAoKICAgIC8qKiDlrqHmoLjmiZPlm54gKi8KICAgIGhhbmRsZVJlamVjdCgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHmiZPlm57nmoTlpITmlrkiKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICBpZiAodGhpcy5zZWxlY3RlZFByb2JsZW1zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6npl67popjnsbvlnosiKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmiZPlm57pgInkuK3nmoTlpITmlrnvvJ8nKS50aGVuKCgpID0+IHsKICAgICAgICBjb25zdCBkYXRhID0gewogICAgICAgICAgY29kZXM6IHRoaXMuaWRzLAogICAgICAgICAgcHJvYmxlbU5hbWVzOiB0aGlzLnNlbGVjdGVkUHJvYmxlbXMKICAgICAgICB9CiAgICAgICAgcmV0dXJuIHJlamVjdFByZXNjcmlwdGlvbnMoZGF0YSkKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB0aGlzLnNlbGVjdGVkUHJvYmxlbXMgPSBbXQogICAgICAgIHRoaXMuY2xlYXJTZWxlY3Rpb24oKQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWuoeaguOaJk+WbnuaIkOWKnyIpCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgfSwKCiAgICAvKiog5qC55o2u6Zeu6aKY562J57qn6I635Y+W5YiG5p6Q57uT5p6cICovCiAgICBnZXRBbmFseXNpc1Jlc3VsdHNCeUxldmVsKGxldmVsKSB7CiAgICAgIHJldHVybiB0aGlzLmFuYWx5c2lzUmVzdWx0cy5maWx0ZXIoaXRlbSA9PiBpdGVtLnd0bHZsID09PSBsZXZlbCkKICAgIH0sCgogICAgLyoqIOiOt+WPluS4pemHjeeoi+W6puexu+WeiyAqLwogICAgZ2V0U2V2ZXJpdHlUeXBlKGxldmVsKSB7CiAgICAgIHN3aXRjaChsZXZlbCkgewogICAgICAgIGNhc2UgJ+mHjeimgSc6IHJldHVybiAnZGFuZ2VyJwogICAgICAgIGNhc2UgJ+S4gOiIrCc6IHJldHVybiAnd2FybmluZycKICAgICAgICBjYXNlICflhbblroMnOiByZXR1cm4gJ2luZm8nCiAgICAgICAgZGVmYXVsdDogcmV0dXJuICdpbmZvJwogICAgICB9CiAgICB9LAoKICAgIC8qKiDojrflj5bkuKXph43nqIvluqbmlofmnKwgKi8KICAgIGdldFNldmVyaXR5VGV4dChsZXZlbCkgewogICAgICByZXR1cm4gbGV2ZWwgfHwgJ+acquefpScKICAgIH0sCgogICAgLyoqIOiuoeeul+W5tOm+hCAqLwogICAgY2FsY3VsYXRlQWdlKGJpcnRoRGF0ZSkgewogICAgICBpZiAoIWJpcnRoRGF0ZSkgcmV0dXJuICfmnKrnn6UnCiAgICAgIGNvbnN0IGJpcnRoID0gbmV3IERhdGUoYmlydGhEYXRlKQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpCiAgICAgIGNvbnN0IGFnZSA9IG5vdy5nZXRGdWxsWWVhcigpIC0gYmlydGguZ2V0RnVsbFllYXIoKQogICAgICByZXR1cm4gYWdlICsgJ+WygScKICAgIH0sCgogICAgLyoqIOS/neWtmOmAieaLqeeKtuaAgeWIsOacrOWcsOWtmOWCqCAqLwogICAgc2F2ZVNlbGVjdGVkU3RhdGUoKSB7CiAgICAgIGNvbnN0IHNlbGVjdGVkQ29kZXMgPSB0aGlzLmlkcwogICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncHJlc2NyaXB0aW9uX3Jldmlld19zZWxlY3RlZCcsIEpTT04uc3RyaW5naWZ5KHNlbGVjdGVkQ29kZXMpKQogICAgfSwKCiAgICAvKiog5LuO5pys5Zyw5a2Y5YKo5Yqg6L296YCJ5oup54q25oCBICovCiAgICBsb2FkU2VsZWN0ZWRTdGF0ZSgpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBzYXZlZFNlbGVjdGlvbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdwcmVzY3JpcHRpb25fcmV2aWV3X3NlbGVjdGVkJykKICAgICAgICBpZiAoc2F2ZWRTZWxlY3Rpb24pIHsKICAgICAgICAgIHRoaXMuaWRzID0gSlNPTi5wYXJzZShzYXZlZFNlbGVjdGlvbikKICAgICAgICAgIHRoaXMubXVsdGlwbGUgPSB0aGlzLmlkcy5sZW5ndGggPT09IDAKICAgICAgICAgIHRoaXMuc2luZ2xlID0gdGhpcy5pZHMubGVuZ3RoICE9PSAxCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUud2Fybign5Yqg6L296YCJ5oup54q25oCB5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuaWRzID0gW10KICAgICAgfQogICAgfSwKCiAgICAvKiog5oGi5aSN6KGo5qC86YCJ5oup54q25oCBICovCiAgICByZXN0b3JlU2VsZWN0ZWRTdGF0ZSgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA+IDAgJiYgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZSAmJiB0aGlzLnByZXNjcmlwdGlvbkxpc3QubGVuZ3RoID4gMCkgewogICAgICAgIHRyeSB7CiAgICAgICAgICAvLyDmuIXpmaTlvZPliY3pgInmi6kKICAgICAgICAgIHRoaXMuJHJlZnMucHJlc2NyaXB0aW9uVGFibGUuY2xlYXJTZWxlY3Rpb24oKQoKICAgICAgICAgIC8vIOaBouWkjemAieaLqeeKtuaAgQogICAgICAgICAgbGV0IHJlc3RvcmVkQ291bnQgPSAwCiAgICAgICAgICB0aGlzLnByZXNjcmlwdGlvbkxpc3QuZm9yRWFjaChyb3cgPT4gewogICAgICAgICAgICBpZiAodGhpcy5pZHMuaW5jbHVkZXMocm93LmNvZGUpKSB7CiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93LCB0cnVlKQogICAgICAgICAgICAgIHJlc3RvcmVkQ291bnQrKwogICAgICAgICAgICB9CiAgICAgICAgICB9KQoKICAgICAgICAgIC8vIOabtOaWsOmAieaLqeeKtuaAgee7n+iuoQogICAgICAgICAgdGhpcy5tdWx0aXBsZSA9IHRoaXMuaWRzLmxlbmd0aCA9PT0gMAogICAgICAgICAgdGhpcy5zaW5nbGUgPSB0aGlzLmlkcy5sZW5ndGggIT09IDEKCiAgICAgICAgICAvLyDosIPor5Xkv6Hmga8KICAgICAgICAgIGlmIChyZXN0b3JlZENvdW50ID4gMCkgewogICAgICAgICAgICBjb25zb2xlLmxvZyhg5oGi5aSN5LqGICR7cmVzdG9yZWRDb3VudH0g5Liq5aSE5pa555qE6YCJ5oup54q25oCBYCkKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgY29uc29sZS5lcnJvcign5oGi5aSN6YCJ5oup54q25oCB5aSx6LSlOicsIGVycm9yKQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvKiog5riF56m66YCJ5oupICovCiAgICBjbGVhclNlbGVjdGlvbigpIHsKICAgICAgdGhpcy5pZHMgPSBbXQogICAgICB0aGlzLnNlbGVjdGVkUHJlc2NyaXB0aW9ucyA9IFtdCiAgICAgIHRoaXMubXVsdGlwbGUgPSB0cnVlCiAgICAgIHRoaXMuc2luZ2xlID0gdHJ1ZQoKICAgICAgLy8g5riF6Zmk6KGo5qC86YCJ5oupCiAgICAgIGlmICh0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlKSB7CiAgICAgICAgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZS5jbGVhclNlbGVjdGlvbigpCiAgICAgIH0KCiAgICAgIC8vIOa4hemZpOacrOWcsOWtmOWCqAogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncHJlc2NyaXB0aW9uX3Jldmlld19zZWxlY3RlZCcpCiAgICB9LAoKICAgIC8qKiDlpITnkIbnqpflj6PlpKflsI/lj5jljJYgKi8KICAgIGhhbmRsZVJlc2l6ZSgpIHsKICAgICAgLy8g56qX5Y+j5aSn5bCP5Y+Y5YyW5pe277yM5by65Yi26YeN5paw6K6h566X6KGo5qC86auY5bqmCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBpZiAodGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZSkgewogICAgICAgICAgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZS5kb0xheW91dCgpCiAgICAgICAgfQogICAgICB9KQogICAgfSwKCiAgICAvKiog5qOA5p+l5L6n6L655qCP54q25oCBICovCiAgICBjaGVja1NpZGViYXJTdGF0dXMoKSB7CiAgICAgIC8vIOajgOafpWJvZHnmmK/lkKbmnIloaWRlU2lkZWJhcuexuwogICAgICBjb25zdCBib2R5ID0gZG9jdW1lbnQuYm9keQogICAgICBpZiAoYm9keS5jbGFzc0xpc3QuY29udGFpbnMoJ2hpZGVTaWRlYmFyJykpIHsKICAgICAgICAvLyDkvqfovrnmoI/lt7LmlLbotbcKICAgICAgICB0aGlzLnVwZGF0ZUZpeGVkQXJlYVBvc2l0aW9uKHRydWUpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5L6n6L655qCP5bGV5byACiAgICAgICAgdGhpcy51cGRhdGVGaXhlZEFyZWFQb3NpdGlvbihmYWxzZSkKICAgICAgfQoKICAgICAgLy8g55uR5ZCs5L6n6L655qCP54q25oCB5Y+Y5YyWCiAgICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IE11dGF0aW9uT2JzZXJ2ZXIoKG11dGF0aW9ucykgPT4gewogICAgICAgIG11dGF0aW9ucy5mb3JFYWNoKChtdXRhdGlvbikgPT4gewogICAgICAgICAgaWYgKG11dGF0aW9uLnR5cGUgPT09ICdhdHRyaWJ1dGVzJyAmJiBtdXRhdGlvbi5hdHRyaWJ1dGVOYW1lID09PSAnY2xhc3MnKSB7CiAgICAgICAgICAgIGNvbnN0IGlzSGlkZGVuID0gYm9keS5jbGFzc0xpc3QuY29udGFpbnMoJ2hpZGVTaWRlYmFyJykKICAgICAgICAgICAgdGhpcy51cGRhdGVGaXhlZEFyZWFQb3NpdGlvbihpc0hpZGRlbikKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9KQoKICAgICAgb2JzZXJ2ZXIub2JzZXJ2ZShib2R5LCB7CiAgICAgICAgYXR0cmlidXRlczogdHJ1ZSwKICAgICAgICBhdHRyaWJ1dGVGaWx0ZXI6IFsnY2xhc3MnXQogICAgICB9KQoKICAgICAgLy8g5L+d5a2Yb2JzZXJ2ZXLlvJXnlKjku6Xkvr/muIXnkIYKICAgICAgdGhpcy5zaWRlYmFyT2JzZXJ2ZXIgPSBvYnNlcnZlcgogICAgfSwKCiAgICAvKiog5pu05paw5Zu65a6a5Yy65Z+f5L2N572uICovCiAgICB1cGRhdGVGaXhlZEFyZWFQb3NpdGlvbihpc0hpZGRlbikgewogICAgICBjb25zdCBmaXhlZEFyZWEgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcucmV2aWV3LWFjdGlvbnMtZml4ZWQnKQogICAgICBpZiAoZml4ZWRBcmVhKSB7CiAgICAgICAgaWYgKGlzSGlkZGVuKSB7CiAgICAgICAgICBmaXhlZEFyZWEuc3R5bGUubGVmdCA9ICc1NHB4JwogICAgICAgICAgZml4ZWRBcmVhLnN0eWxlLm1heFdpZHRoID0gJ2NhbGMoMTAwdncgLSA1NHB4KScKICAgICAgICB9IGVsc2UgewogICAgICAgICAgZml4ZWRBcmVhLnN0eWxlLmxlZnQgPSAnMjAwcHgnCiAgICAgICAgICBmaXhlZEFyZWEuc3R5bGUubWF4V2lkdGggPSAnY2FsYygxMDB2dyAtIDIwMHB4KScKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICBpZiAodGhpcy5yZWZyZXNoVGltZXIpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnJlZnJlc2hUaW1lcikKICAgIH0KICAgIC8vIOenu+mZpOS6i+S7tuebkeWQrOWZqAogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKQoKICAgIC8vIOa4heeQhuS+p+i+ueagj+inguWvn+WZqAogICAgaWYgKHRoaXMuc2lkZWJhck9ic2VydmVyKSB7CiAgICAgIHRoaXMuc2lkZWJhck9ic2VydmVyLmRpc2Nvbm5lY3QoKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/rms/prescription/review", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"搜索关键字\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 工具栏 -->\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"自动刷新\"\n          inactive-text=\"手动刷新\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <!-- 主要内容区域 -->\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 处方列表 -->\n      <el-col :span=\"8\">\n        <el-card class=\"list-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>待审核处方列表</span>\n            <span class=\"header-info\">（共 {{ total }} 条）</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"prescriptionList\"\n            @selection-change=\"handleSelectionChange\"\n            @row-click=\"handleRowClick\"\n            highlight-current-row\n            :height=\"tableHeight\"\n            ref=\"prescriptionTable\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"100\" show-overflow-tooltip />\n            <el-table-column label=\"医生\" align=\"center\" prop=\"doctName\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"患者\" align=\"center\" prop=\"name\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"严重程度\" align=\"center\" width=\"90\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                  {{ getSeverityText(scope.row.level) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n            class=\"pagination-container\"\n          />\n        </el-card>\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"16\">\n        <el-card class=\"detail-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n            <span class=\"header-info\">（{{ currentPrescription.code }}）</span>\n          </div>\n\n          <div class=\"detail-content\" :style=\"{ height: detailHeight, overflowY: 'auto' }\">\n            <!-- 基本信息 - 紧凑布局 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">处方号：</span>{{ currentPrescription.presId }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"16\"><span class=\"label\">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 诊断信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">诊断信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 医生和患者信息 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">医生和患者信息</h4>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"8\"><span class=\"label\">科室：</span>{{ currentPrescription.deptName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">医生：</span>{{ currentPrescription.doctName }}</el-col>\n                <el-col :span=\"8\"><span class=\"label\">患者：</span>{{ currentPrescription.name }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"6\"><span class=\"label\">性别：</span>{{ currentPrescription.sex }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">年龄：</span>{{ calculateAge(currentPrescription.birth) }}</el-col>\n                <el-col :span=\"6\"><span class=\"label\">身高：</span>{{ currentPrescription.height || '未知' }}cm</el-col>\n                <el-col :span=\"6\"><span class=\"label\">体重：</span>{{ currentPrescription.weight || '未知' }}kg</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"12\"><span class=\"label\">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>\n                <el-col :span=\"12\"><span class=\"label\">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>\n              </el-row>\n              <el-row :gutter=\"10\" class=\"info-row\">\n                <el-col :span=\"24\"><span class=\"label\">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>\n              </el-row>\n            </div>\n\n            <!-- 药品明细 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">药品明细</h4>\n              <el-table :data=\"medicationList\" size=\"mini\" border max-height=\"200\">\n                <el-table-column label=\"药名\" prop=\"medName\" min-width=\"120\" show-overflow-tooltip />\n                <el-table-column label=\"组号\" prop=\"group\" width=\"50\" />\n                <el-table-column label=\"规格\" prop=\"spec\" width=\"80\" show-overflow-tooltip />\n                <el-table-column label=\"给药途径\" prop=\"administer\" width=\"70\" />\n                <el-table-column label=\"单次量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.dose }}{{ scope.row.doseUnit }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"频次\" prop=\"freq\" width=\"50\" />\n                <el-table-column label=\"天数\" prop=\"days\" width=\"50\" />\n                <el-table-column label=\"开药数量\" width=\"70\">\n                  <template slot-scope=\"scope\">\n                    {{ scope.row.ordQty }}{{ scope.row.ordUom }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"金额\" prop=\"money\" width=\"60\" />\n                <el-table-column label=\"用药说明\" prop=\"yysm\" min-width=\"100\" show-overflow-tooltip />\n              </el-table>\n            </div>\n\n            <!-- 处方分析结果 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方分析结果</h4>\n              <el-tabs v-model=\"activeTab\" type=\"card\" class=\"compact-tabs\">\n                <el-tab-pane name=\"important\">\n                  <span slot=\"label\">\n                    重要问题 <el-badge :value=\"getAnalysisResultsByLevel('重要').length\" v-if=\"getAnalysisResultsByLevel('重要').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"general\">\n                  <span slot=\"label\">\n                    一般问题 <el-badge :value=\"getAnalysisResultsByLevel('一般').length\" v-if=\"getAnalysisResultsByLevel('一般').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"other\">\n                  <span slot=\"label\">\n                    其它问题 <el-badge :value=\"getAnalysisResultsByLevel('其它').length\" v-if=\"getAnalysisResultsByLevel('其它').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n              </el-tabs>\n            </div>\n\n            <!-- 历史审核记录 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">历史审核记录</h4>\n              <el-table :data=\"reviewHistory\" size=\"mini\" border max-height=\"120\">\n                <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"80\" />\n                <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"130\">\n                  <template slot-scope=\"scope\">\n                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"审核意见\" prop=\"text\" min-width=\"150\" show-overflow-tooltip />\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n        <el-card v-else class=\"detail-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 固定在底部的审核操作区域 -->\n    <div class=\"review-actions-fixed\">\n      <el-card class=\"actions-card\">\n        <el-row :gutter=\"15\" align=\"middle\" type=\"flex\">\n          <el-col :span=\"8\">\n            <el-select\n              v-model=\"selectedProblems\"\n              multiple\n              placeholder=\"选择问题类型\"\n              style=\"width: 100%\"\n              size=\"small\"\n              collapse-tags>\n              <el-option\n                v-for=\"problem in problemTypes\"\n                :key=\"problem.cfwtbh\"\n                :label=\"problem.cfwtbh + problem.cfwtname\"\n                :value=\"problem.cfwtname\">\n              </el-option>\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"danger\"\n              icon=\"el-icon-close\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleReject\"\n              v-hasPermi=\"['rms:prescription:review:reject']\"\n              style=\"width: 100%\">\n              审核打回\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-check\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleApprove\"\n              v-hasPermi=\"['rms:prescription:review:approve']\"\n              style=\"width: 100%\">\n              审核通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"8\" class=\"text-right\">\n            <span class=\"selection-info\">\n              已选择 <strong>{{ ids.length }}</strong> 条处方\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"clearSelection\"\n                v-if=\"ids.length > 0\">\n                清空选择\n              </el-button>\n            </span>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 选中的处方对象数组（用于状态持久化）\n      selectedPrescriptions: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  computed: {\n    /** 计算表格高度 */\n    tableHeight() {\n      // 100vh - 顶部导航(50px) - 面包屑(40px) - 搜索区域(120px) - 卡片头部(60px) - 分页区域(50px) - 底部审核区域(80px) - 边距(40px)\n      return 'calc(100vh - 440px)'\n    },\n    /** 计算详情区域高度 */\n    detailHeight() {\n      // 与表格高度保持一致\n      return 'calc(100vh - 380px)'\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n    this.loadSelectedState()\n  },\n  mounted() {\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize)\n    // 监听侧边栏状态变化\n    this.checkSidebarStatus()\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      this.loading = true\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n\n        // 恢复选择状态 - 确保DOM完全更新后再恢复\n        this.$nextTick(() => {\n          // 延迟一小段时间确保表格完全渲染\n          setTimeout(() => {\n            this.restoreSelectedState()\n          }, 100)\n        })\n      }).catch(error => {\n        this.loading = false\n        console.error('获取处方列表失败:', error)\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.code)\n      this.selectedPrescriptions = selection\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n\n      // 保存选择状态到本地存储\n      this.saveSelectedState()\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    },\n\n    /** 保存选择状态到本地存储 */\n    saveSelectedState() {\n      const selectedCodes = this.ids\n      localStorage.setItem('prescription_review_selected', JSON.stringify(selectedCodes))\n    },\n\n    /** 从本地存储加载选择状态 */\n    loadSelectedState() {\n      try {\n        const savedSelection = localStorage.getItem('prescription_review_selected')\n        if (savedSelection) {\n          this.ids = JSON.parse(savedSelection)\n          this.multiple = this.ids.length === 0\n          this.single = this.ids.length !== 1\n        }\n      } catch (error) {\n        console.warn('加载选择状态失败:', error)\n        this.ids = []\n      }\n    },\n\n    /** 恢复表格选择状态 */\n    restoreSelectedState() {\n      if (this.ids.length > 0 && this.$refs.prescriptionTable && this.prescriptionList.length > 0) {\n        try {\n          // 清除当前选择\n          this.$refs.prescriptionTable.clearSelection()\n\n          // 恢复选择状态\n          let restoredCount = 0\n          this.prescriptionList.forEach(row => {\n            if (this.ids.includes(row.code)) {\n              this.$refs.prescriptionTable.toggleRowSelection(row, true)\n              restoredCount++\n            }\n          })\n\n          // 更新选择状态统计\n          this.multiple = this.ids.length === 0\n          this.single = this.ids.length !== 1\n\n          // 调试信息\n          if (restoredCount > 0) {\n            console.log(`恢复了 ${restoredCount} 个处方的选择状态`)\n          }\n        } catch (error) {\n          console.error('恢复选择状态失败:', error)\n        }\n      }\n    },\n\n    /** 清空选择 */\n    clearSelection() {\n      this.ids = []\n      this.selectedPrescriptions = []\n      this.multiple = true\n      this.single = true\n\n      // 清除表格选择\n      if (this.$refs.prescriptionTable) {\n        this.$refs.prescriptionTable.clearSelection()\n      }\n\n      // 清除本地存储\n      localStorage.removeItem('prescription_review_selected')\n    },\n\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 窗口大小变化时，强制重新计算表格高度\n      this.$nextTick(() => {\n        if (this.$refs.prescriptionTable) {\n          this.$refs.prescriptionTable.doLayout()\n        }\n      })\n    },\n\n    /** 检查侧边栏状态 */\n    checkSidebarStatus() {\n      // 检查body是否有hideSidebar类\n      const body = document.body\n      if (body.classList.contains('hideSidebar')) {\n        // 侧边栏已收起\n        this.updateFixedAreaPosition(true)\n      } else {\n        // 侧边栏展开\n        this.updateFixedAreaPosition(false)\n      }\n\n      // 监听侧边栏状态变化\n      const observer = new MutationObserver((mutations) => {\n        mutations.forEach((mutation) => {\n          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {\n            const isHidden = body.classList.contains('hideSidebar')\n            this.updateFixedAreaPosition(isHidden)\n          }\n        })\n      })\n\n      observer.observe(body, {\n        attributes: true,\n        attributeFilter: ['class']\n      })\n\n      // 保存observer引用以便清理\n      this.sidebarObserver = observer\n    },\n\n    /** 更新固定区域位置 */\n    updateFixedAreaPosition(isHidden) {\n      const fixedArea = document.querySelector('.review-actions-fixed')\n      if (fixedArea) {\n        if (isHidden) {\n          fixedArea.style.left = '54px'\n          fixedArea.style.maxWidth = 'calc(100vw - 54px)'\n        } else {\n          fixedArea.style.left = '200px'\n          fixedArea.style.maxWidth = 'calc(100vw - 200px)'\n        }\n      }\n    }\n  },\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n    }\n    // 移除事件监听器\n    window.removeEventListener('resize', this.handleResize)\n\n    // 清理侧边栏观察器\n    if (this.sidebarObserver) {\n      this.sidebarObserver.disconnect()\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 主要内容区域 */\n.app-container {\n  /*padding-bottom: 80px; !* 为固定的审核区域留出空间 *!*/\n}\n\n.main-content {\n  height: calc(100vh - 240px); /* 顶部导航(50px) + 面包屑(40px) + 搜索区域(80px) + 工具栏(40px) + 底部审核区域(80px) - 一些边距 */\n}\n\n/* 处方列表卡片 */\n.list-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.list-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.pagination-container {\n  margin-top: 10px;\n  text-align: center;\n  flex-shrink: 0;\n}\n\n/* 处方详情卡片 */\n.detail-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  overflow: hidden;\n}\n\n.detail-content {\n  padding-right: 5px;\n}\n\n/* 信息区域样式 */\n.info-section {\n  margin-bottom: 15px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 10px;\n  background-color: #fafafa;\n}\n\n.section-title {\n  margin: 0 0 10px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 14px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 5px;\n}\n\n.info-row {\n  margin-bottom: 8px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.label {\n  font-weight: 500;\n  color: #606266;\n  margin-right: 5px;\n}\n\n/* 紧凑的标签页 */\n.compact-tabs .el-tabs__header {\n  margin-bottom: 10px;\n}\n\n.compact-tabs .el-tab-pane {\n  padding: 0;\n}\n\n/* 固定在底部的审核操作区域 */\n.review-actions-fixed {\n  position: fixed;\n  bottom: 0;\n  left: 200px; /* 侧边栏宽度 */\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(to top, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);\n  backdrop-filter: blur(5px);\n  border-top: 1px solid #e4e7ed;\n  box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.1);\n  max-width: calc(100vw - 200px); /* 限制最大宽度，减去侧边栏宽度 */\n}\n\n/* 当侧边栏收起时的适配 */\n.hideSidebar .review-actions-fixed {\n  left: 54px; /* 收起后的侧边栏宽度 */\n  max-width: calc(100vw - 54px);\n}\n\n/* 移动端适配 */\n@media (max-width: 768px) {\n  .review-actions-fixed {\n    left: 0;\n    right: 0;\n    max-width: 100vw;\n  }\n}\n\n.actions-card {\n  margin: 0;\n  border: none;\n  border-radius: 0;\n  box-shadow: none;\n}\n\n.actions-card .el-card__body {\n  padding: 15px 20px;\n}\n\n.selection-info {\n  color: #606266;\n  font-size: 14px;\n}\n\n.selection-info strong {\n  color: #409eff;\n}\n\n/* 头部信息样式 */\n.header-info {\n  color: #909399;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .main-content .el-col:first-child {\n    margin-bottom: 20px;\n  }\n\n  .info-row .el-col {\n    margin-bottom: 5px;\n  }\n\n  /* 调整小屏幕下的高度计算 */\n  .main-content {\n    height: calc(100vh - 280px);\n  }\n}\n\n@media (max-width: 768px) {\n  .main-content {\n    height: calc(100vh - 320px);\n  }\n\n  .review-actions-fixed .el-row .el-col {\n    margin-bottom: 10px;\n  }\n\n  .actions-card .el-card__body {\n    padding: 10px 15px;\n  }\n}\n\n/* 超大屏幕优化 */\n@media (min-width: 1920px) {\n  .main-content {\n    height: calc(100vh - 220px);\n  }\n}\n\n/* 通用样式 */\n.text-center {\n  text-align: center;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-table {\n  margin-bottom: 5px;\n}\n\n/* 表格优化 */\n.el-table .cell {\n  padding: 0 5px;\n}\n\n.el-table--mini td {\n  padding: 4px 0;\n}\n\n/* 徽章样式 */\n.el-badge {\n  margin-left: 5px;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"]}]}