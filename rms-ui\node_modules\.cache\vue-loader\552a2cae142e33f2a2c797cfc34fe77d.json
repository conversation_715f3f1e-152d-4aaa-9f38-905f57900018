{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\views\\rms\\prescription\\review\\index.vue", "mtime": 1752989786714}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGVuZGluZ1ByZXNjcmlwdGlvbnMsCiAgZ2V0UHJlc2NyaXB0aW9uRGV0YWlsLAogIGdldFByb2JsZW1UeXBlcywKICBnZXRSZWZyZXNoVGltZSwKICBhcHByb3ZlUHJlc2NyaXB0aW9ucywKICByZWplY3RQcmVzY3JpcHRpb25zLAogIGdldERlcGFydG1lbnRzCn0gZnJvbSAiQC9hcGkvcm1zL3ByZXNjcmlwdGlvbnJldmlldyIKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUHJlc2NyaXB0aW9uUmV2aWV3IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5Yqg6L2954q25oCBCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpgInkuK3nmoTlpITmlrnlr7nosaHmlbDnu4TvvIjnlKjkuo7nirbmgIHmjIHkuYXljJbvvIkKICAgICAgc2VsZWN0ZWRQcmVzY3JpcHRpb25zOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlpITmlrnliJfooagKICAgICAgcHJlc2NyaXB0aW9uTGlzdDogW10sCiAgICAgIC8vIOW9k+WJjemAieS4reeahOWkhOaWuQogICAgICBjdXJyZW50UHJlc2NyaXB0aW9uOiBudWxsLAogICAgICAvLyDoja/lk4HmmI7nu4bliJfooagKICAgICAgbWVkaWNhdGlvbkxpc3Q6IFtdLAogICAgICAvLyDliIbmnpDnu5PmnpzliJfooagKICAgICAgYW5hbHlzaXNSZXN1bHRzOiBbXSwKICAgICAgLy8g5a6h5qC45Y6G5Y+y5YiX6KGoCiAgICAgIHJldmlld0hpc3Rvcnk6IFtdLAogICAgICAvLyDnp5HlrqTliJfooagKICAgICAgZGVwYXJ0bWVudExpc3Q6IFtdLAogICAgICAvLyDpl67popjnsbvlnovliJfooagKICAgICAgcHJvYmxlbVR5cGVzOiBbXSwKICAgICAgLy8g6YCJ5Lit55qE6Zeu6aKY57G75Z6LCiAgICAgIHNlbGVjdGVkUHJvYmxlbXM6IFtdLAogICAgICAvLyDoh6rliqjliLfmlrDlvIDlhbMKICAgICAgYXV0b1JlZnJlc2g6IGZhbHNlLAogICAgICAvLyDliLfmlrDlrprml7blmagKICAgICAgcmVmcmVzaFRpbWVyOiBudWxsLAogICAgICAvLyDliLfmlrDpl7TpmpTvvIjmr6vnp5LvvIkKICAgICAgcmVmcmVzaEludGVydmFsOiA1MDAwLAogICAgICAvLyDmmK/lkKbnlLHoh6rliqjliLfmlrDop6blj5HnmoTmoIforrAKICAgICAgaXNBdXRvUmVmcmVzaFRyaWdnZXJlZDogZmFsc2UsCiAgICAgIC8vIOmhtemdouS4jeWPr+ingeWJjeaYr+WQpuW8gOWQr+S6huiHquWKqOWIt+aWsAogICAgICB3YXNBdXRvUmVmcmVzaEFjdGl2ZTogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuato+WcqOaBouWkjemAieaLqeeKtuaAge+8iOeUqOS6jumYu+atouaEj+WklueahOmAieaLqeWPmOWMluS6i+S7tu+8iQogICAgICBpc1Jlc3RvcmluZ1NlbGVjdGlvbjogZmFsc2UsCiAgICAgIC8vIOW9k+WJjea0u+i3g+eahOagh+etvumhtQogICAgICBhY3RpdmVUYWI6ICdpbXBvcnRhbnQnLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBkZXB0Q29kZTogbnVsbCwKICAgICAgICBwcmVzY3JpcHRpb25UeXBlOiAnJywKICAgICAgICBrZXl3b3JkOiBudWxsCiAgICAgIH0KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAvKiog6K6h566X6KGo5qC86auY5bqmICovCiAgICB0YWJsZUhlaWdodCgpIHsKICAgICAgLy8gMTAwdmggLSDpobbpg6jlr7zoiKooNTBweCkgLSDpnaLljIXlsZEoNDBweCkgLSDmkJzntKLljLrln58oODBweCkgLSDljaHniYflpLTpg6goNjBweCkgLSDliIbpobXljLrln58oNTBweCkgLSDlupXpg6jlrqHmoLjljLrln58oODBweCkgLSDovrnot50oNDBweCkKICAgICAgcmV0dXJuICdjYWxjKDEwMHZoIC0gMzUwcHgpJwogICAgfSwKICAgIC8qKiDorqHnrpfor6bmg4XljLrln5/pq5jluqYgKi8KICAgIGRldGFpbEhlaWdodCgpIHsKICAgICAgLy8g5LiO6KGo5qC86auY5bqm5L+d5oyB5LiA6Ie077yM5YeP5Y675Y2h54mH5aS06YOoCiAgICAgIHJldHVybiAnY2FsYygxMDB2aCAtIDM0MHB4KScKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogICAgdGhpcy5nZXREZXBhcnRtZW50TGlzdCgpCiAgICB0aGlzLmdldFByb2JsZW1UeXBlTGlzdCgpCiAgICB0aGlzLmdldFJlZnJlc2hDb25maWcoKQogICAgdGhpcy5sb2FkU2VsZWN0ZWRTdGF0ZSgpCiAgfSwKICBtb3VudGVkKCkgewogICAgLy8g55uR5ZCs56qX5Y+j5aSn5bCP5Y+Y5YyWCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5oYW5kbGVSZXNpemUpCiAgICAvLyDnm5HlkKzkvqfovrnmoI/nirbmgIHlj5jljJYKICAgIHRoaXMuY2hlY2tTaWRlYmFyU3RhdHVzKCkKICAgIC8vIOebkeWQrOmhtemdouWPr+ingeaAp+WPmOWMlgogICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigndmlzaWJpbGl0eWNoYW5nZScsIHRoaXMuaGFuZGxlVmlzaWJpbGl0eUNoYW5nZSkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LlpITmlrnliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFvmlbDmja7liqDovb1dIOW8gOWni+WKoOi9veWkhOaWueWIl+ihqCcsIHsKICAgICAgICDmmK/lkKboh6rliqjliLfmlrA6IHRoaXMuYXV0b1JlZnJlc2gsCiAgICAgICAg5b2T5YmN6aG156CBOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0sCiAgICAgICAg6aG16Z2i5aSn5bCPOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VTaXplLAogICAgICAgIOWKoOi9veaXtumXtDogbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKSwKICAgICAgICDlvZPliY3pgInmi6nnirbmgIE6IHRoaXMuaWRzCiAgICAgIH0pCgogICAgICAvLyDlnKjmlbDmja7liqDovb3liY3kv53lrZjlvZPliY3nmoTpgInmi6nnirbmgIEKICAgICAgY29uc3Qgc2F2ZWRJZHMgPSBbLi4udGhpcy5pZHNdCiAgICAgIGNvbnNvbGUubG9nKCfwn5K+IFvnirbmgIHlpIfku71dIOWkh+S7veW9k+WJjemAieaLqeeKtuaAgScsIHsg5aSH5Lu955qEaWRzOiBzYXZlZElkcyB9KQoKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICAvLyDorr7nva7mgaLlpI3nirbmgIHmoIforrDvvIzpmLLmraLooajmoLzph43mlrDmuLLmn5Pml7bmuIXnqbrpgInmi6kKICAgICAgdGhpcy5pc1Jlc3RvcmluZ1NlbGVjdGlvbiA9IHRydWUKCiAgICAgIC8vIOaehOW7uuafpeivouWPguaVsAogICAgICBsZXQgcGFyYW1zID0geyAuLi50aGlzLnF1ZXJ5UGFyYW1zIH0KCiAgICAgIC8vIOWkhOeQhuWFs+mUruWtl+aQnOe0ogogICAgICBpZiAocGFyYW1zLmtleXdvcmQpIHsKICAgICAgICBwYXJhbXMuZG9jdE5hbWUgPSBwYXJhbXMua2V5d29yZAogICAgICAgIHBhcmFtcy5uYW1lID0gcGFyYW1zLmtleXdvcmQKICAgICAgfQoKICAgICAgZ2V0UGVuZGluZ1ByZXNjcmlwdGlvbnMocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBjb25zb2xlLmxvZygn8J+TiiBb5pWw5o2u5Yqg6L29XSDlpITmlrnliJfooajliqDovb3lrozmiJAnLCB7CiAgICAgICAgICDmlbDmja7mnaHmlbA6IHJlc3BvbnNlLnJvd3MubGVuZ3RoLAogICAgICAgICAg5oC75pWwOiByZXNwb25zZS50b3RhbCwKICAgICAgICAgIOWkhOaWuee8lueggeWIl+ihqDogcmVzcG9uc2Uucm93cy5tYXAoaXRlbSA9PiBpdGVtLmNvZGUpCiAgICAgICAgfSkKCiAgICAgICAgdGhpcy5wcmVzY3JpcHRpb25MaXN0ID0gcmVzcG9uc2Uucm93cwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbAogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCgogICAgICAgIC8vIOaBouWkjeWkh+S7veeahOmAieaLqeeKtuaAgQogICAgICAgIHRoaXMuaWRzID0gc2F2ZWRJZHMKCiAgICAgICAgLy8g6aqM6K+B5bm25riF55CG5peg5pWI55qE6YCJ5oup54q25oCBCiAgICAgICAgdGhpcy52YWxpZGF0ZUFuZENsZWFuU2VsZWN0ZWRTdGF0ZSgpCgogICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFvnirbmgIHmgaLlpI1dIOWHhuWkh+aBouWkjemAieaLqeeKtuaAgScsIHsKICAgICAgICAgIOWkh+S7veeahGlkczogc2F2ZWRJZHMsCiAgICAgICAgICDlvZPliY1pZHM6IHRoaXMuaWRzLAogICAgICAgICAgaWRz5pWw6YePOiB0aGlzLmlkcy5sZW5ndGgsCiAgICAgICAgICDooajmoLxyZWblrZjlnKg6ICEhdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZSwKICAgICAgICAgIOaYr+WQpuiHquWKqOWIt+aWsOinpuWPkTogdGhpcy5pc0F1dG9SZWZyZXNoVHJpZ2dlcmVkCiAgICAgICAgfSkKCiAgICAgICAgLy8g5qC55o2u5piv5ZCm6Ieq5Yqo5Yi35paw5L2/55So5LiN5ZCM55qE5oGi5aSN562W55WlCiAgICAgICAgaWYgKHRoaXMuaXNBdXRvUmVmcmVzaFRyaWdnZXJlZCkgewogICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgW+iHquWKqOWIt+aWsF0g5L2/55So6Ieq5Yqo5Yi35paw5LiT55So5oGi5aSN5py65Yi2JykKICAgICAgICAgIHRoaXMuaGFuZGxlQXV0b1JlZnJlc2hSZXN0b3JlKCkKICAgICAgICAgIHRoaXMuaXNBdXRvUmVmcmVzaFRyaWdnZXJlZCA9IGZhbHNlIC8vIOmHjee9ruagh+iusAogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmxvZygn8J+RhiBb5omL5Yqo5pON5L2cXSDkvb/nlKjluLjop4TmgaLlpI3mnLrliLYnKQogICAgICAgICAgLy8g5oGi5aSN6YCJ5oup54q25oCBIC0g56Gu5L+dRE9N5a6M5YWo5pu05paw5ZCO5YaN5oGi5aSNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfij7AgW+W7tui/n+aBouWkjV0gJG5leHRUaWNr5omn6KGM77yM5YeG5aSH5bu26L+f5oGi5aSN54q25oCBJykKICAgICAgICAgICAgLy8g5bu26L+f5LiA5bCP5q615pe26Ze056Gu5L+d6KGo5qC85a6M5YWo5riy5p+TCiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn46vIFvlvIDlp4vmgaLlpI1dIOW8gOWni+aJp+ihjOeKtuaAgeaBouWkjScpCiAgICAgICAgICAgICAgdGhpcy5yZXN0b3JlU2VsZWN0ZWRTdGF0ZSgpCiAgICAgICAgICAgIH0sIDIwMCkgLy8g5aKe5Yqg5bu26L+f5pe26Ze05YiwMjAwbXMKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgICB0aGlzLmlzUmVzdG9yaW5nU2VsZWN0aW9uID0gZmFsc2UgLy8g5Ye66ZSZ5pe25Lmf6KaB6YeN572u5qCH6K6wCiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFvliqDovb3lpLHotKVdIOiOt+WPluWkhOaWueWIl+ihqOWksei0pTonLCBlcnJvcikKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCgogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFvpgInmi6nlj5jljJZdIOinpuWPkemAieaLqeWPmOWMluS6i+S7ticsIHsKICAgICAgICDpgInkuK3mlbDph486IHNlbGVjdGlvbi5sZW5ndGgsCiAgICAgICAg6YCJ5Lit5aSE5pa5OiBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gKHsgY29kZTogaXRlbS5jb2RlLCBuYW1lOiBpdGVtLm5hbWUgfSkpLAogICAgICAgIOinpuWPkeaXtumXtDogbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKSwKICAgICAgICDmraPlnKjmgaLlpI3nirbmgIE6IHRoaXMuaXNSZXN0b3JpbmdTZWxlY3Rpb24KICAgICAgfSkKCiAgICAgIC8vIOWmguaenOato+WcqOaBouWkjemAieaLqeeKtuaAge+8jOW/veeVpei/measoemAieaLqeWPmOWMluS6i+S7tgogICAgICBpZiAodGhpcy5pc1Jlc3RvcmluZ1NlbGVjdGlvbikgewogICAgICAgIGNvbnNvbGUubG9nKCfwn5qrIFvlv73nlaXkuovku7ZdIOato+WcqOaBouWkjemAieaLqeeKtuaAge+8jOW/veeVpeatpOasoemAieaLqeWPmOWMluS6i+S7ticpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uY29kZSkKICAgICAgdGhpcy5zZWxlY3RlZFByZXNjcmlwdGlvbnMgPSBzZWxlY3Rpb24KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgICBjb25zb2xlLmxvZygnbXVsdGlwbGUnLCB0aGlzLm11bHRpcGxlKQogICAgICBjb25zb2xlLmxvZygnc2VsZWN0aW9uLmxlbmd0aCcsIHNlbGVjdGlvbi5sZW5ndGgpCgogICAgICBjb25zb2xlLmxvZygn8J+SviBb54q25oCB5pu05pawXSDmm7TmlrDnu4Tku7bnirbmgIEnLCB7CiAgICAgICAgaWRzOiB0aGlzLmlkcywKICAgICAgICBzaW5nbGU6IHRoaXMuc2luZ2xlLAogICAgICAgIG11bHRpcGxlOiB0aGlzLm11bHRpcGxlCiAgICAgIH0pCgogICAgICAvLyDkv53lrZjpgInmi6nnirbmgIHliLDmnKzlnLDlrZjlgqgKICAgICAgdGhpcy5zYXZlU2VsZWN0ZWRTdGF0ZSgpCiAgICB9LAoKICAgIC8qKiDooYzngrnlh7vkuovku7YgKi8KICAgIGhhbmRsZVJvd0NsaWNrKHJvdykgewogICAgICB0aGlzLmN1cnJlbnRQcmVzY3JpcHRpb24gPSByb3cKICAgICAgdGhpcy5nZXRQcmVzY3JpcHRpb25EZXRhaWxEYXRhKHJvdy5jb2RlKQogICAgfSwKCiAgICAvKiog6I635Y+W5aSE5pa56K+m5oOFICovCiAgICBnZXRQcmVzY3JpcHRpb25EZXRhaWxEYXRhKGNvZGUpIHsKICAgICAgZ2V0UHJlc2NyaXB0aW9uRGV0YWlsKGNvZGUpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuY3VycmVudFByZXNjcmlwdGlvbiA9IHJlc3BvbnNlLmRhdGEucHJlc2NyaXB0aW9uCiAgICAgICAgdGhpcy5tZWRpY2F0aW9uTGlzdCA9IHJlc3BvbnNlLmRhdGEubWVkaWNhdGlvbnMgfHwgW10KICAgICAgICB0aGlzLmFuYWx5c2lzUmVzdWx0cyA9IHJlc3BvbnNlLmRhdGEuYW5hbHlzaXNSZXN1bHRzIHx8IFtdCiAgICAgICAgdGhpcy5yZXZpZXdIaXN0b3J5ID0gcmVzcG9uc2UuZGF0YS5yZXZpZXdIaXN0b3J5IHx8IFtdCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDojrflj5bnp5HlrqTliJfooaggKi8KICAgIGdldERlcGFydG1lbnRMaXN0KCkgewogICAgICBnZXREZXBhcnRtZW50cygpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZGVwYXJ0bWVudExpc3QgPSByZXNwb25zZS5kYXRhIHx8IFtdCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDojrflj5bpl67popjnsbvlnovliJfooaggKi8KICAgIGdldFByb2JsZW1UeXBlTGlzdCgpIHsKICAgICAgZ2V0UHJvYmxlbVR5cGVzKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wcm9ibGVtVHlwZXMgPSByZXNwb25zZS5kYXRhIHx8IFtdCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDojrflj5bliLfmlrDphY3nva4gKi8KICAgIGdldFJlZnJlc2hDb25maWcoKSB7CiAgICAgIGdldFJlZnJlc2hUaW1lKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5yZWZyZXNoSW50ZXJ2YWwgPSByZXNwb25zZS5kYXRhIHx8IDUwMDAKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOWIh+aNouiHquWKqOWIt+aWsCAqLwogICAgdG9nZ2xlQXV0b1JlZnJlc2godmFsdWUpIHsKICAgICAgY29uc29sZS5sb2coJ/CflIQgW+iHquWKqOWIt+aWsF0g5YiH5o2i6Ieq5Yqo5Yi35paw54q25oCBJywgewogICAgICAgIOW8gOWQrzogdmFsdWUsCiAgICAgICAg5Yi35paw6Ze06ZqUOiB0aGlzLnJlZnJlc2hJbnRlcnZhbCwKICAgICAgICDlvZPliY3pgInmi6nmlbDph486IHRoaXMuaWRzLmxlbmd0aAogICAgICB9KQoKICAgICAgaWYgKHZhbHVlKSB7CiAgICAgICAgdGhpcy5yZWZyZXNoVGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZygn4o+wIFvoh6rliqjliLfmlrBdIOiHquWKqOWIt+aWsOWumuaXtuWZqOinpuWPkScsIHsKICAgICAgICAgICAg6Kem5Y+R5pe26Ze0OiBuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygpLAogICAgICAgICAgICDlvZPliY3pgInmi6nnirbmgIE6IHRoaXMuaWRzCiAgICAgICAgICB9KQoKICAgICAgICAgIC8vIOagh+iusOi/meaYr+iHquWKqOWIt+aWsOinpuWPkeeahOaVsOaNruWKoOi9vQogICAgICAgICAgdGhpcy5pc0F1dG9SZWZyZXNoVHJpZ2dlcmVkID0gdHJ1ZQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB9LCB0aGlzLnJlZnJlc2hJbnRlcnZhbCkKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAodGhpcy5yZWZyZXNoVGltZXIpIHsKICAgICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5yZWZyZXNoVGltZXIpCiAgICAgICAgICB0aGlzLnJlZnJlc2hUaW1lciA9IG51bGwKICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOWuoeaguOmAmui/hyAqLwogICAgaGFuZGxlQXBwcm92ZSgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHlrqHmoLjnmoTlpITmlrkiKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTlrqHmoLjpgJrov4fpgInkuK3nmoTlpITmlrnvvJ8nKS50aGVuKCgpID0+IHsKICAgICAgICByZXR1cm4gYXBwcm92ZVByZXNjcmlwdGlvbnModGhpcy5pZHMpCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgdGhpcy5jbGVhclNlbGVjdGlvbigpCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5a6h5qC46YCa6L+H5oiQ5YqfIikKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pCiAgICB9LAoKICAgIC8qKiDlrqHmoLjmiZPlm54gKi8KICAgIGhhbmRsZVJlamVjdCgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHmiZPlm57nmoTlpITmlrkiKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICBpZiAodGhpcy5zZWxlY3RlZFByb2JsZW1zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6npl67popjnsbvlnosiKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmiZPlm57pgInkuK3nmoTlpITmlrnvvJ8nKS50aGVuKCgpID0+IHsKICAgICAgICBjb25zdCBkYXRhID0gewogICAgICAgICAgY29kZXM6IHRoaXMuaWRzLAogICAgICAgICAgcHJvYmxlbU5hbWVzOiB0aGlzLnNlbGVjdGVkUHJvYmxlbXMKICAgICAgICB9CiAgICAgICAgcmV0dXJuIHJlamVjdFByZXNjcmlwdGlvbnMoZGF0YSkKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB0aGlzLnNlbGVjdGVkUHJvYmxlbXMgPSBbXQogICAgICAgIHRoaXMuY2xlYXJTZWxlY3Rpb24oKQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWuoeaguOaJk+WbnuaIkOWKnyIpCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgfSwKCiAgICAvKiog5qC55o2u6Zeu6aKY562J57qn6I635Y+W5YiG5p6Q57uT5p6cICovCiAgICBnZXRBbmFseXNpc1Jlc3VsdHNCeUxldmVsKGxldmVsKSB7CiAgICAgIHJldHVybiB0aGlzLmFuYWx5c2lzUmVzdWx0cy5maWx0ZXIoaXRlbSA9PiBpdGVtLnd0bHZsID09PSBsZXZlbCkKICAgIH0sCgogICAgLyoqIOiOt+WPluS4pemHjeeoi+W6puexu+WeiyAqLwogICAgZ2V0U2V2ZXJpdHlUeXBlKGxldmVsKSB7CiAgICAgIHN3aXRjaChsZXZlbCkgewogICAgICAgIGNhc2UgJ+mHjeimgSc6IHJldHVybiAnZGFuZ2VyJwogICAgICAgIGNhc2UgJ+S4gOiIrCc6IHJldHVybiAnd2FybmluZycKICAgICAgICBjYXNlICflhbblroMnOiByZXR1cm4gJ2luZm8nCiAgICAgICAgZGVmYXVsdDogcmV0dXJuICdpbmZvJwogICAgICB9CiAgICB9LAoKICAgIC8qKiDojrflj5bkuKXph43nqIvluqbmlofmnKwgKi8KICAgIGdldFNldmVyaXR5VGV4dChsZXZlbCkgewogICAgICByZXR1cm4gbGV2ZWwgfHwgJ+acquefpScKICAgIH0sCgogICAgLyoqIOiuoeeul+W5tOm+hCAqLwogICAgY2FsY3VsYXRlQWdlKGJpcnRoRGF0ZSkgewogICAgICBpZiAoIWJpcnRoRGF0ZSkgcmV0dXJuICfmnKrnn6UnCiAgICAgIGNvbnN0IGJpcnRoID0gbmV3IERhdGUoYmlydGhEYXRlKQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpCiAgICAgIGNvbnN0IGFnZSA9IG5vdy5nZXRGdWxsWWVhcigpIC0gYmlydGguZ2V0RnVsbFllYXIoKQogICAgICByZXR1cm4gYWdlICsgJ+WygScKICAgIH0sCgogICAgLyoqIOS/neWtmOmAieaLqeeKtuaAgeWIsOacrOWcsOWtmOWCqCAqLwogICAgc2F2ZVNlbGVjdGVkU3RhdGUoKSB7CiAgICAgIGNvbnN0IHNlbGVjdGVkQ29kZXMgPSB0aGlzLmlkcwogICAgICBjb25zb2xlLmxvZygn8J+SviBb5L+d5a2Y54q25oCBXSDkv53lrZjpgInmi6nnirbmgIHliLBsb2NhbFN0b3JhZ2UnLCB7CiAgICAgICAg6YCJ5Lit55qE5aSE5pa557yW56CBOiBzZWxlY3RlZENvZGVzLAogICAgICAgIOaVsOmHjzogc2VsZWN0ZWRDb2Rlcy5sZW5ndGgsCiAgICAgICAg5L+d5a2Y5pe26Ze0OiBuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygpCiAgICAgIH0pCgogICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncHJlc2NyaXB0aW9uX3Jldmlld19zZWxlY3RlZCcsIEpTT04uc3RyaW5naWZ5KHNlbGVjdGVkQ29kZXMpKQoKICAgICAgLy8g6aqM6K+B5L+d5a2Y5piv5ZCm5oiQ5YqfCiAgICAgIGNvbnN0IHNhdmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3ByZXNjcmlwdGlvbl9yZXZpZXdfc2VsZWN0ZWQnKQogICAgICBjb25zb2xlLmxvZygn4pyFIFvkv53lrZjpqozor4FdIGxvY2FsU3RvcmFnZeS/neWtmOe7k+aenCcsIHsKICAgICAgICDkv53lrZjnmoTmlbDmja46IHNhdmVkLAogICAgICAgIOino+aekOWQjjogSlNPTi5wYXJzZShzYXZlZCB8fCAnW10nKQogICAgICB9KQogICAgfSwKCiAgICAvKiog5LuO5pys5Zyw5a2Y5YKo5Yqg6L296YCJ5oup54q25oCBICovCiAgICBsb2FkU2VsZWN0ZWRTdGF0ZSgpIHsKICAgICAgY29uc29sZS5sb2coJ/Cfk5YgW+WKoOi9veeKtuaAgV0g5byA5aeL5LuObG9jYWxTdG9yYWdl5Yqg6L296YCJ5oup54q25oCBJykKCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3Qgc2F2ZWRTZWxlY3Rpb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncHJlc2NyaXB0aW9uX3Jldmlld19zZWxlY3RlZCcpCiAgICAgICAgY29uc29sZS5sb2coJ/Cfk5YgW+WKoOi9veeKtuaAgV0gbG9jYWxTdG9yYWdl5Lit55qE5pWw5o2uJywgewogICAgICAgICAg5Y6f5aeL5pWw5o2uOiBzYXZlZFNlbGVjdGlvbiwKICAgICAgICAgIOaVsOaNruexu+WeizogdHlwZW9mIHNhdmVkU2VsZWN0aW9uCiAgICAgICAgfSkKCiAgICAgICAgaWYgKHNhdmVkU2VsZWN0aW9uKSB7CiAgICAgICAgICBjb25zdCBwYXJzZWRJZHMgPSBKU09OLnBhcnNlKHNhdmVkU2VsZWN0aW9uKQogICAgICAgICAgdGhpcy5pZHMgPSBwYXJzZWRJZHMKICAgICAgICAgIHRoaXMubXVsdGlwbGUgPSB0aGlzLmlkcy5sZW5ndGggPT09IDAKICAgICAgICAgIHRoaXMuc2luZ2xlID0gdGhpcy5pZHMubGVuZ3RoICE9PSAxCgogICAgICAgICAgY29uc29sZS5sb2coJ+KchSBb5Yqg6L295oiQ5YqfXSDnirbmgIHliqDovb3lrozmiJAnLCB7CiAgICAgICAgICAgIOWKoOi9veeahGlkczogdGhpcy5pZHMsCiAgICAgICAgICAgIOaVsOmHjzogdGhpcy5pZHMubGVuZ3RoLAogICAgICAgICAgICBtdWx0aXBsZTogdGhpcy5tdWx0aXBsZSwKICAgICAgICAgICAgc2luZ2xlOiB0aGlzLnNpbmdsZQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5sb2coJ+KEue+4jyBb5Yqg6L2954q25oCBXSBsb2NhbFN0b3JhZ2XkuK3msqHmnInkv53lrZjnmoTpgInmi6nnirbmgIEnKQogICAgICAgICAgdGhpcy5pZHMgPSBbXQogICAgICAgICAgdGhpcy5tdWx0aXBsZSA9IHRydWUKICAgICAgICAgIHRoaXMuc2luZ2xlID0gdHJ1ZQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgW+WKoOi9veWksei0pV0g5Yqg6L296YCJ5oup54q25oCB5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuaWRzID0gW10KICAgICAgICB0aGlzLm11bHRpcGxlID0gdHJ1ZQogICAgICAgIHRoaXMuc2luZ2xlID0gdHJ1ZQogICAgICB9CiAgICB9LAoKICAgIC8qKiDpqozor4HlubbmuIXnkIbml6DmlYjnmoTpgInmi6nnirbmgIEgKi8KICAgIHZhbGlkYXRlQW5kQ2xlYW5TZWxlY3RlZFN0YXRlKCkgewogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+KEue+4jyBb54q25oCB6aqM6K+BXSDmsqHmnInpgInmi6nnirbmgIHpnIDopoHpqozor4EnKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDmo4Dmn6Vsb2NhbFN0b3JhZ2XkuK3nmoRJROaYr+WQpuWcqOW9k+WJjeaVsOaNruWIl+ihqOS4reWtmOWcqAogICAgICBjb25zdCBjdXJyZW50Q29kZXMgPSB0aGlzLnByZXNjcmlwdGlvbkxpc3QubWFwKGl0ZW0gPT4gaXRlbS5jb2RlKQogICAgICBjb25zdCB2YWxpZElkcyA9IHRoaXMuaWRzLmZpbHRlcihpZCA9PiBjdXJyZW50Q29kZXMuaW5jbHVkZXMoaWQpKQoKICAgICAgY29uc29sZS5sb2coJ/CflI0gW+eKtuaAgemqjOivgV0g6aqM6K+B6YCJ5oup54q25oCB5pyJ5pWI5oCnJywgewogICAgICAgIOWOn+Wni2lkczogdGhpcy5pZHMsCiAgICAgICAg5b2T5YmN5pWw5o2u57yW56CBOiBjdXJyZW50Q29kZXMsCiAgICAgICAg5pyJ5pWI55qEaWRzOiB2YWxpZElkcywKICAgICAgICDml6DmlYjnmoRpZHM6IHRoaXMuaWRzLmZpbHRlcihpZCA9PiAhY3VycmVudENvZGVzLmluY2x1ZGVzKGlkKSkKICAgICAgfSkKCiAgICAgIGlmICh2YWxpZElkcy5sZW5ndGggIT09IHRoaXMuaWRzLmxlbmd0aCkgewogICAgICAgIC8vIOacieaXoOaViOeahElE77yM6ZyA6KaB5riF55CGCiAgICAgICAgY29uc29sZS5sb2coJ/Cfp7kgW+eKtuaAgea4heeQhl0g5Y+R546w5peg5pWI55qE6YCJ5oup54q25oCB77yM6L+b6KGM5riF55CGJykKICAgICAgICB0aGlzLmlkcyA9IHZhbGlkSWRzCiAgICAgICAgdGhpcy5tdWx0aXBsZSA9IHRoaXMuaWRzLmxlbmd0aCA9PT0gMAogICAgICAgIHRoaXMuc2luZ2xlID0gdGhpcy5pZHMubGVuZ3RoICE9PSAxCgogICAgICAgIC8vIOabtOaWsGxvY2FsU3RvcmFnZQogICAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdwcmVzY3JpcHRpb25fcmV2aWV3X3NlbGVjdGVkJykKICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5eR77iPIFvnirbmgIHmuIXnkIZdIOa4heepumxvY2FsU3RvcmFnZeS4reeahOmAieaLqeeKtuaAgScpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdwcmVzY3JpcHRpb25fcmV2aWV3X3NlbGVjdGVkJywgSlNPTi5zdHJpbmdpZnkodGhpcy5pZHMpKQogICAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW+eKtuaAgea4heeQhl0g5pu05pawbG9jYWxTdG9yYWdl5Lit55qE6YCJ5oup54q25oCBJywgeyDmm7TmlrDlkI7nmoRpZHM6IHRoaXMuaWRzIH0pCiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8qKiDmgaLlpI3ooajmoLzpgInmi6nnirbmgIEgKi8KICAgIHJlc3RvcmVTZWxlY3RlZFN0YXRlKCkgewogICAgICBjb25zb2xlLmxvZygn8J+OryBb54q25oCB5oGi5aSNXSDlvIDlp4vmgaLlpI3ooajmoLzpgInmi6nnirbmgIEnLCB7CiAgICAgICAg6ZyA6KaB5oGi5aSN55qEaWRzOiB0aGlzLmlkcywKICAgICAgICBpZHPmlbDph486IHRoaXMuaWRzLmxlbmd0aCwKICAgICAgICDooajmoLxyZWblrZjlnKg6ICEhdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZSwKICAgICAgICDlpITmlrnliJfooajplb/luqY6IHRoaXMucHJlc2NyaXB0aW9uTGlzdC5sZW5ndGgsCiAgICAgICAg5aSE5pa55YiX6KGo57yW56CBOiB0aGlzLnByZXNjcmlwdGlvbkxpc3QubWFwKGl0ZW0gPT4gaXRlbS5jb2RlKQogICAgICB9KQoKICAgICAgLy8g5qOA5p+l5YmN572u5p2h5Lu2CiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICBjb25zb2xlLmxvZygn4oS577iPIFvnirbmgIHmgaLlpI1dIOayoeaciemcgOimgeaBouWkjeeahOmAieaLqeeKtuaAgScpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGlmICghdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZSkgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBb54q25oCB5oGi5aSNXSDooajmoLxyZWbkuI3lrZjlnKgnKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICBpZiAodGhpcy5wcmVzY3JpcHRpb25MaXN0Lmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNvbnNvbGUubG9nKCfihLnvuI8gW+eKtuaAgeaBouWkjV0g5aSE5pa55YiX6KGo5Li656m677yM5peg5rOV5oGi5aSN54q25oCBJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDmuIXpmaTlvZPliY3pgInmi6kKICAgICAgICBjb25zb2xlLmxvZygn8J+nuSBb5riF6Zmk6YCJ5oupXSDmuIXpmaTooajmoLzlvZPliY3pgInmi6nnirbmgIEnKQogICAgICAgIHRoaXMuJHJlZnMucHJlc2NyaXB0aW9uVGFibGUuY2xlYXJTZWxlY3Rpb24oKQoKICAgICAgICAvLyDmgaLlpI3pgInmi6nnirbmgIEKICAgICAgICBsZXQgcmVzdG9yZWRDb3VudCA9IDAKICAgICAgICBsZXQgbWF0Y2hlZFJvd3MgPSBbXQogICAgICAgIGxldCB1bm1hdGNoZWRJZHMgPSBbXQoKICAgICAgICB0aGlzLnByZXNjcmlwdGlvbkxpc3QuZm9yRWFjaChyb3cgPT4gewogICAgICAgICAgaWYgKHRoaXMuaWRzLmluY2x1ZGVzKHJvdy5jb2RlKSkgewogICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFvljLnphY3miJDlip9dIOaJvuWIsOWMuemFjeeahOWkhOaWuTogJHtyb3cuY29kZX0gLSAke3Jvdy5uYW1lfWApCiAgICAgICAgICAgIHRoaXMuJHJlZnMucHJlc2NyaXB0aW9uVGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdywgdHJ1ZSkKICAgICAgICAgICAgcmVzdG9yZWRDb3VudCsrCiAgICAgICAgICAgIG1hdGNoZWRSb3dzLnB1c2goeyBjb2RlOiByb3cuY29kZSwgbmFtZTogcm93Lm5hbWUgfSkKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgICAvLyDmo4Dmn6XmnKrljLnphY3nmoRJRAogICAgICAgIHRoaXMuaWRzLmZvckVhY2goaWQgPT4gewogICAgICAgICAgaWYgKCF0aGlzLnByZXNjcmlwdGlvbkxpc3QuZmluZChyb3cgPT4gcm93LmNvZGUgPT09IGlkKSkgewogICAgICAgICAgICB1bm1hdGNoZWRJZHMucHVzaChpZCkKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgICAvLyDmm7TmlrDpgInmi6nnirbmgIHnu5/orqEKICAgICAgICB0aGlzLm11bHRpcGxlID0gdGhpcy5pZHMubGVuZ3RoID09PSAwCiAgICAgICAgdGhpcy5zaW5nbGUgPSB0aGlzLmlkcy5sZW5ndGggIT09IDEKCiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ogW+aBouWkjee7k+aenF0g54q25oCB5oGi5aSN5a6M5oiQJywgewogICAgICAgICAg5oGi5aSN5oiQ5Yqf5pWw6YePOiByZXN0b3JlZENvdW50LAogICAgICAgICAg5Yy56YWN55qE5aSE5pa5OiBtYXRjaGVkUm93cywKICAgICAgICAgIOacquWMuemFjeeahElEOiB1bm1hdGNoZWRJZHMsCiAgICAgICAgICDmnIDnu4jnirbmgIE6IHsKICAgICAgICAgICAgbXVsdGlwbGU6IHRoaXMubXVsdGlwbGUsCiAgICAgICAgICAgIHNpbmdsZTogdGhpcy5zaW5nbGUsCiAgICAgICAgICAgIGlkczogdGhpcy5pZHMKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgICAvLyDpqozor4HmgaLlpI3nu5PmnpwKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICBjb25zdCBzZWxlY3RlZFJvd3MgPSB0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlLnNlbGVjdGlvbiB8fCBbXQogICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW+aBouWkjemqjOivgV0g6aqM6K+B6KGo5qC86YCJ5oup54q25oCBJywgewogICAgICAgICAgICDooajmoLzpgInkuK3ooYzmlbA6IHNlbGVjdGVkUm93cy5sZW5ndGgsCiAgICAgICAgICAgIOihqOagvOmAieS4ree8lueggTogc2VsZWN0ZWRSb3dzLm1hcChyb3cgPT4gcm93LmNvZGUpLAogICAgICAgICAgICDmnJ/mnJvpgInkuK3mlbDph486IHRoaXMuaWRzLmxlbmd0aCwKICAgICAgICAgICAg54q25oCB5LiA6Ie0OiBzZWxlY3RlZFJvd3MubGVuZ3RoID09PSByZXN0b3JlZENvdW50CiAgICAgICAgICB9KQoKICAgICAgICAgIGlmIChzZWxlY3RlZFJvd3MubGVuZ3RoICE9PSByZXN0b3JlZENvdW50KSB7CiAgICAgICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIFvnirbmgIHkuI3kuIDoh7RdIOihqOagvOmAieaLqeeKtuaAgeS4jumihOacn+S4jeespu+8jOWwneivlemHjeaWsOaBouWkjScpCiAgICAgICAgICAgIC8vIOWmguaenOeKtuaAgeS4jeS4gOiHtO+8jOWGjeasoeWwneivleaBouWkjQogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLnJlc3RvcmVTZWxlY3RlZFN0YXRlUmV0cnkoKQogICAgICAgICAgICB9LCAxMDApCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDmgaLlpI3miJDlip/vvIzph43nva7mgaLlpI3nirbmgIHmoIforrAKICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBb5oGi5aSN5a6M5oiQXSDnirbmgIHmgaLlpI3miJDlip/vvIzph43nva7mgaLlpI3moIforrAnKQogICAgICAgICAgICB0aGlzLmlzUmVzdG9yaW5nU2VsZWN0aW9uID0gZmFsc2UKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgW+aBouWkjeWksei0pV0g5oGi5aSN6YCJ5oup54q25oCB5aSx6LSlOicsIGVycm9yKQogICAgICB9CiAgICB9LAoKICAgIC8qKiDmuIXnqbrpgInmi6kgKi8KICAgIGNsZWFyU2VsZWN0aW9uKCkgewogICAgICAvLyDorr7nva7mgaLlpI3moIforrDvvIzpmLLmraJjbGVhclNlbGVjdGlvbuinpuWPkemAieaLqeWPmOWMluS6i+S7tgogICAgICB0aGlzLmlzUmVzdG9yaW5nU2VsZWN0aW9uID0gdHJ1ZQoKICAgICAgdGhpcy5pZHMgPSBbXQogICAgICB0aGlzLnNlbGVjdGVkUHJlc2NyaXB0aW9ucyA9IFtdCiAgICAgIHRoaXMubXVsdGlwbGUgPSB0cnVlCiAgICAgIHRoaXMuc2luZ2xlID0gdHJ1ZQoKICAgICAgLy8g5riF6Zmk6KGo5qC86YCJ5oupCiAgICAgIGlmICh0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlKSB7CiAgICAgICAgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZS5jbGVhclNlbGVjdGlvbigpCiAgICAgIH0KCiAgICAgIC8vIOa4hemZpOacrOWcsOWtmOWCqAogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncHJlc2NyaXB0aW9uX3Jldmlld19zZWxlY3RlZCcpCgogICAgICBjb25zb2xlLmxvZygn8J+nuSBb5riF56m66YCJ5oupXSDpgInmi6nnirbmgIHlt7LmuIXnqbonLCB7CiAgICAgICAg5riF56m65pe26Ze0OiBuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygpCiAgICAgIH0pCgogICAgICAvLyDph43nva7mgaLlpI3moIforrAKICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5pc1Jlc3RvcmluZ1NlbGVjdGlvbiA9IGZhbHNlCiAgICAgIH0sIDEwMCkKICAgIH0sCgogICAgLyoqIOmHjeivleaBouWkjemAieaLqeeKtuaAgSAqLwogICAgcmVzdG9yZVNlbGVjdGVkU3RhdGVSZXRyeSgpIHsKICAgICAgY29uc29sZS5sb2coJ/CflIQgW+mHjeivleaBouWkjV0g5byA5aeL6YeN6K+V5oGi5aSN6YCJ5oup54q25oCBJykKCiAgICAgIGlmICghdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZSkgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBb6YeN6K+V5aSx6LSlXSDooajmoLxyZWbku43nhLbkuI3lrZjlnKgnKQogICAgICAgIHRoaXMuaXNSZXN0b3JpbmdTZWxlY3Rpb24gPSBmYWxzZQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDlvLrliLbmuIXpmaTpgInmi6kKICAgICAgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZS5jbGVhclNlbGVjdGlvbigpCgogICAgICAvLyDph43mlrDmgaLlpI0KICAgICAgbGV0IHJldHJ5Q291bnQgPSAwCiAgICAgIHRoaXMucHJlc2NyaXB0aW9uTGlzdC5mb3JFYWNoKHJvdyA9PiB7CiAgICAgICAgaWYgKHRoaXMuaWRzLmluY2x1ZGVzKHJvdy5jb2RlKSkgewogICAgICAgICAgdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93LCB0cnVlKQogICAgICAgICAgcmV0cnlDb3VudCsrCiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UhCBb6YeN6K+V5oGi5aSNXSDph43mlrDpgInmi6nlpITmlrk6ICR7cm93LmNvZGV9YCkKICAgICAgICB9CiAgICAgIH0pCgogICAgICBjb25zb2xlLmxvZyhg4pyFIFvph43or5XlrozmiJBdIOmHjeivleaBouWkjeS6hiAke3JldHJ5Q291bnR9IOS4quWkhOaWueeahOmAieaLqeeKtuaAgWApCgogICAgICAvLyDph43or5XlrozmiJDlkI7ph43nva7moIforrAKICAgICAgdGhpcy5pc1Jlc3RvcmluZ1NlbGVjdGlvbiA9IGZhbHNlCiAgICB9LAoKICAgIC8qKiDoh6rliqjliLfmlrDml7bnmoTnibnmrorlpITnkIYgKi8KICAgIGhhbmRsZUF1dG9SZWZyZXNoUmVzdG9yZSgpIHsKICAgICAgY29uc29sZS5sb2coJ/CflIQgW+iHquWKqOWIt+aWsF0g6Ieq5Yqo5Yi35paw6Kem5Y+R77yM54m55q6K5aSE55CG6YCJ5oup54q25oCB5oGi5aSNJykKCiAgICAgIC8vIOWcqOiHquWKqOWIt+aWsOaXtu+8jOe7meabtOWkmuaXtumXtOiuqeihqOagvOWujOWFqOa4suafkwogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZygn8J+OryBb6Ieq5Yqo5Yi35paw5oGi5aSNXSDlvIDlp4voh6rliqjliLfmlrDlkI7nmoTnirbmgIHmgaLlpI0nKQogICAgICAgICAgdGhpcy5yZXN0b3JlU2VsZWN0ZWRTdGF0ZSgpCgogICAgICAgICAgLy8g6aKd5aSW6aqM6K+BCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWRSb3dzID0gdGhpcy4kcmVmcy5wcmVzY3JpcHRpb25UYWJsZT8uc2VsZWN0aW9uIHx8IFtdCiAgICAgICAgICAgIGlmIChzZWxlY3RlZFJvd3MubGVuZ3RoID09PSAwICYmIHRoaXMuaWRzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBb6Ieq5Yqo5Yi35pawXSDnrKzkuIDmrKHmgaLlpI3lpLHotKXvvIzov5vooYznrKzkuozmrKHlsJ3or5UnKQogICAgICAgICAgICAgIHRoaXMucmVzdG9yZVNlbGVjdGVkU3RhdGVSZXRyeSgpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgLy8g6Ieq5Yqo5Yi35paw5oGi5aSN5oiQ5Yqf77yM6YeN572u5qCH6K6wCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBb6Ieq5Yqo5Yi35pawXSDoh6rliqjliLfmlrDmgaLlpI3miJDlip/vvIzph43nva7mgaLlpI3moIforrAnKQogICAgICAgICAgICAgIHRoaXMuaXNSZXN0b3JpbmdTZWxlY3Rpb24gPSBmYWxzZQogICAgICAgICAgICB9CiAgICAgICAgICB9LCAzMDApCiAgICAgICAgfSwgMzAwKSAvLyDoh6rliqjliLfmlrDml7bkvb/nlKjmm7Tplb/nmoTlu7bov58KICAgICAgfSkKICAgIH0sCgogICAgLyoqIOWkhOeQhueql+WPo+Wkp+Wwj+WPmOWMliAqLwogICAgaGFuZGxlUmVzaXplKCkgewogICAgICAvLyDnqpflj6PlpKflsI/lj5jljJbml7bvvIzlvLrliLbph43mlrDorqHnrpfooajmoLzpq5jluqYKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIGlmICh0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlKSB7CiAgICAgICAgICB0aGlzLiRyZWZzLnByZXNjcmlwdGlvblRhYmxlLmRvTGF5b3V0KCkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDmo4Dmn6XkvqfovrnmoI/nirbmgIEgKi8KICAgIGNoZWNrU2lkZWJhclN0YXR1cygpIHsKICAgICAgLy8g5qOA5p+lYm9keeaYr+WQpuaciWhpZGVTaWRlYmFy57G7CiAgICAgIGNvbnN0IGJvZHkgPSBkb2N1bWVudC5ib2R5CiAgICAgIGlmIChib2R5LmNsYXNzTGlzdC5jb250YWlucygnaGlkZVNpZGViYXInKSkgewogICAgICAgIC8vIOS+p+i+ueagj+W3suaUtui1twogICAgICAgIHRoaXMudXBkYXRlRml4ZWRBcmVhUG9zaXRpb24odHJ1ZSkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDkvqfovrnmoI/lsZXlvIAKICAgICAgICB0aGlzLnVwZGF0ZUZpeGVkQXJlYVBvc2l0aW9uKGZhbHNlKQogICAgICB9CgogICAgICAvLyDnm5HlkKzkvqfovrnmoI/nirbmgIHlj5jljJYKICAgICAgY29uc3Qgb2JzZXJ2ZXIgPSBuZXcgTXV0YXRpb25PYnNlcnZlcigobXV0YXRpb25zKSA9PiB7CiAgICAgICAgbXV0YXRpb25zLmZvckVhY2goKG11dGF0aW9uKSA9PiB7CiAgICAgICAgICBpZiAobXV0YXRpb24udHlwZSA9PT0gJ2F0dHJpYnV0ZXMnICYmIG11dGF0aW9uLmF0dHJpYnV0ZU5hbWUgPT09ICdjbGFzcycpIHsKICAgICAgICAgICAgY29uc3QgaXNIaWRkZW4gPSBib2R5LmNsYXNzTGlzdC5jb250YWlucygnaGlkZVNpZGViYXInKQogICAgICAgICAgICB0aGlzLnVwZGF0ZUZpeGVkQXJlYVBvc2l0aW9uKGlzSGlkZGVuKQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0pCgogICAgICBvYnNlcnZlci5vYnNlcnZlKGJvZHksIHsKICAgICAgICBhdHRyaWJ1dGVzOiB0cnVlLAogICAgICAgIGF0dHJpYnV0ZUZpbHRlcjogWydjbGFzcyddCiAgICAgIH0pCgogICAgICAvLyDkv53lrZhvYnNlcnZlcuW8leeUqOS7peS+v+a4heeQhgogICAgICB0aGlzLnNpZGViYXJPYnNlcnZlciA9IG9ic2VydmVyCiAgICB9LAoKICAgIC8qKiDmm7TmlrDlm7rlrprljLrln5/kvY3nva4gKi8KICAgIHVwZGF0ZUZpeGVkQXJlYVBvc2l0aW9uKGlzSGlkZGVuKSB7CiAgICAgIGNvbnN0IGZpeGVkQXJlYSA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5yZXZpZXctYWN0aW9ucy1maXhlZCcpCiAgICAgIGlmIChmaXhlZEFyZWEpIHsKICAgICAgICBpZiAoaXNIaWRkZW4pIHsKICAgICAgICAgIGZpeGVkQXJlYS5zdHlsZS5sZWZ0ID0gJzU0cHgnCiAgICAgICAgICBmaXhlZEFyZWEuc3R5bGUubWF4V2lkdGggPSAnY2FsYygxMDB2dyAtIDU0cHgpJwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBmaXhlZEFyZWEuc3R5bGUubGVmdCA9ICcyMDBweCcKICAgICAgICAgIGZpeGVkQXJlYS5zdHlsZS5tYXhXaWR0aCA9ICdjYWxjKDEwMHZ3IC0gMjAwcHgpJwogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvKiog5aSE55CG6aG16Z2i5Y+v6KeB5oCn5Y+Y5YyWICovCiAgICBoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlKCkgewogICAgICBpZiAoZG9jdW1lbnQuaGlkZGVuKSB7CiAgICAgICAgY29uc29sZS5sb2coJ/CfkYHvuI8gW+mhtemdoueKtuaAgV0g6aG16Z2i5Y+Y5Li65LiN5Y+v6KeB77yM5pqC5YGc6Ieq5Yqo5Yi35pawJykKICAgICAgICAvLyDpobXpnaLkuI3lj6/op4Hml7bmmoLlgZzoh6rliqjliLfmlrAKICAgICAgICBpZiAodGhpcy5yZWZyZXNoVGltZXIpIHsKICAgICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5yZWZyZXNoVGltZXIpCiAgICAgICAgICB0aGlzLnJlZnJlc2hUaW1lciA9IG51bGwKICAgICAgICAgIHRoaXMud2FzQXV0b1JlZnJlc2hBY3RpdmUgPSB0aGlzLmF1dG9SZWZyZXNoCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnNvbGUubG9nKCfwn5GB77iPIFvpobXpnaLnirbmgIFdIOmhtemdouWPmOS4uuWPr+inge+8jOaBouWkjeiHquWKqOWIt+aWsCcpCiAgICAgICAgLy8g6aG16Z2i5Y+v6KeB5pe25oGi5aSN6Ieq5Yqo5Yi35pawCiAgICAgICAgaWYgKHRoaXMud2FzQXV0b1JlZnJlc2hBY3RpdmUgJiYgdGhpcy5hdXRvUmVmcmVzaCkgewogICAgICAgICAgdGhpcy50b2dnbGVBdXRvUmVmcmVzaCh0cnVlKQogICAgICAgIH0KCiAgICAgICAgLy8g6aG16Z2i6YeN5paw5Y+v6KeB5pe277yM5aaC5p6c5pyJ6YCJ5oup54q25oCB6ZyA6KaB5oGi5aSN77yM56uL5Y2z5oGi5aSNCiAgICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFvpobXpnaLlj6/op4FdIOmhtemdoumHjeaWsOWPr+inge+8jOajgOafpemAieaLqeeKtuaAgScpCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgdGhpcy5yZXN0b3JlU2VsZWN0ZWRTdGF0ZSgpCiAgICAgICAgICB9LCAxMDApCiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfSwKICBiZWZvcmVEZXN0cm95KCkgewogICAgY29uc29sZS5sb2coJ/Cfp7kgW+e7hOS7tumUgOavgV0g5byA5aeL5riF55CG57uE5Lu26LWE5rqQJykKCiAgICBpZiAodGhpcy5yZWZyZXNoVGltZXIpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnJlZnJlc2hUaW1lcikKICAgICAgY29uc29sZS5sb2coJ/Cfp7kgW+e7hOS7tumUgOavgV0g5riF55CG6Ieq5Yqo5Yi35paw5a6a5pe25ZmoJykKICAgIH0KCiAgICAvLyDnp7vpmaTkuovku7bnm5HlkKzlmagKICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLmhhbmRsZVJlc2l6ZSkKICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Zpc2liaWxpdHljaGFuZ2UnLCB0aGlzLmhhbmRsZVZpc2liaWxpdHlDaGFuZ2UpCiAgICBjb25zb2xlLmxvZygn8J+nuSBb57uE5Lu26ZSA5q+BXSDmuIXnkIbkuovku7bnm5HlkKzlmagnKQoKICAgIC8vIOa4heeQhuS+p+i+ueagj+inguWvn+WZqAogICAgaWYgKHRoaXMuc2lkZWJhck9ic2VydmVyKSB7CiAgICAgIHRoaXMuc2lkZWJhck9ic2VydmVyLmRpc2Nvbm5lY3QoKQogICAgICBjb25zb2xlLmxvZygn8J+nuSBb57uE5Lu26ZSA5q+BXSDmuIXnkIbkvqfovrnmoI/op4Llr5/lmagnKQogICAgfQoKICAgIGNvbnNvbGUubG9nKCfinIUgW+e7hOS7tumUgOavgV0g57uE5Lu26LWE5rqQ5riF55CG5a6M5oiQJykKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/rms/prescription/review", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 筛选条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\n      <el-form-item prop=\"deptCode\">\n        <el-select v-model=\"queryParams.deptCode\" placeholder=\"请选择科室\" clearable style=\"width: 200px\">\n          <el-option\n            v-for=\"dept in departmentList\"\n            :key=\"dept.deptCode\"\n            :label=\"dept.deptName\"\n            :value=\"dept.deptCode\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"药品类型\" prop=\"prescriptionType\">\n        <el-radio-group v-model=\"queryParams.prescriptionType\">\n          <el-radio label=\"\">全部</el-radio>\n          <el-radio label=\"1\">西药</el-radio>\n          <el-radio label=\"2\">中药(草药)</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"搜索关键字\"\n          clearable\n          style=\"width: 200px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n      <!-- 自动刷新开关移动到筛选条件区域右侧 -->\n      <el-form-item style=\"float: right; margin-right: 0;\">\n        <el-switch\n          v-model=\"autoRefresh\"\n          active-text=\"开启审方\"\n          inactive-text=\"停止审方\"\n          @change=\"toggleAutoRefresh\">\n        </el-switch>\n      </el-form-item>\n    </el-form>\n\n    <!-- 主要内容区域 -->\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 处方列表 -->\n      <el-col :span=\"9\" style=\"height: 100%\">\n        <el-card class=\"list-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>待审核处方列表</span>\n            <span class=\"header-info\">（共 {{ total }} 条）</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"prescriptionList\"\n            @selection-change=\"handleSelectionChange\"\n            @row-click=\"handleRowClick\"\n            highlight-current-row\n            :height=\"tableHeight\"\n            ref=\"prescriptionTable\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" width=\"100\" show-overflow-tooltip />\n            <el-table-column label=\"医生\" align=\"center\" prop=\"doctName\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"患者\" align=\"center\" prop=\"name\" width=\"80\" show-overflow-tooltip />\n            <el-table-column label=\"严重程度\" align=\"center\" width=\"90\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getSeverityType(scope.row.level)\" size=\"mini\">\n                  {{ getSeverityText(scope.row.level) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"处方时间\" align=\"center\" prop=\"presTime\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n            class=\"pagination-container\"\n          />\n        </el-card>\n      </el-col>\n\n      <!-- 处方详情 -->\n      <el-col :span=\"15\" style=\"height: 100%\">\n        <el-card class=\"detail-card\" v-if=\"currentPrescription\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>处方详情</span>\n            <span class=\"header-info\">（{{ currentPrescription.code }}）</span>\n          </div>\n\n          <div class=\"detail-content\" :style=\"{ height: detailHeight, overflowY: 'auto' }\">\n            <!-- 合并的基本信息卡片 -->\n            <div class=\"unified-info-section\">\n              <!-- 处方基本信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">处方信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">处方号：</span>{{ currentPrescription.presId }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>\n                </el-row>\n              </div>\n\n              <!-- 医生和患者信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">医生和患者信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">科室：</span>{{ currentPrescription.deptName }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">医生：</span>{{ currentPrescription.doctName }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">患者：</span>{{ currentPrescription.name }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">性别/年龄：</span>{{ currentPrescription.sex }} / {{ calculateAge(currentPrescription.birth) }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"6\"><span class=\"label\">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">身高/体重：</span>{{ currentPrescription.height || '未知' }}cm / {{ currentPrescription.weight || '未知' }}kg</el-col>\n                  <el-col :span=\"6\"><span class=\"label\">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"24\"><span class=\"label\">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>\n                </el-row>\n              </div>\n\n              <!-- 诊断信息 -->\n              <div class=\"info-group\">\n                <h4 class=\"group-title\">诊断信息</h4>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>\n                </el-row>\n                <el-row :gutter=\"8\" class=\"compact-row\">\n                  <el-col :span=\"12\"><span class=\"label\">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>\n                  <el-col :span=\"12\"><span class=\"label\">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>\n                </el-row>\n              </div>\n            </div>\n\n            <!-- 药品明细 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">药品明细</h4>\n              <el-table :data=\"medicationList\" size=\"mini\" border max-height=\"200\">\n                <el-table-column label=\"药名\" align=\"center\" prop=\"medName\" min-width=\"120\" show-overflow-tooltip />\n                <el-table-column label=\"组号\" align=\"center\" prop=\"group\" width=\"50\" />\n                <el-table-column label=\"规格\" align=\"center\" prop=\"spec\" width=\"80\" show-overflow-tooltip />\n                <el-table-column label=\"给药途径\" align=\"center\" prop=\"administer\" width=\"80\" />\n                <el-table-column label=\"单次量\" align=\"center\" width=\"100\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.dose) || 0).toFixed(2) }}{{ scope.row.doseUnit }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"频次\" align=\"center\" prop=\"freq\" width=\"50\" />\n                <el-table-column label=\"天数\" align=\"center\" prop=\"days\" width=\"50\" />\n                <el-table-column label=\"开药数量\" align=\"center\" width=\"80\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.ordQty) || 0).toFixed(2) }}{{ scope.row.ordUom }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"金额\" align=\"center\" prop=\"money\" width=\"80\">\n                  <template slot-scope=\"scope\">\n                    {{ (Number(scope.row.money) || 0).toFixed(2) }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"用药说明\" align=\"center\" prop=\"yysm\" min-width=\"100\" show-overflow-tooltip />\n              </el-table>\n            </div>\n\n            <!-- 处方分析结果 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">处方分析结果</h4>\n              <el-tabs v-model=\"activeTab\" type=\"card\" class=\"compact-tabs\">\n                <el-tab-pane name=\"important\">\n                  <span slot=\"label\">\n                    重要问题 <el-badge :value=\"getAnalysisResultsByLevel('重要').length\" v-if=\"getAnalysisResultsByLevel('重要').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('重要')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"general\">\n                  <span slot=\"label\">\n                    一般问题 <el-badge :value=\"getAnalysisResultsByLevel('一般').length\" v-if=\"getAnalysisResultsByLevel('一般').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('一般')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n                <el-tab-pane name=\"other\">\n                  <span slot=\"label\">\n                    其它问题 <el-badge :value=\"getAnalysisResultsByLevel('其它').length\" v-if=\"getAnalysisResultsByLevel('其它').length > 0\" />\n                  </span>\n                  <el-table :data=\"getAnalysisResultsByLevel('其它')\" size=\"mini\" max-height=\"150\">\n                    <el-table-column label=\"药物A\" prop=\"ywa\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"药物B\" prop=\"ywb\" width=\"100\" show-overflow-tooltip />\n                    <el-table-column label=\"问题名称\" prop=\"wtname\" width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"标题\" prop=\"title\" min-width=\"120\" show-overflow-tooltip />\n                    <el-table-column label=\"详情\" prop=\"detail\" min-width=\"150\" show-overflow-tooltip />\n                  </el-table>\n                </el-tab-pane>\n              </el-tabs>\n            </div>\n\n            <!-- 历史审核记录 -->\n            <div class=\"info-section\">\n              <h4 class=\"section-title\">历史审核记录</h4>\n              <el-table :data=\"reviewHistory\" size=\"mini\" border max-height=\"120\">\n                <el-table-column label=\"审核医师\" prop=\"nickName\" width=\"80\" />\n                <el-table-column label=\"审核时间\" prop=\"createTime\" width=\"130\">\n                  <template slot-scope=\"scope\">\n                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"审核意见\" prop=\"text\" min-width=\"150\" show-overflow-tooltip />\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n        <el-card v-else class=\"detail-card\">\n          <div class=\"text-center\" style=\"padding: 50px 0; color: #999;\">\n            请选择处方查看详情\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 固定在底部的审核操作区域 -->\n    <div class=\"review-actions-fixed\">\n      <el-card class=\"actions-card\">\n        <el-row :gutter=\"15\" align=\"middle\" type=\"flex\">\n          <el-col :span=\"8\">\n            <el-select\n              v-model=\"selectedProblems\"\n              multiple\n              placeholder=\"选择问题类型\"\n              style=\"width: 100%\"\n              size=\"small\"\n              collapse-tags>\n              <el-option\n                v-for=\"problem in problemTypes\"\n                :key=\"problem.cfwtbh\"\n                :label=\"problem.cfwtbh + ' ' +problem.cfwtname\"\n                :value=\"problem.cfwtname\">\n              </el-option>\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"danger\"\n              icon=\"el-icon-close\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleReject\"\n              v-hasPermi=\"['rms:prescription:review:reject']\"\n              style=\"width: 100%\">\n              审核打回\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-check\"\n              size=\"small\"\n              :disabled=\"multiple\"\n              @click=\"handleApprove\"\n              v-hasPermi=\"['rms:prescription:review:approve']\"\n              style=\"width: 100%\">\n              审核通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"8\" class=\"text-right\">\n            <span class=\"selection-info\">\n              已选择 <strong>{{ ids.length }}</strong> 条处方\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"clearSelection\"\n                v-if=\"ids.length > 0\">\n                清空选择\n              </el-button>\n            </span>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPendingPrescriptions,\n  getPrescriptionDetail,\n  getProblemTypes,\n  getRefreshTime,\n  approvePrescriptions,\n  rejectPrescriptions,\n  getDepartments\n} from \"@/api/rms/prescriptionreview\"\n\nexport default {\n  name: \"PrescriptionReview\",\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 选中的处方对象数组（用于状态持久化）\n      selectedPrescriptions: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 处方列表\n      prescriptionList: [],\n      // 当前选中的处方\n      currentPrescription: null,\n      // 药品明细列表\n      medicationList: [],\n      // 分析结果列表\n      analysisResults: [],\n      // 审核历史列表\n      reviewHistory: [],\n      // 科室列表\n      departmentList: [],\n      // 问题类型列表\n      problemTypes: [],\n      // 选中的问题类型\n      selectedProblems: [],\n      // 自动刷新开关\n      autoRefresh: false,\n      // 刷新定时器\n      refreshTimer: null,\n      // 刷新间隔（毫秒）\n      refreshInterval: 5000,\n      // 是否由自动刷新触发的标记\n      isAutoRefreshTriggered: false,\n      // 页面不可见前是否开启了自动刷新\n      wasAutoRefreshActive: false,\n      // 是否正在恢复选择状态（用于阻止意外的选择变化事件）\n      isRestoringSelection: false,\n      // 当前活跃的标签页\n      activeTab: 'important',\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        deptCode: null,\n        prescriptionType: '',\n        keyword: null\n      }\n    }\n  },\n  computed: {\n    /** 计算表格高度 */\n    tableHeight() {\n      // 100vh - 顶部导航(50px) - 面包屑(40px) - 搜索区域(80px) - 卡片头部(60px) - 分页区域(50px) - 底部审核区域(80px) - 边距(40px)\n      return 'calc(100vh - 350px)'\n    },\n    /** 计算详情区域高度 */\n    detailHeight() {\n      // 与表格高度保持一致，减去卡片头部\n      return 'calc(100vh - 340px)'\n    }\n  },\n  created() {\n    this.getList()\n    this.getDepartmentList()\n    this.getProblemTypeList()\n    this.getRefreshConfig()\n    this.loadSelectedState()\n  },\n  mounted() {\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize)\n    // 监听侧边栏状态变化\n    this.checkSidebarStatus()\n    // 监听页面可见性变化\n    document.addEventListener('visibilitychange', this.handleVisibilityChange)\n  },\n  methods: {\n    /** 查询处方列表 */\n    getList() {\n      console.log('🔄 [数据加载] 开始加载处方列表', {\n        是否自动刷新: this.autoRefresh,\n        当前页码: this.queryParams.pageNum,\n        页面大小: this.queryParams.pageSize,\n        加载时间: new Date().toLocaleTimeString(),\n        当前选择状态: this.ids\n      })\n\n      // 在数据加载前保存当前的选择状态\n      const savedIds = [...this.ids]\n      console.log('💾 [状态备份] 备份当前选择状态', { 备份的ids: savedIds })\n\n      this.loading = true\n      // 设置恢复状态标记，防止表格重新渲染时清空选择\n      this.isRestoringSelection = true\n\n      // 构建查询参数\n      let params = { ...this.queryParams }\n\n      // 处理关键字搜索\n      if (params.keyword) {\n        params.doctName = params.keyword\n        params.name = params.keyword\n      }\n\n      getPendingPrescriptions(params).then(response => {\n        console.log('📊 [数据加载] 处方列表加载完成', {\n          数据条数: response.rows.length,\n          总数: response.total,\n          处方编码列表: response.rows.map(item => item.code)\n        })\n\n        this.prescriptionList = response.rows\n        this.total = response.total\n        this.loading = false\n\n        // 恢复备份的选择状态\n        this.ids = savedIds\n\n        // 验证并清理无效的选择状态\n        this.validateAndCleanSelectedState()\n\n        console.log('🔄 [状态恢复] 准备恢复选择状态', {\n          备份的ids: savedIds,\n          当前ids: this.ids,\n          ids数量: this.ids.length,\n          表格ref存在: !!this.$refs.prescriptionTable,\n          是否自动刷新触发: this.isAutoRefreshTriggered\n        })\n\n        // 根据是否自动刷新使用不同的恢复策略\n        if (this.isAutoRefreshTriggered) {\n          console.log('🔄 [自动刷新] 使用自动刷新专用恢复机制')\n          this.handleAutoRefreshRestore()\n          this.isAutoRefreshTriggered = false // 重置标记\n        } else {\n          console.log('👆 [手动操作] 使用常规恢复机制')\n          // 恢复选择状态 - 确保DOM完全更新后再恢复\n          this.$nextTick(() => {\n            console.log('⏰ [延迟恢复] $nextTick执行，准备延迟恢复状态')\n            // 延迟一小段时间确保表格完全渲染\n            setTimeout(() => {\n              console.log('🎯 [开始恢复] 开始执行状态恢复')\n              this.restoreSelectedState()\n            }, 200) // 增加延迟时间到200ms\n          })\n        }\n      }).catch(error => {\n        this.loading = false\n        this.isRestoringSelection = false // 出错时也要重置标记\n        console.error('❌ [加载失败] 获取处方列表失败:', error)\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      console.log('🔄 [选择变化] 触发选择变化事件', {\n        选中数量: selection.length,\n        选中处方: selection.map(item => ({ code: item.code, name: item.name })),\n        触发时间: new Date().toLocaleTimeString(),\n        正在恢复状态: this.isRestoringSelection\n      })\n\n      // 如果正在恢复选择状态，忽略这次选择变化事件\n      if (this.isRestoringSelection) {\n        console.log('🚫 [忽略事件] 正在恢复选择状态，忽略此次选择变化事件')\n        return\n      }\n\n      this.ids = selection.map(item => item.code)\n      this.selectedPrescriptions = selection\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n      console.log('multiple', this.multiple)\n      console.log('selection.length', selection.length)\n\n      console.log('💾 [状态更新] 更新组件状态', {\n        ids: this.ids,\n        single: this.single,\n        multiple: this.multiple\n      })\n\n      // 保存选择状态到本地存储\n      this.saveSelectedState()\n    },\n\n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.currentPrescription = row\n      this.getPrescriptionDetailData(row.code)\n    },\n\n    /** 获取处方详情 */\n    getPrescriptionDetailData(code) {\n      getPrescriptionDetail(code).then(response => {\n        this.currentPrescription = response.data.prescription\n        this.medicationList = response.data.medications || []\n        this.analysisResults = response.data.analysisResults || []\n        this.reviewHistory = response.data.reviewHistory || []\n      })\n    },\n\n    /** 获取科室列表 */\n    getDepartmentList() {\n      getDepartments().then(response => {\n        this.departmentList = response.data || []\n      })\n    },\n\n    /** 获取问题类型列表 */\n    getProblemTypeList() {\n      getProblemTypes().then(response => {\n        this.problemTypes = response.data || []\n      })\n    },\n\n    /** 获取刷新配置 */\n    getRefreshConfig() {\n      getRefreshTime().then(response => {\n        this.refreshInterval = response.data || 5000\n      })\n    },\n\n    /** 切换自动刷新 */\n    toggleAutoRefresh(value) {\n      console.log('🔄 [自动刷新] 切换自动刷新状态', {\n        开启: value,\n        刷新间隔: this.refreshInterval,\n        当前选择数量: this.ids.length\n      })\n\n      if (value) {\n        this.refreshTimer = setInterval(() => {\n          console.log('⏰ [自动刷新] 自动刷新定时器触发', {\n            触发时间: new Date().toLocaleTimeString(),\n            当前选择状态: this.ids\n          })\n\n          // 标记这是自动刷新触发的数据加载\n          this.isAutoRefreshTriggered = true\n          this.getList()\n        }, this.refreshInterval)\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n      }\n    },\n\n    /** 审核通过 */\n    handleApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审核的处方\")\n        return\n      }\n\n      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {\n        return approvePrescriptions(this.ids)\n      }).then(() => {\n        this.getList()\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核通过成功\")\n      }).catch(() => {})\n    },\n\n    /** 审核打回 */\n    handleReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要打回的处方\")\n        return\n      }\n\n      if (this.selectedProblems.length === 0) {\n        this.$modal.msgError(\"请选择问题类型\")\n        return\n      }\n\n      this.$modal.confirm('是否确认打回选中的处方？').then(() => {\n        const data = {\n          codes: this.ids,\n          problemNames: this.selectedProblems\n        }\n        return rejectPrescriptions(data)\n      }).then(() => {\n        this.getList()\n        this.selectedProblems = []\n        this.clearSelection()\n        this.$modal.msgSuccess(\"审核打回成功\")\n      }).catch(() => {})\n    },\n\n    /** 根据问题等级获取分析结果 */\n    getAnalysisResultsByLevel(level) {\n      return this.analysisResults.filter(item => item.wtlvl === level)\n    },\n\n    /** 获取严重程度类型 */\n    getSeverityType(level) {\n      switch(level) {\n        case '重要': return 'danger'\n        case '一般': return 'warning'\n        case '其它': return 'info'\n        default: return 'info'\n      }\n    },\n\n    /** 获取严重程度文本 */\n    getSeverityText(level) {\n      return level || '未知'\n    },\n\n    /** 计算年龄 */\n    calculateAge(birthDate) {\n      if (!birthDate) return '未知'\n      const birth = new Date(birthDate)\n      const now = new Date()\n      const age = now.getFullYear() - birth.getFullYear()\n      return age + '岁'\n    },\n\n    /** 保存选择状态到本地存储 */\n    saveSelectedState() {\n      const selectedCodes = this.ids\n      console.log('💾 [保存状态] 保存选择状态到localStorage', {\n        选中的处方编码: selectedCodes,\n        数量: selectedCodes.length,\n        保存时间: new Date().toLocaleTimeString()\n      })\n\n      localStorage.setItem('prescription_review_selected', JSON.stringify(selectedCodes))\n\n      // 验证保存是否成功\n      const saved = localStorage.getItem('prescription_review_selected')\n      console.log('✅ [保存验证] localStorage保存结果', {\n        保存的数据: saved,\n        解析后: JSON.parse(saved || '[]')\n      })\n    },\n\n    /** 从本地存储加载选择状态 */\n    loadSelectedState() {\n      console.log('📖 [加载状态] 开始从localStorage加载选择状态')\n\n      try {\n        const savedSelection = localStorage.getItem('prescription_review_selected')\n        console.log('📖 [加载状态] localStorage中的数据', {\n          原始数据: savedSelection,\n          数据类型: typeof savedSelection\n        })\n\n        if (savedSelection) {\n          const parsedIds = JSON.parse(savedSelection)\n          this.ids = parsedIds\n          this.multiple = this.ids.length === 0\n          this.single = this.ids.length !== 1\n\n          console.log('✅ [加载成功] 状态加载完成', {\n            加载的ids: this.ids,\n            数量: this.ids.length,\n            multiple: this.multiple,\n            single: this.single\n          })\n        } else {\n          console.log('ℹ️ [加载状态] localStorage中没有保存的选择状态')\n          this.ids = []\n          this.multiple = true\n          this.single = true\n        }\n      } catch (error) {\n        console.error('❌ [加载失败] 加载选择状态失败:', error)\n        this.ids = []\n        this.multiple = true\n        this.single = true\n      }\n    },\n\n    /** 验证并清理无效的选择状态 */\n    validateAndCleanSelectedState() {\n      if (this.ids.length === 0) {\n        console.log('ℹ️ [状态验证] 没有选择状态需要验证')\n        return\n      }\n\n      // 检查localStorage中的ID是否在当前数据列表中存在\n      const currentCodes = this.prescriptionList.map(item => item.code)\n      const validIds = this.ids.filter(id => currentCodes.includes(id))\n\n      console.log('🔍 [状态验证] 验证选择状态有效性', {\n        原始ids: this.ids,\n        当前数据编码: currentCodes,\n        有效的ids: validIds,\n        无效的ids: this.ids.filter(id => !currentCodes.includes(id))\n      })\n\n      if (validIds.length !== this.ids.length) {\n        // 有无效的ID，需要清理\n        console.log('🧹 [状态清理] 发现无效的选择状态，进行清理')\n        this.ids = validIds\n        this.multiple = this.ids.length === 0\n        this.single = this.ids.length !== 1\n\n        // 更新localStorage\n        if (this.ids.length === 0) {\n          localStorage.removeItem('prescription_review_selected')\n          console.log('🗑️ [状态清理] 清空localStorage中的选择状态')\n        } else {\n          localStorage.setItem('prescription_review_selected', JSON.stringify(this.ids))\n          console.log('💾 [状态清理] 更新localStorage中的选择状态', { 更新后的ids: this.ids })\n        }\n      }\n    },\n\n    /** 恢复表格选择状态 */\n    restoreSelectedState() {\n      console.log('🎯 [状态恢复] 开始恢复表格选择状态', {\n        需要恢复的ids: this.ids,\n        ids数量: this.ids.length,\n        表格ref存在: !!this.$refs.prescriptionTable,\n        处方列表长度: this.prescriptionList.length,\n        处方列表编码: this.prescriptionList.map(item => item.code)\n      })\n\n      // 检查前置条件\n      if (this.ids.length === 0) {\n        console.log('ℹ️ [状态恢复] 没有需要恢复的选择状态')\n        return\n      }\n\n      if (!this.$refs.prescriptionTable) {\n        console.error('❌ [状态恢复] 表格ref不存在')\n        return\n      }\n\n      if (this.prescriptionList.length === 0) {\n        console.log('ℹ️ [状态恢复] 处方列表为空，无法恢复状态')\n        return\n      }\n\n      try {\n        // 清除当前选择\n        console.log('🧹 [清除选择] 清除表格当前选择状态')\n        this.$refs.prescriptionTable.clearSelection()\n\n        // 恢复选择状态\n        let restoredCount = 0\n        let matchedRows = []\n        let unmatchedIds = []\n\n        this.prescriptionList.forEach(row => {\n          if (this.ids.includes(row.code)) {\n            console.log(`✅ [匹配成功] 找到匹配的处方: ${row.code} - ${row.name}`)\n            this.$refs.prescriptionTable.toggleRowSelection(row, true)\n            restoredCount++\n            matchedRows.push({ code: row.code, name: row.name })\n          }\n        })\n\n        // 检查未匹配的ID\n        this.ids.forEach(id => {\n          if (!this.prescriptionList.find(row => row.code === id)) {\n            unmatchedIds.push(id)\n          }\n        })\n\n        // 更新选择状态统计\n        this.multiple = this.ids.length === 0\n        this.single = this.ids.length !== 1\n\n        console.log('📊 [恢复结果] 状态恢复完成', {\n          恢复成功数量: restoredCount,\n          匹配的处方: matchedRows,\n          未匹配的ID: unmatchedIds,\n          最终状态: {\n            multiple: this.multiple,\n            single: this.single,\n            ids: this.ids\n          }\n        })\n\n        // 验证恢复结果\n        this.$nextTick(() => {\n          const selectedRows = this.$refs.prescriptionTable.selection || []\n          console.log('🔍 [恢复验证] 验证表格选择状态', {\n            表格选中行数: selectedRows.length,\n            表格选中编码: selectedRows.map(row => row.code),\n            期望选中数量: this.ids.length,\n            状态一致: selectedRows.length === restoredCount\n          })\n\n          if (selectedRows.length !== restoredCount) {\n            console.warn('⚠️ [状态不一致] 表格选择状态与预期不符，尝试重新恢复')\n            // 如果状态不一致，再次尝试恢复\n            setTimeout(() => {\n              this.restoreSelectedStateRetry()\n            }, 100)\n          } else {\n            // 恢复成功，重置恢复状态标记\n            console.log('✅ [恢复完成] 状态恢复成功，重置恢复标记')\n            this.isRestoringSelection = false\n          }\n        })\n\n      } catch (error) {\n        console.error('❌ [恢复失败] 恢复选择状态失败:', error)\n      }\n    },\n\n    /** 清空选择 */\n    clearSelection() {\n      // 设置恢复标记，防止clearSelection触发选择变化事件\n      this.isRestoringSelection = true\n\n      this.ids = []\n      this.selectedPrescriptions = []\n      this.multiple = true\n      this.single = true\n\n      // 清除表格选择\n      if (this.$refs.prescriptionTable) {\n        this.$refs.prescriptionTable.clearSelection()\n      }\n\n      // 清除本地存储\n      localStorage.removeItem('prescription_review_selected')\n\n      console.log('🧹 [清空选择] 选择状态已清空', {\n        清空时间: new Date().toLocaleTimeString()\n      })\n\n      // 重置恢复标记\n      setTimeout(() => {\n        this.isRestoringSelection = false\n      }, 100)\n    },\n\n    /** 重试恢复选择状态 */\n    restoreSelectedStateRetry() {\n      console.log('🔄 [重试恢复] 开始重试恢复选择状态')\n\n      if (!this.$refs.prescriptionTable) {\n        console.error('❌ [重试失败] 表格ref仍然不存在')\n        this.isRestoringSelection = false\n        return\n      }\n\n      // 强制清除选择\n      this.$refs.prescriptionTable.clearSelection()\n\n      // 重新恢复\n      let retryCount = 0\n      this.prescriptionList.forEach(row => {\n        if (this.ids.includes(row.code)) {\n          this.$refs.prescriptionTable.toggleRowSelection(row, true)\n          retryCount++\n          console.log(`🔄 [重试恢复] 重新选择处方: ${row.code}`)\n        }\n      })\n\n      console.log(`✅ [重试完成] 重试恢复了 ${retryCount} 个处方的选择状态`)\n\n      // 重试完成后重置标记\n      this.isRestoringSelection = false\n    },\n\n    /** 自动刷新时的特殊处理 */\n    handleAutoRefreshRestore() {\n      console.log('🔄 [自动刷新] 自动刷新触发，特殊处理选择状态恢复')\n\n      // 在自动刷新时，给更多时间让表格完全渲染\n      this.$nextTick(() => {\n        setTimeout(() => {\n          console.log('🎯 [自动刷新恢复] 开始自动刷新后的状态恢复')\n          this.restoreSelectedState()\n\n          // 额外验证\n          setTimeout(() => {\n            const selectedRows = this.$refs.prescriptionTable?.selection || []\n            if (selectedRows.length === 0 && this.ids.length > 0) {\n              console.warn('⚠️ [自动刷新] 第一次恢复失败，进行第二次尝试')\n              this.restoreSelectedStateRetry()\n            } else {\n              // 自动刷新恢复成功，重置标记\n              console.log('✅ [自动刷新] 自动刷新恢复成功，重置恢复标记')\n              this.isRestoringSelection = false\n            }\n          }, 300)\n        }, 300) // 自动刷新时使用更长的延迟\n      })\n    },\n\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 窗口大小变化时，强制重新计算表格高度\n      this.$nextTick(() => {\n        if (this.$refs.prescriptionTable) {\n          this.$refs.prescriptionTable.doLayout()\n        }\n      })\n    },\n\n    /** 检查侧边栏状态 */\n    checkSidebarStatus() {\n      // 检查body是否有hideSidebar类\n      const body = document.body\n      if (body.classList.contains('hideSidebar')) {\n        // 侧边栏已收起\n        this.updateFixedAreaPosition(true)\n      } else {\n        // 侧边栏展开\n        this.updateFixedAreaPosition(false)\n      }\n\n      // 监听侧边栏状态变化\n      const observer = new MutationObserver((mutations) => {\n        mutations.forEach((mutation) => {\n          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {\n            const isHidden = body.classList.contains('hideSidebar')\n            this.updateFixedAreaPosition(isHidden)\n          }\n        })\n      })\n\n      observer.observe(body, {\n        attributes: true,\n        attributeFilter: ['class']\n      })\n\n      // 保存observer引用以便清理\n      this.sidebarObserver = observer\n    },\n\n    /** 更新固定区域位置 */\n    updateFixedAreaPosition(isHidden) {\n      const fixedArea = document.querySelector('.review-actions-fixed')\n      if (fixedArea) {\n        if (isHidden) {\n          fixedArea.style.left = '54px'\n          fixedArea.style.maxWidth = 'calc(100vw - 54px)'\n        } else {\n          fixedArea.style.left = '200px'\n          fixedArea.style.maxWidth = 'calc(100vw - 200px)'\n        }\n      }\n    },\n\n    /** 处理页面可见性变化 */\n    handleVisibilityChange() {\n      if (document.hidden) {\n        console.log('👁️ [页面状态] 页面变为不可见，暂停自动刷新')\n        // 页面不可见时暂停自动刷新\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n          this.wasAutoRefreshActive = this.autoRefresh\n        }\n      } else {\n        console.log('👁️ [页面状态] 页面变为可见，恢复自动刷新')\n        // 页面可见时恢复自动刷新\n        if (this.wasAutoRefreshActive && this.autoRefresh) {\n          this.toggleAutoRefresh(true)\n        }\n\n        // 页面重新可见时，如果有选择状态需要恢复，立即恢复\n        if (this.ids.length > 0) {\n          console.log('🔄 [页面可见] 页面重新可见，检查选择状态')\n          setTimeout(() => {\n            this.restoreSelectedState()\n          }, 100)\n        }\n      }\n    }\n  },\n  beforeDestroy() {\n    console.log('🧹 [组件销毁] 开始清理组件资源')\n\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n      console.log('🧹 [组件销毁] 清理自动刷新定时器')\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('resize', this.handleResize)\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange)\n    console.log('🧹 [组件销毁] 清理事件监听器')\n\n    // 清理侧边栏观察器\n    if (this.sidebarObserver) {\n      this.sidebarObserver.disconnect()\n      console.log('🧹 [组件销毁] 清理侧边栏观察器')\n    }\n\n    console.log('✅ [组件销毁] 组件资源清理完成')\n  }\n}\n</script>\n\n<style scoped>\n/* 主要内容区域 */\n.app-container {\n  /*padding-bottom: 80px; !* 为固定的审核区域留出空间 *!*/\n}\n\n.main-content {\n  height: calc(100vh - 230px); /* 顶部导航(50px) + 面包屑(40px) + 搜索区域(80px) + 底部审核区域(80px) - 一些边距，移除工具栏高度 */\n}\n\n/* 处方列表卡片 */\n.list-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.list-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.pagination-container {\n  margin-top: 10px;\n  text-align: center;\n  flex-shrink: 0;\n}\n\n/* 处方详情卡片 */\n.detail-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-card .el-card__body {\n  padding: 10px;\n  flex: 1;\n  overflow: hidden;\n}\n\n.detail-content {\n  padding-right: 5px;\n}\n\n/* 统一信息区域样式 */\n.unified-info-section {\n  border: 1px solid #ebeef5;\n  border-radius: 6px;\n  padding: 12px;\n  background-color: #fafafa;\n  margin-bottom: 12px;\n}\n\n.info-group {\n  margin-bottom: 12px;\n}\n\n.info-group:last-child {\n  margin-bottom: 0;\n}\n\n.group-title {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 13px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 4px;\n}\n\n.compact-row {\n  margin-bottom: 6px;\n  font-size: 12px;\n  line-height: 1.3;\n}\n\n.compact-row:last-child {\n  margin-bottom: 0;\n}\n\n.label {\n  font-weight: 500;\n  color: #606266;\n  margin-right: 4px;\n}\n\n/* 保留原有信息区域样式以兼容其他部分 */\n.info-section {\n  margin-bottom: 12px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 8px;\n  background-color: #fafafa;\n}\n\n.section-title {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-weight: 500;\n  font-size: 13px;\n  border-bottom: 1px solid #e4e7ed;\n  padding-bottom: 4px;\n}\n\n.info-row {\n  margin-bottom: 6px;\n  font-size: 12px;\n  line-height: 1.3;\n}\n\n/* 紧凑的标签页 */\n.compact-tabs .el-tabs__header {\n  margin-bottom: 10px;\n}\n\n.compact-tabs .el-tab-pane {\n  padding: 0;\n}\n\n/* 固定在底部的审核操作区域 */\n.review-actions-fixed {\n  position: fixed;\n  bottom: 0;\n  left: 200px; /* 侧边栏宽度 */\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(to top, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);\n  backdrop-filter: blur(5px);\n  border-top: 1px solid #e4e7ed;\n  box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.1);\n  max-width: calc(100vw - 200px); /* 限制最大宽度，减去侧边栏宽度 */\n}\n\n/* 当侧边栏收起时的适配 */\n.hideSidebar .review-actions-fixed {\n  left: 54px; /* 收起后的侧边栏宽度 */\n  max-width: calc(100vw - 54px);\n}\n\n/* 移动端适配 */\n@media (max-width: 768px) {\n  .review-actions-fixed {\n    left: 0;\n    right: 0;\n    max-width: 100vw;\n  }\n}\n\n.actions-card {\n  margin: 0;\n  border: none;\n  border-radius: 0;\n  box-shadow: none;\n}\n\n.actions-card .el-card__body {\n  padding: 15px 20px;\n}\n\n.selection-info {\n  color: #606266;\n  font-size: 14px;\n}\n\n.selection-info strong {\n  color: #409eff;\n}\n\n/* 头部信息样式 */\n.header-info {\n  color: #909399;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .main-content .el-col:first-child {\n    margin-bottom: 20px;\n  }\n\n  .info-row .el-col {\n    margin-bottom: 5px;\n  }\n\n  /* 调整小屏幕下的高度计算 */\n  .main-content {\n    height: calc(100vh - 280px);\n  }\n}\n\n@media (max-width: 768px) {\n  .main-content {\n    height: calc(100vh - 320px);\n  }\n\n  .review-actions-fixed .el-row .el-col {\n    margin-bottom: 10px;\n  }\n\n  .actions-card .el-card__body {\n    padding: 10px 15px;\n  }\n}\n\n/* 超大屏幕优化 */\n@media (min-width: 1920px) {\n  .main-content {\n    height: calc(100vh - 220px);\n  }\n}\n\n/* 通用样式 */\n.text-center {\n  text-align: center;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-table {\n  margin-bottom: 5px;\n}\n\n/* 表格优化 */\n.el-table .cell {\n  padding: 0 5px;\n}\n\n.el-table--mini td {\n  padding: 4px 0;\n}\n\n/* 徽章样式 */\n.el-badge {\n  margin-left: 5px;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"]}]}