{"remainingRequest": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1752831475333}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\babel.config.js", "mtime": 1748394285000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\RationalMedicationSystem\\rational-medication-system\\rms-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9SYXRpb25hbE1lZGljYXRpb25TeXN0ZW0vcmF0aW9uYWwtbWVkaWNhdGlvbi1zeXN0ZW0vcm1zLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwp2YXIgX2xvZ28gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvYXNzZXRzL2xvZ28vbG9nby5wbmciKSk7CnZhciBfdmFyaWFibGVzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9hc3NldHMvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogJ1NpZGViYXJMb2dvJywKICBwcm9wczogewogICAgY29sbGFwc2U6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICB2YXJpYWJsZXM6IGZ1bmN0aW9uIHZhcmlhYmxlcygpIHsKICAgICAgcmV0dXJuIF92YXJpYWJsZXMyLmRlZmF1bHQ7CiAgICB9LAogICAgc2lkZVRoZW1lOiBmdW5jdGlvbiBzaWRlVGhlbWUoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5zaWRlVGhlbWU7CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGl0bGU6IHByb2Nlc3MuZW52LlZVRV9BUFBfVElUTEUsCiAgICAgIGxvZ286IF9sb2dvLmRlZmF1bHQKICAgIH07CiAgfQp9Ow=="}, {"version": 3, "names": ["_logo", "_interopRequireDefault", "require", "_variables2", "name", "props", "collapse", "type", "Boolean", "required", "computed", "variables", "sideTheme", "$store", "state", "settings", "data", "title", "process", "env", "VUE_APP_TITLE", "logo", "logoImg"], "sources": ["src/layout/components/Sidebar/Logo.vue"], "sourcesContent": ["<template>\n  <div class=\"sidebar-logo-container\" :class=\"{'collapse':collapse}\" :style=\"{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\n    <transition name=\"sidebarLogoFade\">\n      <router-link v-if=\"collapse\" key=\"collapse\" class=\"sidebar-logo-link\" to=\"/\">\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\" />\n        <h1 v-else class=\"sidebar-title\" :style=\"{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }\">{{ title }} </h1>\n      </router-link>\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\" />\n        <h1 class=\"sidebar-title\" :style=\"{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }\">{{ title }} </h1>\n      </router-link>\n    </transition>\n  </div>\n</template>\n\n<script>\nimport logoImg from '@/assets/logo/logo.png'\nimport variables from '@/assets/styles/variables.scss'\n\nexport default {\n  name: 'SidebarLogo',\n  props: {\n    collapse: {\n      type: Boolean,\n      required: true\n    }\n  },\n  computed: {\n    variables() {\n      return variables\n    },\n    sideTheme() {\n      return this.$store.state.settings.sideTheme\n    }\n  },\n  data() {\n    return {\n      title: process.env.VUE_APP_TITLE,\n      logo: logoImg\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sidebarLogoFade-enter-active {\n  transition: opacity 1.5s;\n}\n\n.sidebarLogoFade-enter,\n.sidebarLogoFade-leave-to {\n  opacity: 0;\n}\n\n.sidebar-logo-container {\n  position: relative;\n  width: 100%;\n  height: 50px;\n  line-height: 50px;\n  background: #2b2f3a;\n  text-align: center;\n  overflow: hidden;\n\n  & .sidebar-logo-link {\n    height: 100%;\n    width: 100%;\n\n    & .sidebar-logo {\n      width: 32px;\n      height: 32px;\n      vertical-align: middle;\n      margin-right: 12px;\n    }\n\n    & .sidebar-title {\n      display: inline-block;\n      margin: 0;\n      color: #fff;\n      font-weight: 600;\n      line-height: 50px;\n      font-size: 18px;\n      font-family: AL-L, Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\n      vertical-align: middle;\n    }\n  }\n\n  &.collapse {\n    .sidebar-logo {\n      margin-right: 0px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;AAgBA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,SAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,aAAA;MACAC,IAAA,EAAAC;IACA;EACA;AACA", "ignoreList": []}]}