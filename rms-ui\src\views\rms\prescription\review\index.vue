<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item prop="deptCode">
        <el-select v-model="queryParams.deptCode" placeholder="请选择科室" clearable style="width: 200px">
          <el-option
            v-for="dept in departmentList"
            :key="dept.deptCode"
            :label="dept.deptName"
            :value="dept.deptCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="药品类型" prop="prescriptionType">
        <el-radio-group v-model="queryParams.prescriptionType">
          <el-radio label="">全部</el-radio>
          <el-radio label="1">西药</el-radio>
          <el-radio label="2">中药(草药)</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="搜索关键字"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <!-- 自动刷新开关移动到筛选条件区域右侧 -->
      <el-form-item style="float: right; margin-right: 0;">
        <el-switch
          v-model="autoRefresh"
          active-text="开启审方"
          inactive-text="停止审方"
          @change="toggleAutoRefresh">
        </el-switch>
      </el-form-item>
    </el-form>

    <!-- 主要内容区域 -->
    <el-row :gutter="10" class="main-content">
      <!-- 处方列表 -->
      <el-col :span="9" style="height: 100%">
        <el-card class="list-card">
          <div slot="header" class="clearfix">
            <span>待审核处方列表</span>
            <span class="header-info">（共 {{ total }} 条）</span>
          </div>
          <el-table
            v-loading="loading"
            :data="prescriptionList"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            highlight-current-row
            :height="tableHeight"
            ref="prescriptionTable">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="科室" align="center" prop="deptName" width="100" show-overflow-tooltip />
            <el-table-column label="医生" align="center" prop="doctName" width="80" show-overflow-tooltip />
            <el-table-column label="患者" align="center" prop="name" width="80" show-overflow-tooltip />
            <el-table-column label="严重程度" align="center" width="90">
              <template slot-scope="scope">
                <el-tag :type="getSeverityType(scope.row.level)" size="mini">
                  {{ getSeverityText(scope.row.level) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="处方时间" align="center" prop="presTime" width="100">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.presTime, '{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
            class="pagination-container"
          />
        </el-card>
      </el-col>

      <!-- 处方详情 -->
      <el-col :span="15" style="height: 100%">
        <el-card class="detail-card" v-if="currentPrescription">
          <div slot="header" class="clearfix">
            <span>处方详情</span>
            <span class="header-info">（{{ currentPrescription.code }}）</span>
          </div>

          <div class="detail-content" :style="{ height: detailHeight, overflowY: 'auto' }">
            <!-- 合并的基本信息卡片 -->
            <div class="unified-info-section">
              <!-- 处方基本信息 -->
              <div class="info-group">
                <h4 class="group-title">处方信息</h4>
                <el-row :gutter="8" class="compact-row">
                  <el-col :span="6"><span class="label">处方号：</span>{{ currentPrescription.presId }}</el-col>
                  <el-col :span="6"><span class="label">金额：</span>{{ currentPrescription.money || '未知' }}</el-col>
                  <el-col :span="6"><span class="label">就诊号：</span>{{ currentPrescription.treatCode }}</el-col>
                  <el-col :span="6"><span class="label">就诊日期：</span>{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-col>
                </el-row>
                <el-row :gutter="8" class="compact-row">
                  <el-col :span="12"><span class="label">处方说明：</span>{{ currentPrescription.presSm || '无' }}</el-col>
                  <el-col :span="12"><span class="label">服用方法：</span>{{ currentPrescription.requir || '无' }}</el-col>
                </el-row>
              </div>

              <!-- 医生和患者信息 -->
              <div class="info-group">
                <h4 class="group-title">医生和患者信息</h4>
                <el-row :gutter="8" class="compact-row">
                  <el-col :span="6"><span class="label">科室：</span>{{ currentPrescription.deptName }}</el-col>
                  <el-col :span="6"><span class="label">医生：</span>{{ currentPrescription.doctName }}</el-col>
                  <el-col :span="6"><span class="label">患者：</span>{{ currentPrescription.name }}</el-col>
                  <el-col :span="6"><span class="label">性别/年龄：</span>{{ currentPrescription.sex }} / {{ calculateAge(currentPrescription.birth) }}</el-col>
                </el-row>
                <el-row :gutter="8" class="compact-row">
                  <el-col :span="6"><span class="label">出生日期：</span>{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-col>
                  <el-col :span="6"><span class="label">身高/体重：</span>{{ currentPrescription.height || '未知' }}cm / {{ currentPrescription.weight || '未知' }}kg</el-col>
                  <el-col :span="6"><span class="label">孕周：</span>{{ currentPrescription.pregnant || '无' }}</el-col>
                </el-row>
                <el-row :gutter="8" class="compact-row">
                  <el-col :span="24"><span class="label">过敏信息：</span>{{ currentPrescription.allInfo || '无' }}</el-col>
                </el-row>
              </div>

              <!-- 诊断信息 -->
              <div class="info-group">
                <h4 class="group-title">诊断信息</h4>
                <el-row :gutter="8" class="compact-row">
                  <el-col :span="12"><span class="label">诊断信息：</span>{{ currentPrescription.diaInfo || '无' }}</el-col>
                  <el-col :span="12"><span class="label">用药理由：</span>{{ currentPrescription.reason || '无' }}</el-col>
                </el-row>
                <el-row :gutter="8" class="compact-row">
                  <el-col :span="12"><span class="label">中医主病：</span>{{ currentPrescription.zyzb || '无' }}</el-col>
                  <el-col :span="12"><span class="label">中医主症：</span>{{ currentPrescription.zyzz || '无' }}</el-col>
                </el-row>
              </div>
            </div>

            <!-- 药品明细 -->
            <div class="info-section">
              <h4 class="section-title">药品明细</h4>
              <el-table :data="medicationList" size="mini" border max-height="200">
                <el-table-column label="药名" align="center" prop="medName" min-width="120" show-overflow-tooltip />
                <el-table-column label="组号" align="center" prop="group" width="50" />
                <el-table-column label="规格" align="center" prop="spec" width="80" show-overflow-tooltip />
                <el-table-column label="给药途径" align="center" prop="administer" width="80" />
                <el-table-column label="单次量" align="center" width="100">
                  <template slot-scope="scope">
                    {{ (Number(scope.row.dose) || 0).toFixed(2) }}{{ scope.row.doseUnit }}
                  </template>
                </el-table-column>
                <el-table-column label="频次" align="center" prop="freq" width="50" />
                <el-table-column label="天数" align="center" prop="days" width="50" />
                <el-table-column label="开药数量" align="center" width="80">
                  <template slot-scope="scope">
                    {{ (Number(scope.row.ordQty) || 0).toFixed(2) }}{{ scope.row.ordUom }}
                  </template>
                </el-table-column>
                <el-table-column label="金额" align="center" prop="money" width="80">
                  <template slot-scope="scope">
                    {{ (Number(scope.row.money) || 0).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column label="用药说明" align="center" prop="yysm" min-width="100" show-overflow-tooltip />
              </el-table>
            </div>

            <!-- 处方分析结果 -->
            <div class="info-section">
              <h4 class="section-title">处方分析结果</h4>
              <el-tabs v-model="activeTab" type="card" class="compact-tabs">
                <el-tab-pane name="important">
                  <span slot="label">
                    重要问题 <el-badge :value="getAnalysisResultsByLevel('重要').length" v-if="getAnalysisResultsByLevel('重要').length > 0" />
                  </span>
                  <el-table :data="getAnalysisResultsByLevel('重要')" size="mini" max-height="150">
                    <el-table-column label="药物A" prop="ywa" width="100" show-overflow-tooltip />
                    <el-table-column label="药物B" prop="ywb" width="100" show-overflow-tooltip />
                    <el-table-column label="问题名称" prop="wtname" width="120" show-overflow-tooltip />
                    <el-table-column label="标题" prop="title" min-width="120" show-overflow-tooltip />
                    <el-table-column label="详情" prop="detail" min-width="150" show-overflow-tooltip />
                  </el-table>
                </el-tab-pane>
                <el-tab-pane name="general">
                  <span slot="label">
                    一般问题 <el-badge :value="getAnalysisResultsByLevel('一般').length" v-if="getAnalysisResultsByLevel('一般').length > 0" />
                  </span>
                  <el-table :data="getAnalysisResultsByLevel('一般')" size="mini" max-height="150">
                    <el-table-column label="药物A" prop="ywa" width="100" show-overflow-tooltip />
                    <el-table-column label="药物B" prop="ywb" width="100" show-overflow-tooltip />
                    <el-table-column label="问题名称" prop="wtname" width="120" show-overflow-tooltip />
                    <el-table-column label="标题" prop="title" min-width="120" show-overflow-tooltip />
                    <el-table-column label="详情" prop="detail" min-width="150" show-overflow-tooltip />
                  </el-table>
                </el-tab-pane>
                <el-tab-pane name="other">
                  <span slot="label">
                    其它问题 <el-badge :value="getAnalysisResultsByLevel('其它').length" v-if="getAnalysisResultsByLevel('其它').length > 0" />
                  </span>
                  <el-table :data="getAnalysisResultsByLevel('其它')" size="mini" max-height="150">
                    <el-table-column label="药物A" prop="ywa" width="100" show-overflow-tooltip />
                    <el-table-column label="药物B" prop="ywb" width="100" show-overflow-tooltip />
                    <el-table-column label="问题名称" prop="wtname" width="120" show-overflow-tooltip />
                    <el-table-column label="标题" prop="title" min-width="120" show-overflow-tooltip />
                    <el-table-column label="详情" prop="detail" min-width="150" show-overflow-tooltip />
                  </el-table>
                </el-tab-pane>
              </el-tabs>
            </div>

            <!-- 历史审核记录 -->
            <div class="info-section">
              <h4 class="section-title">历史审核记录</h4>
              <el-table :data="reviewHistory" size="mini" border max-height="120">
                <el-table-column label="审核医师" prop="nickName" width="80" />
                <el-table-column label="审核时间" prop="createTime" width="130">
                  <template slot-scope="scope">
                    {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}
                  </template>
                </el-table-column>
                <el-table-column label="审核意见" prop="text" min-width="150" show-overflow-tooltip />
              </el-table>
            </div>
          </div>
        </el-card>
        <el-card v-else class="detail-card">
          <div class="text-center" style="padding: 50px 0; color: #999;">
            请选择处方查看详情
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 固定在底部的审核操作区域 -->
    <div class="review-actions-fixed">
      <el-card class="actions-card">
        <el-row :gutter="15" align="middle" type="flex">
          <el-col :span="8">
            <el-select
              v-model="selectedProblems"
              multiple
              placeholder="选择问题类型"
              style="width: 100%"
              size="small"
              collapse-tags>
              <el-option
                v-for="problem in problemTypes"
                :key="problem.cfwtbh"
                :label="problem.cfwtbh + ' ' +problem.cfwtname"
                :value="problem.cfwtname">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button
              type="danger"
              icon="el-icon-close"
              size="small"
              :disabled="multiple"
              @click="handleReject"
              v-hasPermi="['rms:prescription:review:reject']"
              style="width: 100%">
              审核打回
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button
              type="success"
              icon="el-icon-check"
              size="small"
              :disabled="multiple"
              @click="handleApprove"
              v-hasPermi="['rms:prescription:review:approve']"
              style="width: 100%">
              审核通过
            </el-button>
          </el-col>
          <el-col :span="8" class="text-right">
            <span class="selection-info">
              已选择 <strong>{{ ids.length }}</strong> 条处方
              <el-button
                type="text"
                size="mini"
                @click="clearSelection"
                v-if="ids.length > 0">
                清空选择
              </el-button>
            </span>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script>
import {
  getPendingPrescriptions,
  getPrescriptionDetail,
  getProblemTypes,
  getRefreshTime,
  approvePrescriptions,
  rejectPrescriptions,
  getDepartments
} from "@/api/rms/prescriptionreview"

export default {
  name: "PrescriptionReview",
  data() {
    return {
      // 加载状态
      loading: true,
      // 选中数组
      ids: [],
      // 选中的处方对象数组（用于状态持久化）
      selectedPrescriptions: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 处方列表
      prescriptionList: [],
      // 当前选中的处方
      currentPrescription: null,
      // 药品明细列表
      medicationList: [],
      // 分析结果列表
      analysisResults: [],
      // 审核历史列表
      reviewHistory: [],
      // 科室列表
      departmentList: [],
      // 问题类型列表
      problemTypes: [],
      // 选中的问题类型
      selectedProblems: [],
      // 自动刷新开关
      autoRefresh: false,
      // 刷新定时器
      refreshTimer: null,
      // 刷新间隔（毫秒）
      refreshInterval: 5000,
      // 是否由自动刷新触发的标记
      isAutoRefreshTriggered: false,
      // 页面不可见前是否开启了自动刷新
      wasAutoRefreshActive: false,
      // 是否正在恢复选择状态（用于阻止意外的选择变化事件）
      isRestoringSelection: false,
      // 当前活跃的标签页
      activeTab: 'important',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        deptCode: null,
        prescriptionType: '',
        keyword: null
      }
    }
  },
  computed: {
    /** 计算表格高度 */
    tableHeight() {
      // 100vh - 顶部导航(50px) - 面包屑(40px) - 搜索区域(80px) - 卡片头部(60px) - 分页区域(50px) - 底部审核区域(80px) - 边距(40px)
      return 'calc(100vh - 350px)'
    },
    /** 计算详情区域高度 */
    detailHeight() {
      // 与表格高度保持一致，减去卡片头部
      return 'calc(100vh - 340px)'
    }
  },
  created() {
    this.getList()
    this.getDepartmentList()
    this.getProblemTypeList()
    this.getRefreshConfig()
    this.loadSelectedState()
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
    // 监听侧边栏状态变化
    this.checkSidebarStatus()
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange)
  },
  methods: {
    /** 查询处方列表 */
    getList() {
      console.log('🔄 [数据加载] 开始加载处方列表', {
        是否自动刷新: this.autoRefresh,
        当前页码: this.queryParams.pageNum,
        页面大小: this.queryParams.pageSize,
        加载时间: new Date().toLocaleTimeString(),
        当前选择状态: this.ids
      })

      // 在数据加载前保存当前的选择状态
      const savedIds = [...this.ids]
      console.log('💾 [状态备份] 备份当前选择状态', { 备份的ids: savedIds })

      this.loading = true
      // 设置恢复状态标记，防止表格重新渲染时清空选择
      this.isRestoringSelection = true

      // 构建查询参数
      let params = { ...this.queryParams }

      // 处理关键字搜索
      if (params.keyword) {
        params.doctName = params.keyword
        params.name = params.keyword
      }

      getPendingPrescriptions(params).then(response => {
        console.log('📊 [数据加载] 处方列表加载完成', {
          数据条数: response.rows.length,
          总数: response.total,
          处方编码列表: response.rows.map(item => item.code)
        })

        this.prescriptionList = response.rows
        this.total = response.total
        this.loading = false

        // 恢复备份的选择状态
        this.ids = savedIds

        console.log('🔄 [状态恢复] 准备恢复选择状态', {
          备份的ids: savedIds,
          当前ids: this.ids,
          ids数量: this.ids.length,
          表格ref存在: !!this.$refs.prescriptionTable,
          是否自动刷新触发: this.isAutoRefreshTriggered
        })

        // 根据是否自动刷新使用不同的恢复策略
        if (this.isAutoRefreshTriggered) {
          console.log('🔄 [自动刷新] 使用自动刷新专用恢复机制')
          this.handleAutoRefreshRestore()
          this.isAutoRefreshTriggered = false // 重置标记
        } else {
          console.log('👆 [手动操作] 使用常规恢复机制')
          // 恢复选择状态 - 确保DOM完全更新后再恢复
          this.$nextTick(() => {
            console.log('⏰ [延迟恢复] $nextTick执行，准备延迟恢复状态')
            // 延迟一小段时间确保表格完全渲染
            setTimeout(() => {
              console.log('🎯 [开始恢复] 开始执行状态恢复')
              this.restoreSelectedState()
            }, 200) // 增加延迟时间到200ms
          })
        }
      }).catch(error => {
        this.loading = false
        this.isRestoringSelection = false // 出错时也要重置标记
        console.error('❌ [加载失败] 获取处方列表失败:', error)
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      console.log('🔄 [选择变化] 触发选择变化事件', {
        选中数量: selection.length,
        选中处方: selection.map(item => ({ code: item.code, name: item.name })),
        触发时间: new Date().toLocaleTimeString(),
        正在恢复状态: this.isRestoringSelection
      })

      // 如果正在恢复选择状态，忽略这次选择变化事件
      if (this.isRestoringSelection) {
        console.log('🚫 [忽略事件] 正在恢复选择状态，忽略此次选择变化事件')
        return
      }

      this.ids = selection.map(item => item.code)
      this.selectedPrescriptions = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length

      console.log('💾 [状态更新] 更新组件状态', {
        ids: this.ids,
        single: this.single,
        multiple: this.multiple
      })

      // 保存选择状态到本地存储
      this.saveSelectedState()
    },

    /** 行点击事件 */
    handleRowClick(row) {
      this.currentPrescription = row
      this.getPrescriptionDetailData(row.code)
    },

    /** 获取处方详情 */
    getPrescriptionDetailData(code) {
      getPrescriptionDetail(code).then(response => {
        this.currentPrescription = response.data.prescription
        this.medicationList = response.data.medications || []
        this.analysisResults = response.data.analysisResults || []
        this.reviewHistory = response.data.reviewHistory || []
      })
    },

    /** 获取科室列表 */
    getDepartmentList() {
      getDepartments().then(response => {
        this.departmentList = response.data || []
      })
    },

    /** 获取问题类型列表 */
    getProblemTypeList() {
      getProblemTypes().then(response => {
        this.problemTypes = response.data || []
      })
    },

    /** 获取刷新配置 */
    getRefreshConfig() {
      getRefreshTime().then(response => {
        this.refreshInterval = response.data || 5000
      })
    },

    /** 切换自动刷新 */
    toggleAutoRefresh(value) {
      console.log('🔄 [自动刷新] 切换自动刷新状态', {
        开启: value,
        刷新间隔: this.refreshInterval,
        当前选择数量: this.ids.length
      })

      if (value) {
        this.refreshTimer = setInterval(() => {
          console.log('⏰ [自动刷新] 自动刷新定时器触发', {
            触发时间: new Date().toLocaleTimeString(),
            当前选择状态: this.ids
          })

          // 标记这是自动刷新触发的数据加载
          this.isAutoRefreshTriggered = true
          this.getList()
        }, this.refreshInterval)
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
      }
    },

    /** 审核通过 */
    handleApprove() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要审核的处方")
        return
      }

      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {
        return approvePrescriptions(this.ids)
      }).then(() => {
        this.getList()
        this.clearSelection()
        this.$modal.msgSuccess("审核通过成功")
      }).catch(() => {})
    },

    /** 审核打回 */
    handleReject() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要打回的处方")
        return
      }

      if (this.selectedProblems.length === 0) {
        this.$modal.msgError("请选择问题类型")
        return
      }

      this.$modal.confirm('是否确认打回选中的处方？').then(() => {
        const data = {
          codes: this.ids,
          problemNames: this.selectedProblems
        }
        return rejectPrescriptions(data)
      }).then(() => {
        this.getList()
        this.selectedProblems = []
        this.clearSelection()
        this.$modal.msgSuccess("审核打回成功")
      }).catch(() => {})
    },

    /** 根据问题等级获取分析结果 */
    getAnalysisResultsByLevel(level) {
      return this.analysisResults.filter(item => item.wtlvl === level)
    },

    /** 获取严重程度类型 */
    getSeverityType(level) {
      switch(level) {
        case '重要': return 'danger'
        case '一般': return 'warning'
        case '其它': return 'info'
        default: return 'info'
      }
    },

    /** 获取严重程度文本 */
    getSeverityText(level) {
      return level || '未知'
    },

    /** 计算年龄 */
    calculateAge(birthDate) {
      if (!birthDate) return '未知'
      const birth = new Date(birthDate)
      const now = new Date()
      const age = now.getFullYear() - birth.getFullYear()
      return age + '岁'
    },

    /** 保存选择状态到本地存储 */
    saveSelectedState() {
      const selectedCodes = this.ids
      console.log('💾 [保存状态] 保存选择状态到localStorage', {
        选中的处方编码: selectedCodes,
        数量: selectedCodes.length,
        保存时间: new Date().toLocaleTimeString()
      })

      localStorage.setItem('prescription_review_selected', JSON.stringify(selectedCodes))

      // 验证保存是否成功
      const saved = localStorage.getItem('prescription_review_selected')
      console.log('✅ [保存验证] localStorage保存结果', {
        保存的数据: saved,
        解析后: JSON.parse(saved || '[]')
      })
    },

    /** 从本地存储加载选择状态 */
    loadSelectedState() {
      console.log('📖 [加载状态] 开始从localStorage加载选择状态')

      try {
        const savedSelection = localStorage.getItem('prescription_review_selected')
        console.log('📖 [加载状态] localStorage中的数据', {
          原始数据: savedSelection,
          数据类型: typeof savedSelection
        })

        if (savedSelection) {
          const parsedIds = JSON.parse(savedSelection)
          this.ids = parsedIds
          this.multiple = this.ids.length === 0
          this.single = this.ids.length !== 1

          console.log('✅ [加载成功] 状态加载完成', {
            加载的ids: this.ids,
            数量: this.ids.length,
            multiple: this.multiple,
            single: this.single
          })
        } else {
          console.log('ℹ️ [加载状态] localStorage中没有保存的选择状态')
          this.ids = []
        }
      } catch (error) {
        console.error('❌ [加载失败] 加载选择状态失败:', error)
        this.ids = []
      }
    },

    /** 恢复表格选择状态 */
    restoreSelectedState() {
      console.log('🎯 [状态恢复] 开始恢复表格选择状态', {
        需要恢复的ids: this.ids,
        ids数量: this.ids.length,
        表格ref存在: !!this.$refs.prescriptionTable,
        处方列表长度: this.prescriptionList.length,
        处方列表编码: this.prescriptionList.map(item => item.code)
      })

      // 检查前置条件
      if (this.ids.length === 0) {
        console.log('ℹ️ [状态恢复] 没有需要恢复的选择状态')
        return
      }

      if (!this.$refs.prescriptionTable) {
        console.error('❌ [状态恢复] 表格ref不存在')
        return
      }

      if (this.prescriptionList.length === 0) {
        console.log('ℹ️ [状态恢复] 处方列表为空，无法恢复状态')
        return
      }

      try {
        // 清除当前选择
        console.log('🧹 [清除选择] 清除表格当前选择状态')
        this.$refs.prescriptionTable.clearSelection()

        // 恢复选择状态
        let restoredCount = 0
        let matchedRows = []
        let unmatchedIds = []

        this.prescriptionList.forEach(row => {
          if (this.ids.includes(row.code)) {
            console.log(`✅ [匹配成功] 找到匹配的处方: ${row.code} - ${row.name}`)
            this.$refs.prescriptionTable.toggleRowSelection(row, true)
            restoredCount++
            matchedRows.push({ code: row.code, name: row.name })
          }
        })

        // 检查未匹配的ID
        this.ids.forEach(id => {
          if (!this.prescriptionList.find(row => row.code === id)) {
            unmatchedIds.push(id)
          }
        })

        // 更新选择状态统计
        this.multiple = this.ids.length === 0
        this.single = this.ids.length !== 1

        console.log('📊 [恢复结果] 状态恢复完成', {
          恢复成功数量: restoredCount,
          匹配的处方: matchedRows,
          未匹配的ID: unmatchedIds,
          最终状态: {
            multiple: this.multiple,
            single: this.single,
            ids: this.ids
          }
        })

        // 验证恢复结果
        this.$nextTick(() => {
          const selectedRows = this.$refs.prescriptionTable.selection || []
          console.log('🔍 [恢复验证] 验证表格选择状态', {
            表格选中行数: selectedRows.length,
            表格选中编码: selectedRows.map(row => row.code),
            期望选中数量: this.ids.length,
            状态一致: selectedRows.length === restoredCount
          })

          if (selectedRows.length !== restoredCount) {
            console.warn('⚠️ [状态不一致] 表格选择状态与预期不符，尝试重新恢复')
            // 如果状态不一致，再次尝试恢复
            setTimeout(() => {
              this.restoreSelectedStateRetry()
            }, 100)
          } else {
            // 恢复成功，重置恢复状态标记
            console.log('✅ [恢复完成] 状态恢复成功，重置恢复标记')
            this.isRestoringSelection = false
          }
        })

      } catch (error) {
        console.error('❌ [恢复失败] 恢复选择状态失败:', error)
      }
    },

    /** 清空选择 */
    clearSelection() {
      // 设置恢复标记，防止clearSelection触发选择变化事件
      this.isRestoringSelection = true

      this.ids = []
      this.selectedPrescriptions = []
      this.multiple = true
      this.single = true

      // 清除表格选择
      if (this.$refs.prescriptionTable) {
        this.$refs.prescriptionTable.clearSelection()
      }

      // 清除本地存储
      localStorage.removeItem('prescription_review_selected')

      console.log('🧹 [清空选择] 选择状态已清空', {
        清空时间: new Date().toLocaleTimeString()
      })

      // 重置恢复标记
      setTimeout(() => {
        this.isRestoringSelection = false
      }, 100)
    },

    /** 重试恢复选择状态 */
    restoreSelectedStateRetry() {
      console.log('🔄 [重试恢复] 开始重试恢复选择状态')

      if (!this.$refs.prescriptionTable) {
        console.error('❌ [重试失败] 表格ref仍然不存在')
        this.isRestoringSelection = false
        return
      }

      // 强制清除选择
      this.$refs.prescriptionTable.clearSelection()

      // 重新恢复
      let retryCount = 0
      this.prescriptionList.forEach(row => {
        if (this.ids.includes(row.code)) {
          this.$refs.prescriptionTable.toggleRowSelection(row, true)
          retryCount++
          console.log(`🔄 [重试恢复] 重新选择处方: ${row.code}`)
        }
      })

      console.log(`✅ [重试完成] 重试恢复了 ${retryCount} 个处方的选择状态`)

      // 重试完成后重置标记
      this.isRestoringSelection = false
    },

    /** 自动刷新时的特殊处理 */
    handleAutoRefreshRestore() {
      console.log('🔄 [自动刷新] 自动刷新触发，特殊处理选择状态恢复')

      // 在自动刷新时，给更多时间让表格完全渲染
      this.$nextTick(() => {
        setTimeout(() => {
          console.log('🎯 [自动刷新恢复] 开始自动刷新后的状态恢复')
          this.restoreSelectedState()

          // 额外验证
          setTimeout(() => {
            const selectedRows = this.$refs.prescriptionTable?.selection || []
            if (selectedRows.length === 0 && this.ids.length > 0) {
              console.warn('⚠️ [自动刷新] 第一次恢复失败，进行第二次尝试')
              this.restoreSelectedStateRetry()
            } else {
              // 自动刷新恢复成功，重置标记
              console.log('✅ [自动刷新] 自动刷新恢复成功，重置恢复标记')
              this.isRestoringSelection = false
            }
          }, 300)
        }, 300) // 自动刷新时使用更长的延迟
      })
    },

    /** 处理窗口大小变化 */
    handleResize() {
      // 窗口大小变化时，强制重新计算表格高度
      this.$nextTick(() => {
        if (this.$refs.prescriptionTable) {
          this.$refs.prescriptionTable.doLayout()
        }
      })
    },

    /** 检查侧边栏状态 */
    checkSidebarStatus() {
      // 检查body是否有hideSidebar类
      const body = document.body
      if (body.classList.contains('hideSidebar')) {
        // 侧边栏已收起
        this.updateFixedAreaPosition(true)
      } else {
        // 侧边栏展开
        this.updateFixedAreaPosition(false)
      }

      // 监听侧边栏状态变化
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const isHidden = body.classList.contains('hideSidebar')
            this.updateFixedAreaPosition(isHidden)
          }
        })
      })

      observer.observe(body, {
        attributes: true,
        attributeFilter: ['class']
      })

      // 保存observer引用以便清理
      this.sidebarObserver = observer
    },

    /** 更新固定区域位置 */
    updateFixedAreaPosition(isHidden) {
      const fixedArea = document.querySelector('.review-actions-fixed')
      if (fixedArea) {
        if (isHidden) {
          fixedArea.style.left = '54px'
          fixedArea.style.maxWidth = 'calc(100vw - 54px)'
        } else {
          fixedArea.style.left = '200px'
          fixedArea.style.maxWidth = 'calc(100vw - 200px)'
        }
      }
    },

    /** 处理页面可见性变化 */
    handleVisibilityChange() {
      if (document.hidden) {
        console.log('👁️ [页面状态] 页面变为不可见，暂停自动刷新')
        // 页面不可见时暂停自动刷新
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
          this.wasAutoRefreshActive = this.autoRefresh
        }
      } else {
        console.log('👁️ [页面状态] 页面变为可见，恢复自动刷新')
        // 页面可见时恢复自动刷新
        if (this.wasAutoRefreshActive && this.autoRefresh) {
          this.toggleAutoRefresh(true)
        }

        // 页面重新可见时，如果有选择状态需要恢复，立即恢复
        if (this.ids.length > 0) {
          console.log('🔄 [页面可见] 页面重新可见，检查选择状态')
          setTimeout(() => {
            this.restoreSelectedState()
          }, 100)
        }
      }
    }
  },
  beforeDestroy() {
    console.log('🧹 [组件销毁] 开始清理组件资源')

    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      console.log('🧹 [组件销毁] 清理自动刷新定时器')
    }

    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize)
    document.removeEventListener('visibilitychange', this.handleVisibilityChange)
    console.log('🧹 [组件销毁] 清理事件监听器')

    // 清理侧边栏观察器
    if (this.sidebarObserver) {
      this.sidebarObserver.disconnect()
      console.log('🧹 [组件销毁] 清理侧边栏观察器')
    }

    console.log('✅ [组件销毁] 组件资源清理完成')
  }
}
</script>

<style scoped>
/* 主要内容区域 */
.app-container {
  /*padding-bottom: 80px; !* 为固定的审核区域留出空间 *!*/
}

.main-content {
  height: calc(100vh - 230px); /* 顶部导航(50px) + 面包屑(40px) + 搜索区域(80px) + 底部审核区域(80px) - 一些边距，移除工具栏高度 */
}

/* 处方列表卡片 */
.list-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-card .el-card__body {
  padding: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pagination-container {
  margin-top: 10px;
  text-align: center;
  flex-shrink: 0;
}

/* 处方详情卡片 */
.detail-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-card .el-card__body {
  padding: 10px;
  flex: 1;
  overflow: hidden;
}

.detail-content {
  padding-right: 5px;
}

/* 统一信息区域样式 */
.unified-info-section {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 12px;
  background-color: #fafafa;
  margin-bottom: 12px;
}

.info-group {
  margin-bottom: 12px;
}

.info-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 8px 0;
  color: #303133;
  font-weight: 500;
  font-size: 13px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 4px;
}

.compact-row {
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.3;
}

.compact-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #606266;
  margin-right: 4px;
}

/* 保留原有信息区域样式以兼容其他部分 */
.info-section {
  margin-bottom: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.section-title {
  margin: 0 0 8px 0;
  color: #303133;
  font-weight: 500;
  font-size: 13px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 4px;
}

.info-row {
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.3;
}

/* 紧凑的标签页 */
.compact-tabs .el-tabs__header {
  margin-bottom: 10px;
}

.compact-tabs .el-tab-pane {
  padding: 0;
}

/* 固定在底部的审核操作区域 */
.review-actions-fixed {
  position: fixed;
  bottom: 0;
  left: 200px; /* 侧边栏宽度 */
  right: 0;
  z-index: 1000;
  background: linear-gradient(to top, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  backdrop-filter: blur(5px);
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: calc(100vw - 200px); /* 限制最大宽度，减去侧边栏宽度 */
}

/* 当侧边栏收起时的适配 */
.hideSidebar .review-actions-fixed {
  left: 54px; /* 收起后的侧边栏宽度 */
  max-width: calc(100vw - 54px);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .review-actions-fixed {
    left: 0;
    right: 0;
    max-width: 100vw;
  }
}

.actions-card {
  margin: 0;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.actions-card .el-card__body {
  padding: 15px 20px;
}

.selection-info {
  color: #606266;
  font-size: 14px;
}

.selection-info strong {
  color: #409eff;
}

/* 头部信息样式 */
.header-info {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content .el-col:first-child {
    margin-bottom: 20px;
  }

  .info-row .el-col {
    margin-bottom: 5px;
  }

  /* 调整小屏幕下的高度计算 */
  .main-content {
    height: calc(100vh - 280px);
  }
}

@media (max-width: 768px) {
  .main-content {
    height: calc(100vh - 320px);
  }

  .review-actions-fixed .el-row .el-col {
    margin-bottom: 10px;
  }

  .actions-card .el-card__body {
    padding: 10px 15px;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1920px) {
  .main-content {
    height: calc(100vh - 220px);
  }
}

/* 通用样式 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-table {
  margin-bottom: 5px;
}

/* 表格优化 */
.el-table .cell {
  padding: 0 5px;
}

.el-table--mini td {
  padding: 4px 0;
}

/* 徽章样式 */
.el-badge {
  margin-left: 5px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
