<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="科室" prop="deptCode">
        <el-select v-model="queryParams.deptCode" placeholder="请选择科室" clearable style="width: 200px">
          <el-option
            v-for="dept in departmentList"
            :key="dept.deptCode"
            :label="dept.deptName"
            :value="dept.deptCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="药品类型" prop="prescriptionType">
        <el-radio-group v-model="queryParams.prescriptionType">
          <el-radio label="">全部</el-radio>
          <el-radio label="1">西药</el-radio>
          <el-radio label="2">中药(草药)</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="关键字" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="医生姓名或患者姓名"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-select v-model="selectedProblems" multiple placeholder="选择问题类型" style="width: 300px" size="mini">
          <el-option
            v-for="problem in problemTypes"
            :key="problem.cfwtbh"
            :label="problem.cfwtname"
            :value="problem.cfwtname">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-close"
          size="mini"
          :disabled="multiple"
          @click="handleReject"
          v-hasPermi="['rms:prescription:review:reject']"
        >审核打回</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleApprove"
          v-hasPermi="['rms:prescription:review:approve']"
        >审核通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
          @change="toggleAutoRefresh">
        </el-switch>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 处方列表 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-table
          v-loading="loading"
          :data="prescriptionList"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          highlight-current-row
          height="600">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="科室" align="center" prop="deptName" width="120" />
          <el-table-column label="医生姓名" align="center" prop="doctName" width="100" />
          <el-table-column label="患者姓名" align="center" prop="name" width="100" />
          <el-table-column label="严重程度" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="getSeverityType(scope.row.level)" size="mini">
                {{ getSeverityText(scope.row.level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="处方时间" align="center" prop="presTime" width="120">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.presTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>

      <!-- 处方详情 -->
      <el-col :span="12">
        <el-card class="box-card" v-if="currentPrescription">
          <div slot="header" class="clearfix">
            <span>处方详情</span>
          </div>

          <!-- 基本信息 -->
          <el-descriptions title="处方信息" :column="2" size="small" border>
            <el-descriptions-item label="处方号">{{ currentPrescription.presId }}</el-descriptions-item>
            <el-descriptions-item label="金额">{{ currentPrescription.money || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="就诊号">{{ currentPrescription.treatCode }}</el-descriptions-item>
            <el-descriptions-item label="就诊日期">{{ parseTime(currentPrescription.hisTime, '{y}-{m}-{d}') }}</el-descriptions-item>
            <el-descriptions-item label="处方说明" :span="2">{{ currentPrescription.presSm || '无' }}</el-descriptions-item>
            <el-descriptions-item label="服用方法" :span="2">{{ currentPrescription.requir || '无' }}</el-descriptions-item>
          </el-descriptions>

          <el-descriptions title="诊断信息" :column="2" size="small" border style="margin-top: 20px">
            <el-descriptions-item label="诊断信息" :span="2">{{ currentPrescription.diaInfo || '无' }}</el-descriptions-item>
            <el-descriptions-item label="中医主病">{{ currentPrescription.zyzb || '无' }}</el-descriptions-item>
            <el-descriptions-item label="中医主症">{{ currentPrescription.zyzz || '无' }}</el-descriptions-item>
            <el-descriptions-item label="用药理由" :span="2">{{ currentPrescription.reason || '无' }}</el-descriptions-item>
          </el-descriptions>

          <el-descriptions title="医生信息" :column="2" size="small" border style="margin-top: 20px">
            <el-descriptions-item label="科室">{{ currentPrescription.deptName }}</el-descriptions-item>
            <el-descriptions-item label="医生姓名">{{ currentPrescription.doctName }}</el-descriptions-item>
          </el-descriptions>

          <el-descriptions title="患者信息" :column="2" size="small" border style="margin-top: 20px">
            <el-descriptions-item label="患者姓名">{{ currentPrescription.name }}</el-descriptions-item>
            <el-descriptions-item label="出生日期">{{ parseTime(currentPrescription.birth, '{y}-{m}-{d}') }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ currentPrescription.sex }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ calculateAge(currentPrescription.birth) }}</el-descriptions-item>
            <el-descriptions-item label="身高">{{ currentPrescription.height || '未知' }}cm</el-descriptions-item>
            <el-descriptions-item label="体重">{{ currentPrescription.weight || '未知' }}kg</el-descriptions-item>
            <el-descriptions-item label="孕周">{{ currentPrescription.pregnant || '无' }}</el-descriptions-item>
            <el-descriptions-item label="过敏信息" :span="2">{{ currentPrescription.allInfo || '无' }}</el-descriptions-item>
          </el-descriptions>

          <!-- 药品明细 -->
          <div style="margin-top: 20px">
            <h4>药品明细</h4>
            <el-table :data="medicationList" size="small" border>
              <el-table-column label="药名" prop="medName" />
              <el-table-column label="组号" prop="group" width="60" />
              <el-table-column label="规格" prop="spec" width="100" />
              <el-table-column label="给药途径" prop="administer" width="80" />
              <el-table-column label="单次量" width="80">
                <template slot-scope="scope">
                  {{ scope.row.dose }}{{ scope.row.doseUnit }}
                </template>
              </el-table-column>
              <el-table-column label="频次" prop="freq" width="60" />
              <el-table-column label="天数" prop="days" width="60" />
              <el-table-column label="开药数量" width="80">
                <template slot-scope="scope">
                  {{ scope.row.ordQty }}{{ scope.row.ordUom }}
                </template>
              </el-table-column>
              <el-table-column label="金额" prop="money" width="80" />
              <el-table-column label="用药说明" prop="yysm" />
            </el-table>
          </div>

          <!-- 处方分析结果 -->
          <div style="margin-top: 20px">
            <h4>处方分析结果</h4>
            <el-tabs v-model="activeTab">
              <el-tab-pane label="重要问题" name="important">
                <el-table :data="getAnalysisResultsByLevel('重要')" size="small">
                  <el-table-column label="药物A" prop="ywa" width="120" />
                  <el-table-column label="药物B" prop="ywb" width="120" />
                  <el-table-column label="问题名称" prop="wtname" />
                  <el-table-column label="标题" prop="title" />
                  <el-table-column label="详情" prop="detail" show-overflow-tooltip />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="一般问题" name="general">
                <el-table :data="getAnalysisResultsByLevel('一般')" size="small">
                  <el-table-column label="药物A" prop="ywa" width="120" />
                  <el-table-column label="药物B" prop="ywb" width="120" />
                  <el-table-column label="问题名称" prop="wtname" />
                  <el-table-column label="标题" prop="title" />
                  <el-table-column label="详情" prop="detail" show-overflow-tooltip />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="其它问题" name="other">
                <el-table :data="getAnalysisResultsByLevel('其它')" size="small">
                  <el-table-column label="药物A" prop="ywa" width="120" />
                  <el-table-column label="药物B" prop="ywb" width="120" />
                  <el-table-column label="问题名称" prop="wtname" />
                  <el-table-column label="标题" prop="title" />
                  <el-table-column label="详情" prop="detail" show-overflow-tooltip />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 历史审核记录 -->
          <div style="margin-top: 20px">
            <h4>历史审核记录</h4>
            <el-table :data="reviewHistory" size="small" border>
              <el-table-column label="审核医师" prop="nickName" width="100" />
              <el-table-column label="审核时间" prop="createTime" width="150">
                <template slot-scope="scope">
                  {{ parseTime(scope.row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column label="审核意见" prop="text" />
            </el-table>
          </div>
        </el-card>
        <el-card v-else class="box-card">
          <div class="text-center" style="padding: 50px 0; color: #999;">
            请选择处方查看详情
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getPendingPrescriptions,
  getPrescriptionDetail,
  getProblemTypes,
  getRefreshTime,
  approvePrescriptions,
  rejectPrescriptions,
  getDepartments
} from "@/api/rms/prescriptionreview"

export default {
  name: "PrescriptionReview",
  data() {
    return {
      // 加载状态
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 处方列表
      prescriptionList: [],
      // 当前选中的处方
      currentPrescription: null,
      // 药品明细列表
      medicationList: [],
      // 分析结果列表
      analysisResults: [],
      // 审核历史列表
      reviewHistory: [],
      // 科室列表
      departmentList: [],
      // 问题类型列表
      problemTypes: [],
      // 选中的问题类型
      selectedProblems: [],
      // 自动刷新开关
      autoRefresh: false,
      // 刷新定时器
      refreshTimer: null,
      // 刷新间隔（毫秒）
      refreshInterval: 5000,
      // 当前活跃的标签页
      activeTab: 'important',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        deptCode: null,
        prescriptionType: '',
        keyword: null
      }
    }
  },
  created() {
    this.getList()
    this.getDepartmentList()
    this.getProblemTypeList()
    this.getRefreshConfig()
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },
  methods: {
    /** 查询处方列表 */
    getList() {
      this.loading = true
      // 构建查询参数
      let params = { ...this.queryParams }

      // 处理关键字搜索
      if (params.keyword) {
        params.doctName = params.keyword
        params.name = params.keyword
      }

      getPendingPrescriptions(params).then(response => {
        this.prescriptionList = response.rows
        this.total = response.total
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.code)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 行点击事件 */
    handleRowClick(row) {
      this.currentPrescription = row
      this.getPrescriptionDetailData(row.code)
    },

    /** 获取处方详情 */
    getPrescriptionDetailData(code) {
      getPrescriptionDetail(code).then(response => {
        this.currentPrescription = response.data.prescription
        this.medicationList = response.data.medications || []
        this.analysisResults = response.data.analysisResults || []
        this.reviewHistory = response.data.reviewHistory || []
      })
    },

    /** 获取科室列表 */
    getDepartmentList() {
      getDepartments().then(response => {
        this.departmentList = response.data || []
      })
    },

    /** 获取问题类型列表 */
    getProblemTypeList() {
      getProblemTypes().then(response => {
        this.problemTypes = response.data || []
      })
    },

    /** 获取刷新配置 */
    getRefreshConfig() {
      getRefreshTime().then(response => {
        this.refreshInterval = response.data || 5000
      })
    },

    /** 切换自动刷新 */
    toggleAutoRefresh(value) {
      if (value) {
        this.refreshTimer = setInterval(() => {
          this.getList()
        }, this.refreshInterval)
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
      }
    },

    /** 审核通过 */
    handleApprove() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要审核的处方")
        return
      }

      this.$modal.confirm('是否确认审核通过选中的处方？').then(() => {
        return approvePrescriptions(this.ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("审核通过成功")
      }).catch(() => {})
    },

    /** 审核打回 */
    handleReject() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要打回的处方")
        return
      }

      if (this.selectedProblems.length === 0) {
        this.$modal.msgError("请选择问题类型")
        return
      }

      this.$modal.confirm('是否确认打回选中的处方？').then(() => {
        const data = {
          codes: this.ids,
          problemNames: this.selectedProblems
        }
        return rejectPrescriptions(data)
      }).then(() => {
        this.getList()
        this.selectedProblems = []
        this.$modal.msgSuccess("审核打回成功")
      }).catch(() => {})
    },

    /** 根据问题等级获取分析结果 */
    getAnalysisResultsByLevel(level) {
      return this.analysisResults.filter(item => item.wtlvl === level)
    },

    /** 获取严重程度类型 */
    getSeverityType(level) {
      switch(level) {
        case '重要': return 'danger'
        case '一般': return 'warning'
        case '其它': return 'info'
        default: return 'info'
      }
    },

    /** 获取严重程度文本 */
    getSeverityText(level) {
      return level || '未知'
    },

    /** 计算年龄 */
    calculateAge(birthDate) {
      if (!birthDate) return '未知'
      const birth = new Date(birthDate)
      const now = new Date()
      const age = now.getFullYear() - birth.getFullYear()
      return age + '岁'
    }
  }
}
</script>

<style scoped>
.box-card {
  height: 600px;
  overflow-y: auto;
}

.text-center {
  text-align: center;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-table {
  margin-bottom: 10px;
}

h4 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-weight: 500;
}
</style>